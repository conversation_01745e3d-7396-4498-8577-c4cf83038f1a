"""Integration tests module.

This module contains integration tests for the karpark application.

The module is organized as follows:
- registrar/: Core testing framework components
- fixtures/: Test data, configurations, and builders
- utils/: Utility functions for testing

Usage:
    from tests.integration.registrar.test_runner import test_karpark_integration
    test_karpark_integration()
"""

__all__ = []