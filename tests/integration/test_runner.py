#!/usr/bin/env python3
"""测试运行器模块

此模块使用原始的main.start()函数，通过Hook smart_sleep()注入时间管理。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import responses
from karpark.registrar.task_scheduler import TaskScheduler
from karpark.main import main

from tests.integration.fixtures.configs.scenarios import KARPARK_TEST_SCENARIO
from tests.integration.core.scenario_manager import ScenarioManager
from tests.integration.core.time_manager import test_time_manager
from karpark.common.config import AccessControl, TaskSchedulerConfig


def run_integration_test():
    """运行集成测试 - 使用原始main.start()和smart_sleep Hook"""
    print("=" * 80)
    print("🚀 Starting Karpark Integration Test")
    print("=" * 80)
    
    # 创建场景管理器
    manager = ScenarioManager(KARPARK_TEST_SCENARIO)
    
    # 显示测试时间配置
    config = KARPARK_TEST_SCENARIO.scenario_config
    print(f"📅 Test Timeline:")
    print(f"   Start Time:  {config.start_time.strftime('%H:%M:%S')}")
    print(f"   Close Time:  {config.close_at_time.strftime('%H:%M:%S')} (+{(config.close_at_time - config.start_time).total_seconds()}s)")
    print(f"   Delete Time: {config.delete_at_time.strftime('%H:%M:%S')} (+{(config.delete_at_time - config.start_time).total_seconds()}s)")
    print(f"   Reopen Time: {config.reopen_at_time.strftime('%H:%M:%S')} (+{(config.reopen_at_time - config.start_time).total_seconds()}s)")
    print(f"   Time Acceleration: {test_time_manager.time_acceleration}x")
    print()
    
    print(f"🗂️ Deletion Configuration:")
    print(f"   Total to Delete: {config.delete_total_size}")
    print(f"   Deletion Batches: {config.deletion_batch_count}")
    print(f"   Batch Size: {config.dynamic_batch_size}")
    print(f"   Last Batch Size: {config.last_batch_size}")
    print(f"   Deletion Interval: {config.deletion_interval_seconds}s")
    print()
    
    # 重置TaskScheduler单例状态
    TaskScheduler._instance = None
    TaskScheduler._initialized = False

    # 重置时间管理器
    manager.reset_time()

    try:
        # 启动responses模拟
        responses.start()
        try:
            # 设置模拟响应
            manager.setup_mock_responses()

            # 设置所有必要的模拟
            with (manager.setup_database_mock(),
                  manager.setup_smart_sleep_mock(),
                  manager.setup_smtp_mock(),
                  manager.setup_config_file_mock(),
                  manager.setup_mock_time_logging()):

                # 在mock配置生效后重新加载配置并显示状态
                AccessControl.load_from_config()

                # 显示优先级排序配置状态（现在显示的是mock配置）
                print(f"🔧 Priority Sorting Configuration (Mock):")
                print(f"   Enable Priority Sorting: {TaskSchedulerConfig.Control.enable_priority_sorting}")
                print(f"   Admin Users: {AccessControl.Users.admin_users}")
                print(f"   Privileged Users: {AccessControl.Users.privileged_users}")
                print(f"   Authorized Users: {AccessControl.Users.authorized_users}")
                print()
                
                # 启动主程序
                print("🎯 Starting original main program...")
                print()
                
                # 🎯 关键：直接使用原始的start()函数！
                # 传递空参数列表避免解析pytest参数
                exit_code = main([])
                
                print(f"\n✅ Program exited with code: {exit_code}")
                
        finally:
            responses.stop()
            responses.reset()
                
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n" + "=" * 80)
        print("🏁 Integration Test Completed")
        print("=" * 80)


def test_karpark_integration():
    """集成测试入口函数"""
    run_integration_test()


if __name__ == "__main__":
    test_karpark_integration()
