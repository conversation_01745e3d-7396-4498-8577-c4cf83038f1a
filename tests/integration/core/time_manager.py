#!/usr/bin/env python3
"""时间管理模块

此模块提供测试中的时间控制功能，包括时间模拟和时间相关的Mock。
"""

import logging
from datetime import datetime, timedelta
from unittest.mock import patch
from typing import Optional


class TestTimeManager:
    """测试时间管理器
    
    用于在测试中控制时间流逝，支持时间加速和时间Mock。
    """
    
    def __init__(self, start_time: Optional[datetime] = None):
        """初始化时间管理器
        
        Args:
            start_time: 测试开始时间，如果为None则使用当前时间
        """
        self.start_time = start_time or datetime.now()
        self.current_time = self.start_time
        self.time_acceleration = 1.0  # 时间加速倍数，1.0表示正常速度
        
    def get_current_time(self) -> datetime:
        """获取当前模拟时间"""
        return self.current_time
    
    def advance_time(self, seconds: float) -> None:
        """推进时间
        
        Args:
            seconds: 要推进的秒数
        """
        self.current_time += timedelta(seconds=seconds)
        
    def advance_time_by_loop(self, loop_duration_seconds: float = 1.0) -> None:
        """按循环推进时间
        
        Args:
            loop_duration_seconds: 每个循环的持续时间（秒）
        """
        self.advance_time(loop_duration_seconds * self.time_acceleration)
    
    def set_time_acceleration(self, acceleration: float) -> None:
        """设置时间加速倍数
        
        Args:
            acceleration: 加速倍数，大于1表示加速，小于1表示减速
        """
        self.time_acceleration = acceleration
    
    def reset_time(self, new_start_time: Optional[datetime] = None) -> None:
        """重置时间
        
        Args:
            new_start_time: 新的开始时间，如果为None则使用当前时间
        """
        self.start_time = new_start_time or datetime.now()
        self.current_time = self.start_time
    

    
    def get_elapsed_time(self) -> timedelta:
        """获取从开始到现在的经过时间"""
        return self.current_time - self.start_time
    
    def format_current_time(self) -> str:
        """格式化当前时间为字符串"""
        return self.current_time.strftime("%H:%M:%S")
    
    def is_time_after(self, target_time: datetime) -> bool:
        """检查当前时间是否在目标时间之后"""
        return self.current_time >= target_time
    
    def is_time_before(self, target_time: datetime) -> bool:
        """检查当前时间是否在目标时间之前"""
        return self.current_time < target_time
    
    def is_time_between(self, start_time: datetime, end_time: datetime) -> bool:
        """检查当前时间是否在指定时间范围内"""
        return start_time <= self.current_time < end_time
    
    def get_time_until(self, target_time: datetime) -> timedelta:
        """获取到目标时间的剩余时间"""
        return target_time - self.current_time
    
    def get_time_since(self, reference_time: datetime) -> timedelta:
        """获取从参考时间到现在的经过时间"""
        return self.current_time - reference_time


def setup_mock_time_logging(time_manager):
    """设置模拟时间日志记录

    Args:
        time_manager: 时间管理器实例

    Returns:
        patch对象，用于在with语句中使用
    """
    # 保存原始的formatTime方法
    original_format_time = logging.Formatter.formatTime

    def mock_format_time(self, record, datefmt=None):
        """Mock的formatTime方法，使用模拟时间"""
        # 获取模拟时间
        mock_datetime = time_manager.get_current_time()

        # 如果指定了日期格式，使用指定格式
        if datefmt:
            return mock_datetime.strftime(datefmt)
        else:
            # 使用默认格式，包含毫秒
            return mock_datetime.strftime('%Y-%m-%d %H:%M:%S') + f',{mock_datetime.microsecond // 1000:03d}'

    # Mock logging.Formatter.formatTime方法
    return patch.object(logging.Formatter, 'formatTime', mock_format_time)


# 全局时间管理器实例
test_time_manager = TestTimeManager()


def get_current_time():
    """获取当前模拟时间

    Returns:
        datetime: 当前的模拟时间
    """
    return test_time_manager.get_current_time()


def setup_loguru_time_mock():
    """设置 Loguru 时间模拟

    使用 loguru 的 patch 方法来修改日志记录中的时间字段，
    确保日志显示模拟时间而不是真实时间。

    Returns:
        context manager: 用于在 with 语句中使用的上下文管理器
    """
    from loguru import logger
    from contextlib import contextmanager

    @contextmanager
    def loguru_time_patch():
        def patch_time(record):
            """时间 patch 函数：将日志记录中的时间替换为模拟时间"""
            original_time = record["time"]
            mock_time = test_time_manager.get_current_time()

            # 尝试保持原始时间对象的类型和时区信息
            if hasattr(original_time, 'replace'):
                try:
                    # 获取原始时间的时区信息
                    tzinfo = getattr(original_time, 'tzinfo', None)
                    if tzinfo is None:
                        import datetime as dt
                        tzinfo = dt.timezone.utc

                    # 创建新的时间对象，保持原始对象的类型和时区
                    new_time = original_time.replace(
                        year=mock_time.year,
                        month=mock_time.month,
                        day=mock_time.day,
                        hour=mock_time.hour,
                        minute=mock_time.minute,
                        second=mock_time.second,
                        microsecond=mock_time.microsecond,
                        tzinfo=tzinfo
                    )
                    record["time"] = new_time
                except Exception:
                    # 如果出现任何异常，回退到直接使用模拟时间
                    record["time"] = mock_time
            else:
                # 如果原始时间对象没有 replace 方法，直接使用模拟时间
                record["time"] = mock_time

        # 应用时间 patch，创建带有时间模拟的 logger 实例
        patched_logger = logger.patch(patch_time)

        # 保存原始的 logger 方法，以便稍后恢复
        original_methods = {}
        log_methods = ['trace', 'debug', 'info', 'success', 'warning', 'error', 'critical', 'exception', 'log']

        # 用 patched logger 的方法替换原始 logger 的方法
        for method_name in log_methods:
            original_methods[method_name] = getattr(logger, method_name)
            setattr(logger, method_name, getattr(patched_logger, method_name))

        yield

    return loguru_time_patch()
