#!/usr/bin/env python3
"""请求处理器模块

此模块负责处理HTTP请求并生成相应的响应。
"""
from karpark.registrar.entities.post_data import PostData

from tests.integration.fixtures.configs.scenarios import KarparkTestScenario
from tests.integration.fixtures.builders.response_builder import ResponseBuilder, RequestContext
from tests.integration.utils.http_utils import build_http_response, handle_request_error
from tests.integration.core.time_manager import test_time_manager
from tests.integration.utils.time_utils import create_timestamp

class RequestHandler:
    """请求处理器类
    
    负责处理GET和POST请求，生成相应的响应。
    """
    
    def __init__(self, scenario: KarparkTestScenario):
        """初始化请求处理器

        Args:
            scenario: 测试场景
        """
        self.scenario = scenario
    
    def create_get_callback(self) -> callable:
        """创建GET请求回调函数

        Returns:
            callable: GET请求回调函数
        """
        def get_callback(request):
            """处理GET请求的回调函数"""
            cookie, parker_item = self._get_server_managed_parker_from_request(request)
            if not parker_item:
                return handle_request_error(f"无效的cookie: {cookie}")

            # 更新所有parker的状态
            current_time = test_time_manager.get_current_time()
            self.scenario.update_all_parkers_positions(current_time)
            
            ctx = RequestContext(
                config=self.scenario.scenario_config,
                server_managed_parker_item=parker_item,
                current_time=current_time
            )
            
            resp_json = ResponseBuilder.create_generic_response(request, ctx)

            # 根据当前时间判断是否需要添加关闭错误
            resp_json = ResponseBuilder.apply_closed_error_if_needed(resp_json, ctx)

            return build_http_response(resp_json, cookie)
        
        return get_callback
    
    def create_post_callback(self) -> callable:
        """创建POST请求回调函数

        Returns:
            callable: POST请求回调函数
        """
        def post_callback(request):
            """处理POST请求的回调函数"""
            cookie, parker_item = self._get_server_managed_parker_from_request(request)
            if not parker_item:
                return handle_request_error(f"无效的cookie: {cookie}")

            post_data: PostData = PostData.from_request(request)
            current_plate_variant = post_data.car_no

            # 获取当前时间
            current_time = test_time_manager.get_current_time()

            ctx = RequestContext(
                config=self.scenario.scenario_config,
                server_managed_parker_item=parker_item,
                current_time=current_time
            )

            # 检查是否有重复车牌错误
            if ctx.config.should_return_duplicate_error(current_plate_variant):
                print(f"Parker {cookie} - POST请求处理完成，车牌号重复")
                resp_json = ResponseBuilder.create_registration_response(request, ctx)
                resp_json = ResponseBuilder.apply_duplicate_error_if_needed(resp_json, ctx, current_plate_variant)
            else:
                # 成功注册的情况

                # 更新parker_item的所有注册相关字段
                # 计算新注册的order：所有parkers的最大total + 1
                max_total = max((p.total for p in self.scenario.server_managed_parkers if p.total > 0), default=0)
                new_order = max_total + 1
                new_total = new_order  # 刚注册时，自己的order就是total

                # 更新parker_item的所有字段
                parker_item.car_no = post_data.car_no
                parker_item.location = post_data.location
                parker_item.order = new_order
                parker_item.total = new_total
                parker_item.status = post_data.status
                parker_item.register_time = create_timestamp(time_source='now')
                parker_item.time_stamp = create_timestamp(time_source='now')

                print(f"Parker {cookie} - POST请求处理完成，parker已重新注册 (order={new_order}, total={new_total})")

                # 更新所有parker的位置（基于时间的删除逻辑）
                self.scenario.update_all_parkers_positions(current_time)

                # # 模拟新注册对total的影响（增加total值）
                # # 不再需要，因为update_all_parkers_positions已经有取最大值作为total的逻辑
                # self._simulate_new_registration_effect_on_total()

                resp_json = ResponseBuilder.create_registration_response(request, ctx)
                resp_json = ResponseBuilder.apply_closed_error_if_needed(resp_json, ctx)

            return build_http_response(resp_json, cookie)

        return post_callback

    def _simulate_new_registration_effect_on_total(self) -> None:
        """模拟新注册对total的影响

        当有新的注册成功时，为所有非零total的parker增加total值，
        模拟其他人也在同时注册的效果。
        """
        for parker in self.scenario.server_managed_parkers:
            if parker.total > 0:  # 只对非零total的parker更新
                parker.total += 1  # 每次新注册增加1

    def _get_server_managed_parker_from_request(self, request) -> tuple:
        """从请求中获取服务器管理的parker项目
        
        Args:
            request: HTTP请求对象
            
        Returns:
            tuple: (cookie, parker_item) 元组
        """
        cookie = request.headers.get('Cookie', '')
        parker_item = self.scenario.get_server_managed_parker_by_cookie(cookie)
        return cookie, parker_item
