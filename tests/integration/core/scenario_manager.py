#!/usr/bin/env python3
"""场景管理器模块

此模块负责管理测试场景的生命周期。
"""
import responses
from unittest.mock import patch
from datetime import datetime

from karpark.registrar.db_operations import EnrollmentDBOperation
from tests.integration.fixtures.configs.scenarios import KarparkTestScenario
from tests.integration.core.request_handler import RequestHandler
from tests.integration.core.time_manager import test_time_manager, setup_mock_time_logging, setup_loguru_time_mock


class ScenarioManager:
    """场景管理器类

    负责设置和管理测试场景。
    """

    def __init__(self, scenario: KarparkTestScenario):
        """初始化场景管理器

        Args:
            scenario: 测试场景
        """
        self.scenario = scenario
        self.request_handler = RequestHandler(scenario)

        # 初始化时间管理器
        test_time_manager.reset_time(scenario.scenario_config.start_time)
        test_time_manager.set_time_acceleration(10.0)  # 10倍速加速测试
    
    def setup_mock_responses(self):
        """设置模拟响应"""
        # 创建回调函数
        get_callback = self.request_handler.create_get_callback()
        post_callback = self.request_handler.create_post_callback()

        # 注册回调函数
        responses.add_callback(
            responses.GET,
            "https://parking.labs.sap.cn/mobile/parking/getEntry",
            callback=get_callback,
            content_type="application/json"
        )

        responses.add_callback(
            responses.POST,
            "https://parking.labs.sap.cn/mobile/parking/saveEntry",
            callback=post_callback,
            content_type="application/json"
        )
    
    def setup_database_mock(self):
        """设置数据库模拟"""
        def mock_table_operations(operation: EnrollmentDBOperation, *args, **kwargs):
            """模拟数据库操作"""
            if operation == EnrollmentDBOperation.GET_DELTA__ENROLLMENT:
                # 基于当前时间获取登记数据
                current_time = test_time_manager.get_current_time()
                current_enrollments = self.scenario.get_current_enrollments(current_time)
                return current_enrollments
            elif operation == EnrollmentDBOperation.GET_ALL_ENROLLMENT:
                # 基于当前时间获取所有登记数据
                current_time = test_time_manager.get_current_time()
                return self.scenario.get_current_enrollments(current_time)
            elif operation == EnrollmentDBOperation.CLEAR_ALL_TABLES:
                # 清空操作，不需要返回值
                return None
            else:
                # 其他操作返回空列表
                return []

        return patch('karpark.registrar.task_scheduler.enrollment_db_operations', side_effect=mock_table_operations)

    def setup_smart_sleep_mock(self):
        """设置smart_sleep模拟，避免实际等待时间"""
        # 保存对scenario_manager的引用
        scenario_manager = self

        def mock_smart_sleep(scheduler_self):
            """模拟smart_sleep函数，推进时间但不实际等待"""
            if scheduler_self.active_parker:
                status_based_delay = scheduler_self.get_status_based_delay()
                token_based_delay = scheduler_self.get_token_based_delay()
                delay = min(status_based_delay, token_based_delay)
                print(f"[MOCK] Would sleep for {delay}s (status_based={status_based_delay}s, token_based={token_based_delay}s)")
                # 推进时间而不是实际等待
                test_time_manager.advance_time(delay) ##todo 非工作时间总是900
            else:
                from karpark.common.config import TaskSchedulerConfig
                delay = TaskSchedulerConfig.Intervals.poll_interval_minimum.seconds
                print(f"[MOCK] Would sleep for {delay}s (minimum break time)")
                # 推进时间而不是实际等待
                test_time_manager.advance_time(delay)

            # 🎯 关键：在每次sleep后更新场景状态
            scenario_manager.update_scenario_by_time()

        return patch('karpark.registrar.task_scheduler.TaskScheduler.smart_sleep', mock_smart_sleep)

    def setup_smtp_mock(self):
        """设置SMTP模拟，防止测试时真正发送邮件"""
        def mock_sendmail(self, from_addr, to_addrs, msg, mail_options=[], rcpt_options=[]):
            """模拟sendmail函数，输出邮件信息但不实际发送"""
            print(f"[MOCK] Would send email from {from_addr} to {to_addrs}")
            print(f"[MOCK] Email content length: {len(msg)} characters")
            return {}  # 返回空字典表示发送成功

        return patch('smtplib.SMTP_SSL.sendmail', mock_sendmail)

    def setup_config_file_mock(self):
        """设置配置文件模拟，用测试配置替换生产配置"""
        from pathlib import Path
        from unittest.mock import patch
        from contextlib import ExitStack

        # 获取测试配置文件路径
        test_config_path = Path(__file__).parent.parent / "fixtures" / "configs" / "mock_configs.yaml"

        # 创建一个ExitStack来管理多个patch
        stack = ExitStack()

        # Mock paths.email_address_file，这是实际加载邮件配置的路径
        stack.enter_context(patch('karpark.common.paths.paths.email_address_file', test_config_path))

        # Mock paths.access_control_file，这是实际加载访问控制配置的路径
        stack.enter_context(patch('karpark.common.paths.paths.access_control_file', test_config_path))

        return stack

    def setup_mock_time_logging(self):
        """设置模拟时间日志记录，让logger显示模拟时间而不是真实时间"""
        # 使用新的 loguru 时间模拟方法
        return setup_loguru_time_mock()

    def update_scenario_by_time(self):
        """基于时间更新场景状态"""
        current_time = test_time_manager.get_current_time()

        # 显示当前时间状态
        if hasattr(self, '_last_status_time'):
            if (current_time - self._last_status_time).total_seconds() >= 10:  # 每10秒显示一次状态
                self._show_time_status(current_time)
                self._last_status_time = current_time
        else:
            self._show_time_status(current_time)
            self._last_status_time = current_time

        # 更新Parker位置
        self.scenario.update_all_parkers_positions(current_time)

    def _show_time_status(self, current_time: datetime):
        """显示当前时间状态"""
        config = self.scenario.scenario_config

        if current_time < config.close_at_time:
            _status = "NORMAL"
            _next_event = f"CLOSE at {config.close_at_time.strftime('%H:%M:%S')}"
        elif current_time < config.delete_at_time:
            _status = "CLOSED"
            _next_event = f"DELETE at {config.delete_at_time.strftime('%H:%M:%S')}"
        elif current_time < config.reopen_at_time:
            _status = "DELETING"
            _next_event = f"REOPEN at {config.reopen_at_time.strftime('%H:%M:%S')}"
        else:
            _status = "REOPENED"
            _next_event = "No more events"

        print(f"[TIME] {current_time.strftime('%H:%M:%S')} - Status: {_status} - Next: {_next_event}")

    def reset_time(self):
        """重置时间管理器"""
        test_time_manager.reset_time(self.scenario.scenario_config.start_time)


