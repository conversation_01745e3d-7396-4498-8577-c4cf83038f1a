#!/usr/bin/env python3
"""测试场景配置模块

此模块定义了测试所需的配置类和测试场景。
"""
import random
from dataclasses import dataclass
from typing import List, Optional, Tuple
from datetime import datetime, timedelta

from karpark.utils import AutoNameEnum, auto

from tests.integration.fixtures.data.enrollment_data import CLIENT_ENROLLMENT_STAGES
from tests.integration.fixtures.data.server_parker_data import ServerManagedParker, SERVER_MANAGED_PARKERS


class CloseScope(AutoNameEnum):
    """注册关闭范围枚举类
    
    定义了不同的注册关闭范围场景。
    """
    ALL = auto()  # 所有parker都关闭
    TO_BE_DELETED = auto()  # 只有将要被删除的parker关闭
    ALREADY_DELETED = auto()  # 只有已经被删除的parker关闭


@dataclass
class ScenarioConfig:
    """测试配置类

    使用时间点来触发各种操作。
    """
    start_time: datetime  # 测试开始时间
    close_at_time: datetime  # 关闭时间点
    delete_at_time: datetime  # 删除时间点
    reopen_at_time: datetime  # 重新开放时间点
    delete_total_size: int  # 删除总数
    delete_at_once: bool # 是否一次删除所有
    close_scope: CloseScope  # 注册关闭范围
    occupied_plate_numbers: List[str]  # 已占用的车牌号列表

    def __post_init__(self):
        """初始化后处理"""
        # 计算删除相关参数
        delete_duration = (self.reopen_at_time - self.delete_at_time).total_seconds()
        # 假设每5秒删除一批，这样可以有足够的删除批次
        self.deletion_interval_seconds = 5
        if self.delete_at_once:
            self.deletion_batch_count = 1
        else:
            self.deletion_batch_count = max(1, int(delete_duration // self.deletion_interval_seconds))

        if self.deletion_batch_count > 1: # 有多个删除批次
            self.dynamic_batch_size = self.delete_total_size // (self.deletion_batch_count - 1) # 这里一定要-1，AI总会删掉
            self.last_batch_size = self.delete_total_size % (self.deletion_batch_count - 1) # 这里一定要-1，AI总是删掉
            if self.last_batch_size == 0:
                self.last_batch_size = self.dynamic_batch_size
        else: # 一次删除完
            self.dynamic_batch_size = self.delete_total_size
            self.last_batch_size = self.delete_total_size

    def should_close_registration(self, parker_item: ServerManagedParker, current_time: datetime) -> bool:
        """判断是否应该关闭注册"""
        if current_time < self.close_at_time:
            return False

        if current_time >= self.reopen_at_time:
            return False

        if self.close_scope == CloseScope.ALL:
            return True
        elif self.close_scope == CloseScope.TO_BE_DELETED:
            return parker_item.order > 0 and parker_item.initial_order <= self.delete_total_size
        elif self.close_scope == CloseScope.ALREADY_DELETED:
            return parker_item.order == 0

        return False

    def should_return_duplicate_error(self, car_no: str) -> bool:
        """判断是否应该返回重复车牌错误"""
        return car_no in self.occupied_plate_numbers

    def get_current_deletion_batch(self, current_time: datetime) -> int:
        """获取当前删除批次的大小"""
        if current_time < self.delete_at_time or current_time >= self.reopen_at_time:
            return 0

        elapsed_seconds = (current_time - self.delete_at_time).total_seconds()
        batch_index = int(elapsed_seconds // self.deletion_interval_seconds)

        if batch_index >= self.deletion_batch_count - 1:
            return self.last_batch_size
        else:
            return self.dynamic_batch_size


@dataclass
class KarparkTestScenario:
    """测试场景类

    包含测试场景所需的所有数据。
    """
    scenario_config: ScenarioConfig  # 测试配置
    server_managed_parkers: List[ServerManagedParker]  # 测试用的parker列表
    enrollments: List[Tuple[int, List[dict]]]  # 登记数据列表，每个元素为(relative_time_minutes, enrollment_data)的元组
    last_deletion_time: Optional[datetime] = None  # 上次删除时间
    deletion_count: int = 0  # 已删除批次计数

    def get_server_managed_parker_by_cookie(self, cookie: str) -> Optional[ServerManagedParker]:
        """根据cookie获取服务器管理的parker项目"""
        for parker in self.server_managed_parkers:
            if parker.cookie == cookie:
                return parker
        return None

    def get_current_enrollments(self, current_time: datetime) -> List[dict]:
        """获取当前时间的登记数据"""
        current_stage_index = 0
        elapsed_minutes = (current_time - self.scenario_config.start_time).total_seconds() / 60

        # 找到当前时间应该使用的阶段
        for i, (relative_time_minutes, enrollment_data) in enumerate(self.enrollments):
            if elapsed_minutes >= relative_time_minutes:
                current_stage_index = i

        # 返回对应阶段的登记数据
        if current_stage_index < len(self.enrollments):
            return self.enrollments[current_stage_index][1]  # 返回元组的第二个元素（enrollment_data）
        else:
            # 如果超出了所有阶段，返回最后一个阶段的数据
            return self.enrollments[-1][1] if self.enrollments else []

    def update_all_parkers_positions(self, current_time: datetime) -> None:
        """基于时间更新所有parker的状态"""
        # 静态和关闭状态，不需要更新
        if current_time < self.scenario_config.delete_at_time:
            return

        # 删除状态，更新所有parker的order和total
        # if self.scenario_config.delete_at_time <= current_time < self.scenario_config.reopen_at_time: 
        # # 由于系统休眠可能导致错过重新开放时间点，需要继续执行删除操作，因此移除<=重新开放时间的限制条件
        if current_time >= self.scenario_config.delete_at_time:
            self._update_all_parkers_positions(current_time)

        # 重新开放状态，模拟新注册
        if current_time >= self.scenario_config.reopen_at_time:
            self._simulate_external_registrations_effect_on_total()

    def _update_all_parkers_positions(self, current_time: datetime) -> None:
        """基于时间更新所有parker的位置信息"""
        # 基于模拟时间计算应该执行的删除批次
        # 如果当前时间已经超过重新开放时间（由于休眠时间过长），说明错过了删除窗口，需要删除所有内容
        if current_time >= self.scenario_config.reopen_at_time:
            expected_batch_count = self.scenario_config.deletion_batch_count
        else:
            elapsed_since_delete_start = (current_time - self.scenario_config.delete_at_time).total_seconds()
            expected_batch_count = int(elapsed_since_delete_start // self.scenario_config.deletion_interval_seconds) + 1

        # 限制批次数量不超过总批次数
        expected_batch_count = min(expected_batch_count, self.scenario_config.deletion_batch_count)

        # 如果当前删除批次数已经达到预期，不需要删除
        if self.deletion_count >= expected_batch_count:
            return

        # 执行所有需要的删除批次
        while self.deletion_count < expected_batch_count:
            # 检查是否还有需要删除的位置
            active_parkers = [p for p in self.server_managed_parkers if p.order > 0]
            if not active_parkers:
                break

            # 计算当前批次大小
            if self.deletion_count >= self.scenario_config.deletion_batch_count - 1:
                current_batch = self.scenario_config.last_batch_size
            else:
                current_batch = self.scenario_config.dynamic_batch_size

            print(f"[DELETION] Deletion batch {self.deletion_count + 1}/{self.scenario_config.deletion_batch_count}, batch_size={current_batch}")

            # 执行删除操作
            self._perform_deletion_batch(current_batch)

            # 更新删除状态
            self.deletion_count += 1

    def _perform_deletion_batch(self, batch_size: int) -> None:
        """执行一批删除操作"""
        # 第一步：计算所有parker的new_order和临时total
        temp_totals = []
        new_orders = []
        for parker in self.server_managed_parkers:
            new_order = max(0, parker.order - batch_size)
            new_orders.append(new_order)
            temp_total = 0 if new_order == 0 else max(0, parker.total - batch_size)
            temp_totals.append(temp_total)

        # 第二步：找到所有非零total的最大值作为统一的total
        non_zero_totals = [total for total in temp_totals if total > 0]
        unified_total = max(non_zero_totals) if non_zero_totals else 0

        # 第三步：应用更新
        for i, parker in enumerate(self.server_managed_parkers):
            old_order = parker.order
            old_total = parker.total
            new_total = unified_total if new_orders[i] > 0 else 0
            parker.update_position(new_orders[i], new_total)

            # 添加队列变化日志（仅显示重要变化）
            if old_order != new_orders[i] and parker.car_no in ["沪KX0217", "沪KX0090", "沪KX0015"]:
                print(f"[QUEUE] {parker.car_no}: order {old_order}→{new_orders[i]}, total {old_total}→{new_total} (batch_size={batch_size})")

    def _simulate_external_registrations_effect_on_total(self) -> None:
        """模拟程序外他人新注册"""
        for parker in self.server_managed_parkers:
            if parker.total > 0:  # 只对非零total的parker更新
                parker.total += random.randint(1, 3)

# 测试场景创建函数
def create_test_scenario() -> KarparkTestScenario:
    """创建测试场景"""
    # 设置测试开始时间为当前时间
    # start_time = datetime.now() - timedelta(hours=6, seconds=30)
    start_time = datetime.now().replace(hour=14, minute=30, second=0, microsecond=0)

    # 按照你的要求设置时间点：
    # - close_at: start_time后10分钟30秒
    # - delete_at: start_time后10分钟60秒
    # - reopen_at: start_time后10分钟300秒（delete后240秒）
    # - enrollment_change: 现在在CLIENT_ENROLLMENT_STAGES中定义

    close_at_time = start_time + timedelta(minutes=10, seconds=30)   # start_time后10分钟30秒
    delete_at_time = start_time + timedelta(minutes=10, seconds=60)  # start_time后10分钟60秒
    reopen_at_time = start_time + timedelta(minutes=10, seconds=300) # start_time后10分钟300秒

    delete_total_size = 217
    occupied_plate_numbers=["沪KX0090"]

    return KarparkTestScenario(
        scenario_config=ScenarioConfig(
            start_time=start_time,
            close_at_time=close_at_time,
            delete_at_time=delete_at_time,
            reopen_at_time=reopen_at_time,
            delete_total_size=delete_total_size,
            delete_at_once=False,
            close_scope=CloseScope.ALREADY_DELETED,
            occupied_plate_numbers=occupied_plate_numbers
        ),
        server_managed_parkers=SERVER_MANAGED_PARKERS,
        enrollments=CLIENT_ENROLLMENT_STAGES
    )


# 测试场景实例
KARPARK_TEST_SCENARIO = create_test_scenario()
