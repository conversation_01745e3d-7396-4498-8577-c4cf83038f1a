# Email configuration for testing
admin_emails:
  - <EMAIL>
  - <EMAIL>

global_emails:
  - <EMAIL>
  - <EMAIL>

# Access control configuration for testing
# Using test user IDs that match the parker data (I000015, I000090, I000217, I000218, I000030, I000040)
admin_users:
  - I000015  # position 15 parker - highest priority
  - I000090  # position 90 parker - highest priority

privileged_users:
  - I000217  # position 217 parker - medium priority
  - I000218  # position 218 parker - medium priority

authorized_users:
  - I000030  # position 30 parker - regular priority
  - I000040  # position 40 parker - regular priority
  - I000015  # admin users are also authorized
  - I000090  # admin users are also authorized
  - I000217  # privileged users are also authorized
  - I000218  # privileged users are also authorized