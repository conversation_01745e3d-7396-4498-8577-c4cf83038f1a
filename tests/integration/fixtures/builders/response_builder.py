#!/usr/bin/env python3
"""响应构建器模块

此模块负责生成各种状态下的模拟服务器响应。
"""
from dataclasses import dataclass
from typing import Dict, Any

from datetime import datetime

from tests.integration.fixtures.configs.scenarios import ScenarioConfig, CloseScope
from tests.integration.fixtures.data.server_parker_data import ServerManagedParker
from tests.integration.utils.error_utils import create_none_error, create_closed_error, create_duplicate_error


@dataclass
class RequestContext:
    """请求上下文，包含所有响应生成函数需要的参数"""
    config: ScenarioConfig
    server_managed_parker_item: ServerManagedParker
    current_time: datetime


class ResponseBuilder:
    """响应构建器类
    
    负责生成各种状态下的模拟服务器响应。
    所有响应生成方法都是静态方法，不需要实例化。
    """
    
    @staticmethod
    def _create_basic_template(request, ctx: RequestContext) -> Dict[str, Any]:
        """创建队列查询(GET)响应模板"""
        return {
            'id': None,
            'carNo': ctx.server_managed_parker_item.car_no,
            'location': ctx.server_managed_parker_item.location,
            'eeId': ctx.server_managed_parker_item.ee_id,
            'eeName': ctx.server_managed_parker_item.ee_name,
            'order': ctx.server_managed_parker_item.order,
            'total': ctx.server_managed_parker_item.total,
            'registerTime': ctx.server_managed_parker_item.register_time,
            'timeStamp': ctx.server_managed_parker_item.time_stamp,
            'error': create_none_error(),   # 稍后根据需要替换
            'status': ctx.server_managed_parker_item.status
        }

    @staticmethod
    def create_generic_response(request, ctx: RequestContext) -> Dict[str, Any]:
        """创建通用响应"""
        return ResponseBuilder._create_basic_template(request, ctx)

    @staticmethod
    def create_registration_response(request, ctx: RequestContext) -> Dict[str, Any]:
        """创建注册(POST)响应"""
        return ResponseBuilder._create_basic_template(request, ctx)

    @staticmethod
    def apply_closed_error_if_needed(resp_json: Dict[str, Any], ctx: RequestContext) -> Dict[str, Any]:
        """根据配置应用关闭错误"""
        if ctx.config.should_close_registration(ctx.server_managed_parker_item, ctx.current_time):
            resp_json["error"] = create_closed_error()
        return resp_json

    @staticmethod
    def apply_duplicate_error_if_needed(resp_json: Dict[str, Any], ctx: RequestContext, car_no: str) -> Dict[str, Any]:
        """根据配置应用重复车牌错误"""
        if ctx.config.should_return_duplicate_error(car_no):
            resp_json["error"] = create_duplicate_error(car_no)
        return resp_json
