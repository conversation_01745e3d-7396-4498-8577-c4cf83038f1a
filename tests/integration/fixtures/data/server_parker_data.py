#!/usr/bin/env python3
"""服务器管理的Parker数据模块

此模块提供服务器端Parker管理相关的测试数据。
"""
from typing import List, Optional
from dataclasses import dataclass

from tests.integration.fixtures.data.parker_data import PARKERS_DATA_HUB

DEFAULT_LOCATION = "PVG06"
QUEUE_TOTAL = 608


@dataclass
class ServerManagedParker:
    """服务器管理的Parker项目"""
    cookie: str
    car_no: Optional[str]
    location: Optional[str]
    ee_id: str
    ee_name: str
    order: int
    total: int
    register_time: Optional[str]
    time_stamp: Optional[str]
    status: Optional[str]

    def __post_init__(self):
        """初始化后处理
        
        在对象初始化后执行的操作，包括：
        1. 设置initial_order属性，初始值与order相同
        """
        self.initial_order = self.order

    def update_position(self, order: int, total: int) -> None:
        """更新排队位置相关信息

        更新Parker的排队位置和总人数。
        确保order和total不会小于0。
        当order为零时，total也设为零，同时将register_time和time_stamp置为None。

        Args:
            order: 新的排队位置
            total: 新的总人数
        """
        self.order = max(0, order)
        if self.order == 0:
            # 删除之后，除了ee_id和ee_name外，服务器无其他信息
            self.total = 0
            self.car_no = None
            self.location = None
            self.status = None
            self.register_time = None
            self.time_stamp = None
        else:
            self.total = max(0, total)


def create_server_managed_parkers() -> List[ServerManagedParker]:
    """创建服务器管理的Parker列表"""
    return [
        ServerManagedParker(
            cookie=parker.cookie,
            car_no=parker.car_no,
            location=DEFAULT_LOCATION,
            ee_id=parker.ee_id,
            ee_name=parker.ee_name,
            order=parker.order,
            total=QUEUE_TOTAL,
            register_time=parker.register_time,
            time_stamp=parker.time_stamp,
            status=parker.status,
        ) for parker in PARKERS_DATA_HUB
    ]

SERVER_MANAGED_PARKERS = create_server_managed_parkers()

if __name__ == '__main__':
    from pprint import pprint
    pprint(create_server_managed_parkers())
