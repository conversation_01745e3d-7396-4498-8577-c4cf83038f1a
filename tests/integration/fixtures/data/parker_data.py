#!/usr/bin/env python3
"""Parker测试数据模块

此模块提供Parker相关的测试数据。
"""
from dataclasses import dataclass
from typing import List, Optional

from tests.integration.utils.time_utils import create_timestamp


@dataclass
class Parker:
    """Parker数据类"""
    ee_id: str
    ee_name: str
    car_no: Optional[str]
    plate_number: str
    order: int
    cookie: str
    emails: str
    register_time: Optional[str] = None
    time_stamp: Optional[str] = None
    status: Optional[str] = "Accepted"


def to_ordinal(n: int) -> str:
    """将数字转换为序数词
    
    Args:
        n: 要转换的数字
        
    Returns:
        str: 序数词字符串
    """
    if 10 <= n % 100 <= 20:
        suffix = 'th'
    else:
        suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(n % 10, 'th')
    return f"{n}{suffix}"


PARKER_COUNT = 630


def generate_parkers() -> List[Parker]:
    """生成测试用的Parker列表"""
    parkers = []
    for i in range(1, PARKER_COUNT + 1):
        ordinal_suffix = to_ordinal(i)
        suffix = f"{i:04d}"
        parker = <PERSON>(
            ee_id=f"I00{suffix}",
            ee_name=f"Parker, {ordinal_suffix}",
            car_no=f"沪KX{suffix}",
            plate_number=f"沪KX{suffix}",
            order=i,
            cookie=f"JSESSIONID=2EDDF576D836A4E184647F9E52EDB6C8; __VCAP_ID__=603f30e5-9be4-4878-78dc-{suffix}",
            emails=f"parker.{ordinal_suffix}@example.com",
            register_time=create_timestamp(),
            time_stamp=create_timestamp()
        )
        parkers.append(parker)
    return parkers


# 全局Parker数据
PARKERS_DATA_HUB = generate_parkers()


if __name__ == "__main__":
    from pprint import pprint
    pprint(PARKERS_DATA_HUB)
