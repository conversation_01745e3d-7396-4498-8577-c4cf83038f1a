#!/usr/bin/env python3
"""登记数据模块

此模块提供客户端登记相关的测试数据。
"""
from typing import Dict, Any, List

from tests.integration.fixtures.data.parker_data import PARKERS_DATA_HUB


def create_enrollment_item(position: int, updated_time: str = '2024-01-01T09:00:00+00:00',
                           use_magic_plate: bool = False, is_withdrawn: bool = False) -> Dict[str, Any]:
    """创建指定位置的登记数据
    
    Args:
        position: Parker在列表中的位置（1-based）
        updated_time: 更新时间
        use_magic_plate: 是否使用魔法车牌
        is_withdrawn: 是否已撤回
        
    Returns:
        Dict[str, Any]: 登记数据字典
    """
    parker = PARKERS_DATA_HUB[position - 1]
    return {
        'ee_id': parker.ee_id,
        'cookie': parker.cookie,
        'plate_number': parker.plate_number,
        'use_magic_plate': use_magic_plate,
        'is_withdrawn': is_withdrawn,
        'emails': parker.emails,
        'updated_time': updated_time
    }


# 带时间配对的阶段数据结构
# 每个元素包含 (relative_time_minutes, enrollment_data) 的元组
# relative_time_minutes 表示相对于测试开始时间的分钟数
CLIENT_ENROLLMENT_STAGES = [
    # stage 1 - initial load (测试开始后5分钟)
    (1, [
        create_enrollment_item(position=15),
        create_enrollment_item(position=90),
        create_enrollment_item(position=217, use_magic_plate=True),
        create_enrollment_item(position=218),
    ]),

    # stage 2 - delta 1 (测试开始后10分钟)
    (5, [
        create_enrollment_item(position=30),
    ]),

    # stage 3 - delta 2 (测试开始后15分钟，可以根据需要添加更多阶段)
    (10, [
        create_enrollment_item(position=40),
    ])
]


if __name__ == '__main__':
    from pprint import pprint
    pprint(CLIENT_ENROLLMENT_STAGES)
