#!/usr/bin/env python3
"""HTTP工具模块

此模块提供HTTP响应生成和处理的工具函数。
"""
import json
from typing import Dict, Any


def build_http_response(resp_json: Dict[str, Any], cookie: str, status: int = 200) -> tuple:
    """测试专用HTTP响应生成工具

    用于快速构建符合测试场景的HTTP响应三元组，包含固定Cookie头设置。
    
    Args:
        resp_json: 响应体内容（字典格式）
        cookie: 测试用的固定会话标识符
        status: HTTP状态码，默认200
        
    Returns:
        tuple: 包含三个元素的元组 - 
            - 状态码 (int)
            - 响应头字典 (dict)
            - 字节格式响应体 (bytes)
    """
    json_str = json.dumps(resp_json, ensure_ascii=False)
    resp_body = json_str.encode(encoding="utf8")
    # todo headers 需要模仿的更像吗？
    headers = {"Cookie": cookie}
    return status, headers, resp_body


def handle_request_error(message: str, status_code: int = 400) -> tuple:
    """处理请求错误
    
    生成一个包含错误信息的HTTP响应。
    
    Args:
        message: 错误信息
        status_code: HTTP状态码
        
    Returns:
        tuple: 错误响应
    """
    print(f"Error: {message}")
    error_response = {"error": message}
    return build_http_response(error_response, "", status_code)
