#!/usr/bin/env python3
"""时间工具模块

此模块提供时间相关的工具函数，用于生成测试所需的时间戳。
"""
from datetime import datetime, timezone, timedelta


def create_timestamp(time_source: str = "six_months_ago") -> str:
    """创建时间戳
    
    根据指定的时间源生成ISO 8601格式的时间戳字符串。
    支持当前时间和固定六个月前时间两种模式。
    
    Args:
        time_source: 时间源选项
            - "now": 当前时间
            - "six_months_ago": 固定六个月前时间
            
    Returns:
        str: ISO 8601格式的时间戳字符串（包含毫秒和时区信息）
    """
    if time_source == "now":
        return datetime.now(timezone.utc).isoformat(timespec='milliseconds')
    
    # 返回动态计算的六个月前时间
    return (datetime.now(timezone.utc) - timedelta(days=365)).isoformat(timespec='milliseconds')
