#!/usr/bin/env python3
"""错误工具模块

此模块提供错误响应生成的工具函数。
"""
import random
from typing import Dict, Any

from karpark.common.const import ErrorCode, JsonFieldKeys


def create_none_error() -> Dict[str, Any]:
    """创建空错误响应
    
    生成一个随机概率的空错误响应，用于模拟服务器偶尔返回空错误的情况。
    
    Returns:
        Dict[str, Any]: 空错误响应
            - 80%概率返回None
            - 20%概率返回包含空错误码和错误信息的字典
    """
    return None if random.random() < 0.8 else {
        JsonFieldKeys.ERROR_CODE.value: None,
        JsonFieldKeys.ERROR_MESSAGE.value: None
    }


def create_closed_error() -> Dict[str, Any]:
    """创建注册关闭错误响应
    
    生成一个表示注册暂时关闭的错误响应。
    
    Returns:
        Dict[str, Any]: 注册关闭错误响应
    """
    return {
        JsonFieldKeys.ERROR_CODE.value: ErrorCode.CLOSED,
        JsonFieldKeys.ERROR_MESSAGE.value: "Sorry,Registration is closed temporarily, please try later."
    }


def create_duplicate_error(car_no: str) -> Dict[str, Any]:
    """创建重复车牌错误响应
    
    生成一个表示车牌号已被注册的错误响应。
    
    Args:
        car_no: 重复的车牌号
        
    Returns:
        Dict[str, Any]: 重复车牌错误响应
    """
    return {
        JsonFieldKeys.ERROR_CODE.value: ErrorCode.DUPLICATE_CAR.value,
        JsonFieldKeys.ERROR_MESSAGE.value: f"Car Plate Number {car_no} was already registered by I000000, please confirm with administrator."
    }
