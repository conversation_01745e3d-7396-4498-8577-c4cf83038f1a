#!/usr/bin/env python3
"""pytest配置文件

此文件包含pytest的配置和fixture定义。
"""
import pytest
from pathlib import Path

from tests.integration.fixtures.configs.scenarios import KARPARK_TEST_SCENARIO
from tests.integration.core.scenario_manager import ScenarioManager


@pytest.fixture(scope="session")
def test_scenarios():
    """提供测试场景的fixture"""
    return KARPARK_TEST_SCENARIO


@pytest.fixture(scope="function")
def scenario_manager(test_scenarios):
    """提供场景管理器的fixture"""
    # 默认使用第一个场景
    scenario = test_scenarios[0]
    manager = ScenarioManager(scenario)
    manager.reset_time()
    return manager


@pytest.fixture(scope="session")
def test_data_dir():
    """提供测试数据目录的fixture"""
    return Path(__file__).parent / "fixtures" / "data"


@pytest.fixture(scope="session")
def test_config_dir():
    """提供测试配置目录的fixture"""
    return Path(__file__).parent / "fixtures" / "configs"
