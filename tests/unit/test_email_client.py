"""邮件客户端测试模块。

本模块测试 EmailClient 类的各项功能，包括：
1. 邮件发送功能
2. 邮件接收功能
3. 邮件解析功能
4. 连接池管理
5. 错误处理

注意：虽然使用真实的配置信息，但通过 mock SMTP 发送函数来避免实际发送邮件。
"""
import unittest
from unittest.mock import patch, MagicMock
from email.mime.multipart import MIMEMultipart

from karpark.mail.email_client import EmailClient
from karpark.common.config import MailConfig


class TestEmailClient(unittest.TestCase):
    """邮件客户端测试类。"""

    def setUp(self):
        """测试环境设置。"""
        self.email_client = EmailClient()
        # 保存原始配置
        self.original_config = MailConfig.Settings

    def tearDown(self):
        """测试环境清理。"""
        # 恢复原始配置
        MailConfig.Settings = self.original_config

    def test_send_mail_basic(self):
        """测试基本的邮件发送功能。"""
        # 准备测试数据
        subject = "测试邮件"
        text = "这是一封测试邮件"
        to = ["<EMAIL>"]
        
        # Mock SMTP 连接和发送
        with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
            # 设置 mock 对象
            mock_server = MagicMock()
            mock_get_connection.return_value = mock_server
            
            # 发送邮件
            result = self.email_client.send_mail(
                subject=subject,
                text=text,
                to=to
            )
            
            # 验证结果
            self.assertTrue(result)
            # 验证 sendmail 被调用
            mock_server.sendmail.assert_called_once()
            
            # 验证邮件内容
            call_args = mock_server.sendmail.call_args[1]
            self.assertEqual(call_args['from_addr'], MailConfig.Settings.from_address)
            self.assertEqual(call_args['to_addrs'], to)

    def test_send_mail_with_attachments(self):
        """测试带附件的邮件发送功能。"""
        # 准备测试数据
        subject = "测试带附件的邮件"
        text = "这是一封带附件的测试邮件"
        to = ["<EMAIL>"]
        attachments = ["test.txt"]
        
        # Mock 文件读取
        with patch('builtins.open', MagicMock()) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = b"test content"
            
            # Mock SMTP 连接和发送
            with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
                mock_server = MagicMock()
                mock_get_connection.return_value = mock_server
                
                # Mock _create_msg 方法
                with patch.object(self.email_client, '_create_msg') as mock_create_msg:
                    mock_msg = MIMEMultipart()
                    mock_create_msg.return_value = mock_msg
                    
                    # 发送邮件
                    result = self.email_client.send_mail(
                        subject=subject,
                        text=text,
                        to=to,
                        attachments=attachments
                    )
                    
                    # 验证结果
                    self.assertTrue(result)
                    mock_server.sendmail.assert_called_once()
                    
                    # 验证附件是否被正确添加
                    mock_create_msg.assert_called_once()
                    call_args = mock_create_msg.call_args[1]
                    self.assertEqual(call_args['subject'], subject)
                    self.assertEqual(call_args['text'], text)
                    self.assertEqual(call_args['to'], to)
                    self.assertEqual(call_args['attachments'], attachments)

    def test_send_mail_error_handling(self):
        """测试邮件发送错误处理。"""
        # 准备测试数据
        subject = "测试错误处理"
        text = "测试邮件"
        to = ["<EMAIL>"]
        
        # Mock SMTP 连接抛出异常
        with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
            mock_server = MagicMock()
            mock_server.sendmail.side_effect = Exception("发送失败")
            mock_get_connection.return_value = mock_server
            
            # Mock _handle_smtp_error 方法
            with patch.object(self.email_client, '_handle_smtp_error') as mock_handle_error:
                # 发送邮件并验证异常处理
                with self.assertRaises(Exception):
                    self.email_client.send_mail(
                        subject=subject,
                        text=text,
                        to=to
                    )
                
                # 验证错误处理方法被调用了3次（重试机制）
                self.assertEqual(mock_handle_error.call_count, 3)
                # 验证每次调用都使用了正确的参数
                for call in mock_handle_error.call_args_list:
                    self.assertEqual(call[0][0], mock_server)

    def test_send_mail_with_string_recipients(self):
        """测试使用","或";"分割的邮件地址字符串格式的收件人参数。"""
        # 准备测试数据
        subject = "测试字符串收件人"
        text = "这是一封测试邮件"
        to = "<EMAIL>, <EMAIL>"
        cc = "<EMAIL>; <EMAIL>"
        bcc = "<EMAIL>, <EMAIL>; <EMAIL>"
        
        # Mock SMTP 连接和发送
        with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
            mock_server = MagicMock()
            mock_get_connection.return_value = mock_server
            
            # 发送邮件
            result = self.email_client.send_mail(
                subject=subject,
                text=text,
                to=to,
                cc=cc,
                bcc=bcc
            )
            
            # 验证结果
            self.assertTrue(result)
            mock_server.sendmail.assert_called_once()
            
            # 验证邮件内容
            call_args = mock_server.sendmail.call_args[1]
            self.assertEqual(call_args['from_addr'], MailConfig.Settings.from_address)
            # 验证收件人列表被正确解析和去重
            expected_recipients = [
                "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>"
            ]
            self.assertEqual(sorted(call_args['to_addrs']), sorted(expected_recipients))

    def test_send_mail_with_mixed_recipients(self):
        """测试混合格式的收件人参数。"""
        # 准备测试数据
        subject = "测试混合格式收件人"
        text = "这是一封测试邮件"
        to = ["<EMAIL>", "<EMAIL>, <EMAIL>"]
        cc = "<EMAIL>; <EMAIL>, <EMAIL>"
        bcc = ["<EMAIL>", "<EMAIL>; <EMAIL>"]
        
        # Mock SMTP 连接和发送
        with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
            mock_server = MagicMock()
            mock_get_connection.return_value = mock_server
            
            # 发送邮件
            result = self.email_client.send_mail(
                subject=subject,
                text=text,
                to=to,
                cc=cc,
                bcc=bcc
            )
            
            # 验证结果
            self.assertTrue(result)
            mock_server.sendmail.assert_called_once()
            
            # 验证邮件内容
            call_args = mock_server.sendmail.call_args[1]
            self.assertEqual(call_args['from_addr'], MailConfig.Settings.from_address)
            # 验证收件人列表被正确解析和去重
            expected_recipients = [
                "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>"
            ]
            self.assertEqual(sorted(call_args['to_addrs']), sorted(expected_recipients))

    def test_connection_pool(self):
        """测试 SMTP 连接池功能。"""
        # Mock SMTP 连接
        with patch('karpark.mail.email_client.EmailClient._get_smtp_connection') as mock_get_connection:
            mock_server = MagicMock()
            mock_get_connection.return_value = mock_server
            
            # 发送第一封邮件
            self.email_client.send_mail(
                subject="测试1",
                text="测试内容1",
                to=["<EMAIL>"]
            )
            
            # 发送第二封邮件
            self.email_client.send_mail(
                subject="测试2",
                text="测试内容2",
                to=["<EMAIL>"]
            )
            
            # 验证连接被正确复用
            self.assertEqual(mock_get_connection.call_count, 2)
            self.assertEqual(mock_server.sendmail.call_count, 2)

    def test_recipient_deduplication(self):
        """测试收件人列表去重功能。"""
        # 准备测试数据
        to = ["<EMAIL>", "<EMAIL>"]
        cc = ["<EMAIL>", "<EMAIL>"]
        bcc = ["<EMAIL>", "<EMAIL>"]
        
        # 获取去重后的收件人列表
        recipients = self.email_client.get_recipients_list(to, cc, bcc)
        
        # 验证结果
        self.assertEqual(len(recipients), 3)  # 应该只有3个不同的地址
        self.assertIn("<EMAIL>", recipients)
        self.assertIn("<EMAIL>", recipients)
        self.assertIn("<EMAIL>", recipients)


if __name__ == '__main__':
    unittest.main() 