"""Unit tests for notification system status notifications."""
import unittest
from unittest.mock import Mock

from karpark.registrar.parker import <PERSON>
from karpark.registrar.entities.status import Status
from karpark.registrar.notification import Notification
from karpark.mail.template import EmailTemplate


class TestNotificationStatus(unittest.TestCase):
    """Test cases for notification system status notifications."""

    def setUp(self):
        """Set up test environment."""
        # Clear singleton instance
        Notification._instance = None
        self.notification = Notification()
        
        # Mock mail client
        self.notification.email_client = Mock()
        
        # Mock parker
        self.parker = Mock(spec=<PERSON>)
        self.parker.emails = ["<EMAIL>"]
        
        # Set up test mail addresses
        self.notification.global_email_addresses = ["<EMAIL>"]
        self.notification.admin_email_addresses = ["<EMAIL>"]

    def tearDown(self):
        """Clean up after tests."""
        self.notification.clear_notification_history()

    def test_error_status_notification(self):
        """测试错误状态通知
        状态: ERROR
        接收者: 管理员邮箱
        模板: EmailTemplate.ERROR
        """
        self.parker.current_status = Status.ERROR
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.ERROR["subject"],
            text=EmailTemplate.ERROR["text"]
        )

    def test_initial_status_notification(self):
        """测试初始状态通知
        状态: INITIAL
        接收者: 无特定接收者
        模板: 无特定模板
        """
        self.parker.current_status = Status.INITIAL
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_not_called()

    def test_closed_static_status_notification(self):
        """测试静态关闭状态通知
        状态: CLOSED_STATIC
        接收者: 全局订阅者
        模板: EmailTemplate.CLOSED_STATIC
        """
        self.parker.current_status = Status.CLOSED_STATIC
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.CLOSED_STATIC["subject"],
            text=EmailTemplate.CLOSED_STATIC["text"]
        )

    def test_closed_moving_status_notification(self):
        """测试移动关闭状态通知
        状态: CLOSED_MOVING
        接收者: 全局订阅者
        模板: EmailTemplate.CLOSED_MOVING
        """
        self.parker.current_status = Status.CLOSED_MOVING
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.CLOSED_MOVING["subject"],
            text=EmailTemplate.CLOSED_MOVING["text"]
        )

    def test_closed_deleted_status_notification(self):
        """测试删除关闭状态通知
        状态: CLOSED_DELETED
        接收者: 全局订阅者
        模板: EmailTemplate.CLOSED_DELETED
        """
        self.parker.current_status = Status.CLOSED_DELETED
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.CLOSED_DELETED["subject"],
            text=EmailTemplate.CLOSED_DELETED["text"]
        )

    def test_open_static_status_notification(self):
        """测试静态开启状态通知
        状态: OPEN_STATIC
        接收者: 无特定接收者
        模板: 无特定模板
        """
        self.parker.current_status = Status.OPEN_STATIC
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_not_called()

    def test_open_moving_status_notification(self):
        """测试移动开启状态通知
        状态: OPEN_MOVING
        接收者: 全局订阅者
        模板: EmailTemplate.OPEN_MOVING
        """
        self.parker.current_status = Status.OPEN_MOVING
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.OPEN_MOVING["subject"],
            text=EmailTemplate.OPEN_MOVING["text"]
        )

    def test_open_deleted_status_notification(self):
        """测试删除开启状态通知
        状态: OPEN_DELETED
        接收者: 全局订阅者
        模板: EmailTemplate.OPEN_DELETED
        """
        self.parker.current_status = Status.OPEN_DELETED
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.OPEN_DELETED["subject"],
            text=EmailTemplate.OPEN_DELETED["text"]
        )

    def test_reg_success_status_notification(self):
        """测试注册成功状态通知
        状态: REG_SUCCESS
        接收者: Parker邮箱
        模板: EmailTemplate.REG_SUCCESS
        """
        self.parker.current_status = Status.REG_SUCCESS
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.REG_SUCCESS["subject"],
            text=EmailTemplate.REG_SUCCESS["text"]
        )

    def test_reg_failure_status_notification(self):
        """测试注册失败状态通知
        状态: REG_FAILURE
        接收者: Parker邮箱
        模板: EmailTemplate.REG_FAILURE
        """
        self.parker.current_status = Status.REG_FAILURE
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)
        self.notification.email_client.send_mail.assert_called_once_with(
            to=["<EMAIL>"],
            subject=EmailTemplate.REG_FAILURE["subject"],
            text=EmailTemplate.REG_FAILURE["text"]
        )


if __name__ == '__main__':
    unittest.main() 