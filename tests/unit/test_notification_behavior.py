"""Unit tests for notification system behaviors."""
import time
import unittest
from unittest.mock import Mock, patch

from karpark.registrar.notification import Notification, NotificationReceiver, NotificationTemplate
from karpark.registrar.parker import <PERSON>
from karpark.registrar.entities.status import Status
from karpark.mail.template import EmailTemplate


class TestNotificationBehavior(unittest.TestCase):
    """Test cases for notification system behaviors."""

    def setUp(self):
        """Set up test environment."""
        # Clear singleton instance
        Notification._instance = None
        self.notification = Notification()
        
        # Mock mail client
        self.notification.email_client = Mock()
        self.notification.email_client.send_mail = Mock()
        
        # Mock parker with proper comparison behavior
        self.parker = Mock(spec=<PERSON>)
        self.parker.emails = ["<EMAIL>"]
        # 创建 queue_info mock 对象
        self.parker.queue_info = Mock()
        self.parker.queue_info.ee_id = "test_ee_id"
        
        # Set up test mail addresses
        self.notification.global_email_addresses = ["<EMAIL>"]
        self.notification.admin_email_addresses = ["<EMAIL>"]

    def tearDown(self):
        """Clean up after tests."""
        # 不要清除通知历史，让它在测试过程中保持状态
        pass

    def test_repeatable_notification(self):
        """测试重复通知行为
        1. 第一次发送通知应该成功
        2. 立即再次发送相同通知时不应该发送（冷却期内）
        3. 冷却期过后（3600秒后）应该可以再次发送通知
        """
        with patch.object(self.notification, '_load_email_address_data') as mock_load:
            # 设置测试邮箱地址
            self.notification.global_email_addresses = ["<EMAIL>"]
            self.notification.admin_email_addresses = ["<EMAIL>"]
            
            self.parker.current_status = Status.ERROR
            
            # 第一次发送通知
            print(f"Parker status: {self.parker.current_status}")
            print(f"Parker emails: {self.parker.emails}")
            result1 = self.notification.send_notification(self.parker)
            print(f"First notification result: {result1}")
            print(f"Email client calls: {self.notification.email_client.send_mail.call_count}")
            self.assertTrue(result1, "第一次发送通知应该成功")
            self.notification.email_client.send_mail.assert_called_once_with(
                to=["<EMAIL>"],
                subject=EmailTemplate.ERROR["subject"],
                text=EmailTemplate.ERROR["text"]
            )
            self.notification.email_client.send_mail.reset_mock()
            
            # 立即再次发送相同通知（应该在冷却期内）
            result2 = self.notification.send_notification(self.parker)
            self.assertTrue(result2, "冷却期内的通知发送应该返回成功")
            self.notification.email_client.send_mail.assert_not_called()
            
            # 模拟时间过去3601秒（冷却期+1秒）
            current_time = time.time()
            with patch('karpark.registrar.notification.time.time') as mock_time:
                # 设置模拟时间
                mock_time.return_value = current_time + 3601
                
                # 打印调试信息
                print(f"Current time: {current_time}")
                print(f"Mocked time: {mock_time.return_value}")
                print(f"Time difference: {mock_time.return_value - current_time}")
                
                # 发送第三次通知
                result3 = self.notification.send_notification(self.parker)
                print(f"Third notification result: {result3}")
                print(f"Email client calls after cooldown: {self.notification.email_client.send_mail.call_count}")
                
                self.assertTrue(result3, "冷却期后的通知发送应该成功")
                self.notification.email_client.send_mail.assert_called_once_with(
                    to=["<EMAIL>"],
                    subject=EmailTemplate.ERROR["subject"],
                    text=EmailTemplate.ERROR["text"]
                )

    def test_global_notification_priority(self):
        """Test global notification priority handling."""
        # Send higher priority notification first
        self.parker.current_status = Status.OPEN_DELETED
        self.notification.send_notification(self.parker)
        self.notification.email_client.send_mail.reset_mock()
        
        # Lower priority notification should not be sent
        self.parker.current_status = Status.CLOSED_STATIC
        self.notification.send_notification(self.parker)
        self.notification.email_client.send_mail.assert_not_called()

    def test_notification_without_emails(self):
        """Test notification behavior when no emails are configured."""
        self.parker.emails = []
        self.notification.global_email_addresses = []
        self.notification.admin_email_addresses = []
        
        # Test all notification types
        for status in Status:
            self.parker.current_status = status
            result = self.notification.send_notification(self.parker)
            self.assertTrue(result)
            self.notification.email_client.send_mail.assert_not_called()

    def test_email_client_error(self):
        """Test handling of mail client errors."""
        self.notification.email_client.send_mail.side_effect = Exception("Mocked Exception - Email error")
        
        self.parker.current_status = Status.REG_SUCCESS
        result = self.notification.send_notification(self.parker)
        
        self.assertFalse(result)
        self.notification.email_client.send_mail.assert_called_once()

    def test_clear_notification_history(self):
        """Test clearing notification history."""
        # Send some notifications
        self.parker.current_status = Status.REG_SUCCESS
        self.notification.send_notification(self.parker)
        self.notification.email_client.send_mail.reset_mock()
        
        # Clear history
        self.notification.clear_notification_history()
        
        # Send same notification again
        self.notification.send_notification(self.parker)
        self.notification.email_client.send_mail.assert_called_once()

    def test_invalid_status_template(self):
        """测试处理无效状态模板的情况"""
        self.parker.current_status = "INVALID_STATUS"  # 设置一个不存在的状态
        result = self.notification.send_notification(self.parker)
        self.assertTrue(result)  # 应该优雅地处理无效状态

    def test_notification_record_accuracy(self):
        """测试通知记录是否正确记录"""
        # 确保测试状态不在 repeatable_statuses 列表中
        if Status.REG_SUCCESS in self.notification.repeatable_statuses[NotificationReceiver.PARKER]:
            self.notification.repeatable_statuses[NotificationReceiver.PARKER].remove(Status.REG_SUCCESS)
            
        self.parker.current_status = Status.REG_SUCCESS
        self.notification.send_notification(self.parker)
        
        # 验证记录是否正确添加
        record = self.notification.records[NotificationReceiver.PARKER]
        # 使用列表而不是集合来验证
        sent_notifications = list(record.parker_sent)
        self.assertEqual(len(sent_notifications), 1)
        self.assertEqual(sent_notifications[0][0], Status.REG_SUCCESS)
        self.assertEqual(sent_notifications[0][1].queue_info.ee_id, self.parker.queue_info.ee_id)

    def test_notification_with_empty_template(self):
        """测试空模板的情况"""
        # 临时修改模板
        original_template = self.notification.TEMPLATES[Status.REG_SUCCESS]
        self.notification.TEMPLATES[Status.REG_SUCCESS] = NotificationTemplate(subject="", text="")
        
        try:
            self.parker.current_status = Status.REG_SUCCESS
            result = self.notification.send_notification(self.parker)
            self.assertTrue(result)  # 应该能处理空模板
        finally:
            # 恢复原始模板
            self.notification.TEMPLATES[Status.REG_SUCCESS] = original_template

    def test_notification_priority_combinations(self):
        """测试不同优先级状态的组合"""
        # 测试多个状态的优先级组合
        statuses = [
            Status.OPEN_DELETED,
            Status.CLOSED_DELETED,
            Status.OPEN_MOVING,
            Status.CLOSED_MOVING
        ]
        
        for status in statuses:
            self.parker.current_status = status
            self.notification.send_notification(self.parker)
        
        # 验证只有最高优先级的通知被发送
        self.assertEqual(self.notification.email_client.send_mail.call_count, 1)

    def test_notification_after_config_change(self):
        """测试配置变更后的通知行为"""
        # 先发送一个通知
        self.parker.current_status = Status.REG_SUCCESS
        self.notification.send_notification(self.parker)
        self.notification.email_client.send_mail.reset_mock()
        
        # 修改配置
        self.notification.repeatable_statuses[NotificationReceiver.PARKER].append(Status.REG_SUCCESS)
        
        # 再次发送通知
        self.notification.send_notification(self.parker)
        # 验证通知是否被重新发送
        self.notification.email_client.send_mail.assert_called_once()


if __name__ == '__main__':
    unittest.main() 