# KarPark - 停车位自动抢注系统

KarPark 是一个为 SAP Labs Digital Parking 提供抢先自动注册功能的系统。它提供智能队列监控、登记自动注册和实时通知。

## 🚀 功能特性

- **自动队列监控**: 持续监控停车队列状态和位置
- **智能注册**: 当队列开放时自动进行停车注册
- **魔法车牌支持**: 使用特殊字符组合提供同一车牌多次注册
- **隐藏魔法模式**: 通过特殊操作启用的车牌变体生成功能
- **实时通知**: 状态变化和队列更新的邮件提醒
- **优先级管理**: 管理员和特权用户以及普通用户优先级处理
- **命令行管理**: 登记管理的命令行界面
- **全面日志**: 使用 Loguru 进行详细的调试和监控日志

## 📋 系统要求

- Python 3.8+
- 访问 SAP Labs Digital Parking的权限(即SAP员工)
- SMTP 服务器访问权限用于邮件通知

## 🛠️ 安装

### 前置要求

确保您的系统已安装 Python 3.8 或更高版本。

### 推荐安装方法（使用 setup.py）

**setup.py 是什么？**
setup.py 是 Python 项目的标准安装配置文件，它可以：
- 自动安装项目依赖
- 将项目根目录添加到 PYTHONPATH，使模块导入更方便
- 自动调用 `scripts/install_mitm.py` 安装 MITM 代理依赖
- 创建命令行入口点，可以直接运行 `karpark` 命令

#### 开发模式安装（推荐）：
```bash
# 开发模式安装，代码修改立即生效
python setup.py develop
```

#### 标准安装：
```bash
# 标准安装到系统或虚拟环境
python setup.py install
```

#### 使用 pip 安装（推荐）：
```bash
# 开发模式安装
pip install -e .

# 标准安装
pip install .
```

### 传统安装方法

#### 对于网络连接良好的用户：
```bash
pip install -r requirements.txt
```

#### 对于中国用户或网络连接受限的用户：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 其他安装方法

#### 直接使用 pip：
```bash
pip install loguru mitmproxy requests tenacity PyYAML pytest responses click beautifulsoup4
```

#### 使用 conda：
```bash
conda install -c conda-forge loguru requests tenacity pyyaml pytest click
pip install mitmproxy responses beautifulsoup4  # 这些包在 conda-forge 中可能不可用
```

### 安装后验证

安装完成后，你可以使用以下命令验证安装：

```bash
# 检查主程序
karpark --help
# 或
python -m karpark.main

# 检查工具集（推荐的新方式）
karpark-tools                        # 显示可用工具
karpark-tools enrollment             # 启动登记管理器
karpark-tools mitm --help            # MITM 代理帮助
karpark-tools validate --help        # 资源验证工具帮助

# 传统方式（仍然可用）
karpark-enrollment                    # 登记管理器
karpark-mitm --help                  # MITM 代理
karpark-validate --help              # 资源验证工具
# 或直接运行脚本
python scripts/run_enrollment_manager.py
python scripts/run_interceptor.py --help
python scripts/run_change_monitor.py --help
```

### setup.py 详细说明

**setup.py 的主要功能：**

1. **自动依赖管理**：
   - 从 `requirements.txt` 读取并安装所有依赖
   - 自动调用 `scripts/install_mitm.py` 安装 MITM 相关依赖

2. **PYTHONPATH 配置**：
   - 将项目根目录添加到 Python 路径
   - 使得可以直接 `import karpark` 而无需手动配置路径

3. **命令行入口点**：
   - `karpark` → 运行主程序
   - `karpark-tools` → 统一工具入口点（推荐）
     - `karpark-tools enrollment` → 登记管理器
     - `karpark-tools mitm` → MITM 代理服务器
     - `karpark-tools validate` → 服务器资源验证工具
   - 传统入口点（向后兼容）：
     - `karpark-enrollment` → 登记管理器
     - `karpark-mitm` → MITM 代理服务器
     - `karpark-validate` → 服务器资源验证工具

4. **包数据管理**：
   - 自动包含配置文件（`*.yaml`）
   - 包含 MITM 代理的静态资源文件

5. **开发模式支持**：
   - 使用 `pip install -e .` 进行开发模式安装
   - 代码修改立即生效，无需重新安装

**安装模式对比：**

| 安装方式                      | 优点          | 缺点       | 适用场景  |
|---------------------------|-------------|----------|-------|
| `pip install -e .`        | 开发友好，修改立即生效 | 需要保持源码目录 | 开发和调试 |
| `pip install .`           | 标准安装，独立运行   | 修改需重新安装  | 生产部署  |
| `python setup.py develop` | 同开发模式       | 较老的方式    | 兼容性需求 |
| `python setup.py install` | 传统安装方式      | 不推荐使用    | 特殊环境  |

## 📦 setup.py 使用指南

### 什么是 setup.py？

setup.py 是 Python 项目的标准配置文件，类似于其他语言的包管理配置：
- **Node.js** 的 `package.json`
- **Rust** 的 `Cargo.toml`
- **Go** 的 `go.mod`

它定义了项目的元数据、依赖关系、安装方式等信息。

### 为什么使用 setup.py？

1. **标准化安装**：提供统一的安装体验
2. **依赖管理**：自动处理项目依赖
3. **路径配置**：自动配置 PYTHONPATH
4. **命令行工具**：创建全局可用的命令
5. **包分发**：支持打包和分发

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd karpark-project

# 2. 开发模式安装（推荐）
pip install -e .

# 3. 验证安装
karpark --help
karpark-tools                    # 显示工具菜单
karpark-tools enrollment         # 启动登记管理器
karpark-tools mitm --status      # 检查 MITM 状态
```

### 高级用法

```bash
# 安装额外的开发依赖
pip install -e ".[dev]"

# 仅安装 MITM 相关依赖
pip install -e ".[mitm]"

# 安装所有可选依赖
pip install -e ".[dev,mitm]"

# 从源码构建并安装
python setup.py sdist bdist_wheel
pip install dist/karpark-*.whl
```

### 故障排除

**问题：找不到 karpark 模块**
```bash
# 解决方案：确保使用开发模式安装
pip install -e .
```

**问题：命令行工具不可用**
```bash
# 解决方案：检查 Python Scripts 目录是否在 PATH 中
python -m pip show karpark
```

**问题：MITM 依赖安装失败**
```bash
# 解决方案：手动安装 MITM 依赖
python scripts/install_mitm.py --system
```

## ⚙️ 配置

在运行 KarPark 之前，您需要通过编辑 `karpark/config/` 目录中的 YAML 文件来配置系统：

### 必需的配置文件
####  应用层配置文件
1. **`base.yaml`** - 主应用程序配置
2. **`_email_address.yaml`** - 邮件通知设置
3. **`_access_control.yaml`** - 管理员和特权用户设置
#### 层序层静态配置文件(`./static` 子目录)
1. **`_logger.yaml`** - loguru日志配置
2. **`_magic_plate.yaml`** - 魔法车牌配置
3. **`_service_url.yaml`** - Digital Parking 服务 URL

### 配置示例

#### 基本邮件配置 (`_email_address.yaml`)：
admin_emails收到系统异常通知，global_emails收到全局parking status变化的提醒便于及时手动注册
```yaml
admin_emails:
  - <EMAIL>
global_emails:
  - <EMAIL>
```

#### 服务 URL 配置 (`_service_url.yaml`)：
除非Digital Parking服务变更，否则无需修改
```yaml
  domain: 'parking.labs.sap.cn'
  base_path: '/mobile/parking'
  urls:
    entry: '/getEntry'
    post: '/saveEntry'
```

## 🚀 使用方法

### 运行主应用程序

启动停车队列监控和登记系统：

**使用 setup.py 安装后（推荐）：**
```bash
karpark
```

**传统方式：**
```bash
python karpark/main.py
```

或使用入口点：

```bash
python -m karpark.main
```

### 命令行登记管理

通过命令行界面管理停车登记：

**使用 setup.py 安装后（推荐的新方式）：**
```bash
karpark-tools enrollment
```

**传统方式（仍然可用）：**
```bash
karpark-enrollment
# 或
python scripts/run_enrollment_manager.py
```

这提供了一个交互式菜单，用于：
- 添加新登记
- 编辑现有登记
- 查看登记状态
- 管理用户优先级

### MITM 代理模式

通过 Web 界面拦截进行自动登记：

**使用 setup.py 安装后（推荐的新方式）：**
```bash
karpark-tools mitm
```

**传统方式（仍然可用）：**
```bash
karpark-mitm
# 或
python scripts/run_interceptor.py
```

然后配置您的设备使用代理（默认端口：8080）。

## 🎭 隐藏功能：魔法模式

### 功能概述

魔法模式是一个隐藏的开发者功能，允许用户通过特殊操作启用车牌变体生成功能，以提高停车注册的成功率。

### 使用方法

#### 激活魔法模式
1. 在停车注册页面，快速点击 **Employee ID 输入框** 10 次（3秒内）
2. 界面会切换为**亮橙色主题**
3. 显示 "Magic Mode Enabled! 🔥" 提示

#### 功能特性
- **隐蔽性**: 没有明显的UI指示器，保持功能的隐蔽性
- **视觉反馈**: 启用时界面变为亮橙色主题
- **车牌变体**: 自动生成并使用车牌变体进行注册
- **一键切换**: 再次快速点击10次可关闭魔法模式

#### 技术原理
- **前端**: 检测快速点击，切换主题和POST URL
- **后端**: 使用 `magic_plate.py` 生成车牌变体
- **处理**: 随机选择变体替换原始车牌号

### 使用场景
1. **生产环境**: 提高注册成功率，对普通用户不可见
2. **功能验证**: 测试车牌变体生成和处理逻辑

### 安全考虑
- **访问控制**: 功能隐蔽，降低被滥用的风险
- **日志记录**: 所有操作都有详细日志记录

#### MITM 代理高级功能

**进程管理和远程重启**

MITM 代理现在支持进程管理和通过邮件远程重启功能：

**使用 setup.py 安装后（推荐的新方式）：**
```bash
# 启动代理（等待模式，自动启用邮件监控）
karpark-tools mitm

# 后台启动（自动启用邮件监控）
karpark-tools mitm --no-wait

# 重启代理
karpark-tools mitm --restart

# 停止代理
karpark-tools mitm --stop

# 查看状态
karpark-tools mitm --status

# 仅启动邮件监控
karpark-tools mitm --email-monitor

# 测试模式（不加载拦截脚本）
karpark-tools mitm --test-mode

# 安装依赖
karpark-tools mitm --install-deps
```

**传统方式（仍然可用）：**
```bash
# 启动代理（等待模式，自动启用邮件监控）
karpark-mitm

# 后台启动（自动启用邮件监控）
karpark-mitm --no-wait

# 重启代理
karpark-mitm --restart

# 停止代理
karpark-mitm --stop

# 查看状态
karpark-mitm --status

# 仅启动邮件监控
karpark-mitm --email-monitor

# 测试模式（不加载拦截脚本）
karpark-mitm --test-mode

# 安装依赖
karpark-mitm --install-deps
```

**传统方式：**
```bash
# 启动代理（等待模式，自动启用邮件监控）
python scripts/run_interceptor.py

# 后台启动（自动启用邮件监控）
python scripts/run_interceptor.py --no-wait

# 重启代理
python scripts/run_interceptor.py --restart

# 停止代理
python scripts/run_interceptor.py --stop

# 查看状态
python scripts/run_interceptor.py --status

# 仅启动邮件监控
python scripts/run_interceptor.py --email-monitor

# 测试模式（不加载拦截脚本）
python scripts/run_interceptor.py --test-mode

# 安装依赖
python scripts/run_interceptor.py --install-deps
```

**邮件远程重启功能**

通过发送邮件可以远程重启 MITM 代理：

1. **发送重启邮件**：
   - **主题**：必须是以下之一：
     - "karpark restart request"
     - "proxy restart"
     - "restart proxy"
   - **内容**：必须包含所有关键词和密码：
     - 关键词："restart", "mproxy", "karpark"
     - 密码："karpark_restart_2024"

2. **邮件示例**：
   ```
   主题：karpark restart request

   请重启 mitmproxy 服务。
   关键词：restart mitmproxy karpark
   密码：karpark_restart_2024
   ```

3. **安全特性**：
   - 多层验证（主题、关键词、密码）
   - 防重复处理
   - **重启冷却时间**：10分钟内只能重启一次
   - **自动回复**：冷却期内会自动回复邮件说明情况
   - 可配置的安全参数

4. **冷却机制**：
   - 防止频繁重启导致系统不稳定
   - 冷却期内的重启请求会收到自动回复邮件
   - 回复邮件包含上次重启时间和下次可用时间

**配置文件**

邮件监控配置位于 `karpark/config/_mitmproxy.yaml`：

```yaml
# 邮件监控设置
email_monitor:
  enabled: true                    # 启用邮件监控
  check_interval: 5               # 检查间隔（分钟）

  restart_command:
    password: "karpark_restart_2024"  # 重启密码（邮件主题或正文中包含此密码即可触发重启）
    cooldown_minutes: 10          # 重启冷却时间（分钟）

    reply_settings:               # 冷却期回复邮件设置
      enabled: true               # 启用自动回复
      subject_template: "Re: {original_subject} - Restart Cooldown Active"
      message_template: |         # 回复邮件模板
        Hello,
        Your restart request has been received, but the MITM proxy was recently restarted.
        Please try again after {remaining_minutes} minutes.

  retention:
    days: 7                       # 邮件记录保留天数
```

**环境管理**

MITM代理的Python环境选择现在通过配置文件和命令行参数进行管理：

**1. 配置文件方式（推荐）**

在 `karpark/config/_mitmproxy.yaml` 中设置：
```yaml
python:
#  use_system_python: true  # 使用系统Python
#  # 或
  use_system_python: false # 使用虚拟环境（默认）
```

**2. 命令行临时覆盖**
```bash
# 临时使用系统Python（覆盖配置文件设置）
python scripts/run_interceptor.py --system-python
```

**3. 配置优先级**
1. 命令行参数 `--system-python`（最高优先级）
2. 配置文件 `_mitmproxy.yaml` 中的 `python.use_system_python`
3. 自动检测（虚拟环境检测和项目环境查找）

#### MITM 代理部署指南

MITM 脚本现在支持多种部署方式，可以灵活适应不同的环境需求。

**1. 虚拟环境部署（推荐）**

这是最安全和可靠的部署方式，避免依赖冲突。

```bash
# 在项目根目录创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 安装依赖
pip install mitmproxy coloredlogs psutil

# 运行（自动检测虚拟环境）
python scripts/run_interceptor.py

# 或者自动安装依赖
python scripts/run_interceptor.py --install-deps
```

**2. 系统 Python 部署**

适用于生产环境或容器化部署。

```bash
# 安装系统依赖
python -m pip install mitmproxy coloredlogs psutil

# 或者使用脚本自动安装
python scripts/run_interceptor.py --install-deps --system-python

# 强制使用系统 Python 运行
python scripts/run_interceptor.py --system-python

# 或者设置环境变量
export KARPARK_USE_SYSTEM_PYTHON=1  # Linux/Mac
set KARPARK_USE_SYSTEM_PYTHON=1     # Windows
```

**3. 容器化部署**

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

# 安装依赖
RUN pip install mitmproxy coloredlogs psutil

# 设置环境变量使用系统 Python
ENV KARPARK_USE_SYSTEM_PYTHON=1

# 运行
CMD ["python", "scripts/run_mitm_server.py", "--no-wait"]
```

**最佳实践**

1. **开发环境**: 使用虚拟环境
2. **生产环境**: 使用容器化部署或系统 Python
3. **CI/CD**: 设置 `KARPARK_USE_SYSTEM_PYTHON=1`
4. **多环境**: 使用 requirements.txt 管理依赖

**依赖管理**

创建 requirements.txt:
```
mitmproxy>=10.0.0
coloredlogs>=15.0.0
psutil>=5.9.0
```

安装:
```bash
pip install -r requirements.txt
```

**故障排除**

1. **找不到依赖模块**
   ```bash
   # 检查当前 Python 环境
   python -c "import sys; print(sys.executable)"

   # 安装依赖
   python scripts/run_interceptor.py --install-deps
   ```

2. **权限问题**
   ```bash
   # 使用用户级安装
   python -m pip install --user mitmproxy coloredlogs psutil
   ```

3. **进程管理问题**
   ```bash
   # 检查进程状态
   python scripts/run_interceptor.py --status

   # 强制停止
   python scripts/run_interceptor.py --stop

   # 清理PID文件
   rm karpark/data/mitm_proxy.*
   ```

---

## 📁 工具集 (Scripts)

本项目包含多个实用工具脚本，提供停车登记管理、服务器资源验证等功能。

### 🎮 停车登记管理工具

**文件**: `scripts/run_enrollment_manager.py`

#### 功能特性
- 以格式化表格显示所有登记信息
- 使用箭头键（↑/↓）浏览登记记录
- 向导式界面添加新登记
- 逐字段编辑现有登记
- 带确认提示的安全删除

#### 使用方法
```bash
# 从项目根目录运行
python scripts/run_enrollment_manager.py
```

#### 菜单选项
1. **查看登记信息** - 浏览和管理现有登记
2. **添加新登记** - 创建新的登记记录
3. **退出** - 关闭应用程序

### 🔍 服务器资源验证工具

#### 目的
验证服务器端资源（HTML、CSS、JS）是否发生变化，通过下载基准版本并与后续版本进行比较来检测变化。
本项目基于对 SAP Labs Digital Parking 的JavaScript程序parking.js?v3进行逆向工程，因此如果服务器端资源发生变化，项目需要做相应调整。
**文件**: `scripts/run_validate_server_resources.py`

#### 功能特性
- 🔍 **自动资源发现**: 自动解析HTML页面中的CSS和JS资源链接
- 📦 **基准版本管理**: 下载并保存资源的基准版本用于比较
- 🔄 **变化检测**: 使用SHA256哈希值快速检测内容变化
- 📊 **详细报告**: 提供美观的表格形式变化报告
- 🍪 **智能Cookie管理**: 集成Cookie输入和状态验证，自动保存重用
- 🎯 **交互式界面**: 支持命令行参数和交互式菜单两种使用方式
- 🔐 **认证状态显示**: 自动验证Cookie有效性并显示状态信息

#### 获取认证Cookie（使用 Fiddler Classic）

由于目标地址只能在微信小程序中打开，需要使用 Fiddler Classic 抓取Cookie：

1. **安装并启动 Fiddler Classic**
   - 下载并安装 Fiddler Classic
   - 启动 Fiddler，确保正在监听流量

2. **配置手机代理**
   - 在手机WiFi设置中配置代理
   - 代理地址：运行 Fiddler 的电脑IP
   - 代理端口：8888（Fiddler默认端口）

3. **访问微信小程序**
   - 在手机上打开微信
   - 进入SAP Digital Parking 小程序
   - 访问注册页面

4. **抓取Cookie**
   - 在 Fiddler 中找到对 `parking.labs.sap.cn` 的请求
   - 查看 Request Headers
   - 复制完整的 `Cookie` 字段值

#### 使用方法

**命令行模式（推荐的新方式）：**
```bash
# 初始化基准版本（会提示输入Cookie）
karpark-tools validate init

# 检查资源变化（会提示输入Cookie并显示状态）
karpark-tools validate check

# 显示最新报告
karpark-tools validate report

# 检查Cookie状态
karpark-tools validate status
```

**交互式模式（推荐的新方式）：**
```bash
# 启动交互式菜单
karpark-tools validate --interactive
```

**传统方式（仍然可用）：**
```bash
# 命令行模式
python scripts/run_change_monitor.py init
python scripts/run_change_monitor.py check
python scripts/run_change_monitor.py report
python scripts/run_change_monitor.py status

# 交互式模式
python scripts/run_change_monitor.py --interactive
# 或使用传统入口点
karpark-validate init
karpark-validate check
karpark-validate --interactive
```

### 🍪 Cookie管理

Cookie输入和管理已完全集成到资源验证工具中：

- **自动提示**: 在执行 `init` 和 `check` 命令时自动提示输入Cookie
- **状态验证**: 自动验证Cookie有效性并显示状态
- **自动保存**: Cookie会自动保存供后续使用
- **智能重用**: 优先使用已保存的有效Cookie

无需单独的Cookie管理步骤，所有操作都在主要命令中完成。

### 📊 CI/CD集成

脚本支持集成到CI/CD流水线中：

```yaml
# GitHub Actions示例
- name: Validate Server Resources
  run: |
    python scripts/run_validate_server_resources.py check --cookie "${{ secrets.PARKING_COOKIE }}"
```

## 📁 Scripts 目录详细说明

### 主要脚本详细说明

#### `run_enrollment_manager.py`
交互式 CLI 工具，用于管理停车登记。

**功能特性：**
- 查看和管理登记记录
- 添加新的登记记录
- 审查 Cookie 所有者
- 丰富的格式化和交互式界面

**使用方法：**
```bash
# 推荐的新方式
karpark-tools enrollment

# 传统方式（仍然可用）
python scripts/run_enrollment_manager.py
# 或使用传统入口点
karpark-enrollment
```

#### `run_mitm_server.py`
MITM 代理服务器启动器，具有进程管理功能。

**功能特性：**
- 进程管理（启动/停止/重启/状态）
- 远程重启的邮件监控
- 环境检测（虚拟环境 vs 系统 Python）
- 依赖安装

**使用方法：**
```bash
# 推荐的新方式
karpark-tools mitm
karpark-tools mitm --no-wait

# 传统方式（仍然可用）
python scripts/run_interceptor.py
python scripts/run_interceptor.py --no-wait
# 或使用传统入口点
karpark-mitm
```

#### `run_validate_server_resources.py`
服务器资源验证工具，用于监控 HTML、CSS 和 JS 文件的变化。

**功能特性：**
- 基准资源管理
- 使用 SHA256 哈希的变化检测
- Cookie 认证支持
- 交互式和命令行模式

**使用方法：**
```bash
# 推荐的新方式
karpark-tools validate init
karpark-tools validate check
karpark-tools validate --interactive

# 传统方式（仍然可用）
python scripts/run_change_monitor.py init
python scripts/run_change_monitor.py check
# 或使用传统入口点
karpark-validate --help
```

### 工具脚本详细说明

#### `tools/set_cookie.py`
专用的认证 Cookie 设置和管理工具。

**功能特性：**
- 多种 Cookie 输入方式
- Cookie 验证
- 自动保存供后续使用

**使用方法：**
```bash
python scripts/tools/set_cookie.py
```

#### `tools/install_mitm.py`
安装 MITM 代理依赖的工具。

**功能特性：**
- 自动依赖检测
- 虚拟环境支持
- 系统 Python 安装选项

**使用方法：**
```bash
# 为当前环境安装
python scripts/tools/install_mitm.py

# 为系统 Python 安装
python scripts/tools/install_mitm.py --system
```

### 配置文件

#### `config/resource_validation_config.yaml`
服务器资源验证工具的配置文件。

包含以下设置：
- 目标 URL
- 请求参数
- 要监控的资源类型
- 验证规则

### Setup.py 集成

所有主要脚本都与 setup.py 集成，可以使用入口点运行：

**推荐的新方式（统一工具入口点）：**
- `karpark-tools` → `scripts.run_karpark_tools:main`
  - `karpark-tools enrollment` → 登记管理器
  - `karpark-tools mitm` → MITM 代理服务器
  - `karpark-tools validate` → 服务器资源验证工具

**传统方式（向后兼容）：**
- `karpark-enrollment` → `scripts.run_enrollment_manager:main`
- `karpark-mitm` → `scripts.run_mitm_server:main`
- `karpark-validate` → `scripts.run_validate_server_resources:cli`

---

## 📊 系统架构

### 核心组件

- **TaskScheduler**: 管理和调度多个停车任务
- **Parker**: 处理单个车辆登记和队列监控
- **StatusManager**: 处理状态变化并触发相应操作
- **Notification**: 基于状态变化管理邮件通知
- **MagicPlate**: 生成特殊车牌变体以提高成功率
- **隐藏魔法模式**: 通过特殊操作启用的增强功能

### 登记方法

1. **基于邮件**: 用户发送包含登记详情的格式化邮件
2. **MITM 代理**: 自动拦截基于Web的登记尝试
3. **命令行管理**: 通过命令行界面直接登记
4. **隐藏魔法模式**: 通过特殊操作启用车牌变体功能

## 📈 监控和日志

KarPark 通过 Loguru 提供全面的日志记录：

- **应用程序日志**: 存储在 `logs/` 目录中
- **实时监控**: 带有彩色日志的控制台输出
- **调试信息**: 用于故障排除的详细执行跟踪

### 日志级别

- `INFO`: 一般操作信息
- `DEBUG`: 详细执行跟踪
- `WARNING`: 非关键问题
- `ERROR`: 需要注意的错误条件

## 🔧 开发

### 项目结构

```
karpark-project/
├── data/                  # 数据存储目录
│   ├── server_resources/  # 服务器资源缓存
│   └── sqlite/           # SQLite数据库文件
├── docs/                  # 项目文档
├── karpark/              # 项目核心代码
│   ├── cli/              # 命令行界面模块
│   ├── colorlog/         # 日志配置
│   ├── common/           # 共享工具和配置
│   ├── config/           # 配置文件 (YAML)
│   │   ├── base.yaml     # 主应用程序配置
│   │   ├── _email_address.yaml  # 邮件通知设置
│   │   ├── _access_control.yaml # 管理员和特权用户设置
│   │   ├── _mitmproxy.yaml      # MITM代理和邮件监控配置
│   │   └── static/       # 静态配置文件
│   │       ├── _logger.yaml     # loguru日志配置
│   │       ├── _magic_plate.yaml # 魔法车牌配置
│   │       └── _service_url.yaml # Digital Parking 服务 URL
│   ├── core/             # 核心业务逻辑
│   │   ├── entities/     # 数据模型和枚举
│   │   ├── db_operations.py  # 数据库操作
│   │   ├── parker.py     # 单个停车管理
│   │   ├── task_scheduler.py # 任务调度和协调
│   │   ├── status_manager.py # 状态处理
│   │   └── notification.py   # 邮件通知系统
│   ├── mail/             # 邮件处理模块
│   │   ├── email_client.py      # 邮件客户端
│   │   ├── email_db_operations.py # 邮件数据库操作
│   │   └── template.py   # 邮件模板
│   ├── mitm/             # MITM 代理集成
│   │   ├── launch_mitm_server.py # MITM代理启动器（增强版）
│   │   ├── process_manager.py    # 进程管理工具
│   │   ├── email_monitor.py      # 邮件监控服务
│   │   ├── intercept.py  # 拦截脚本
│   │   ├── admin/        # 管理员页面
│   │   └── enrollment/   # 注册页面替换资源
│   │       └── replacement/
│   │           ├── authorized/  # 授权用户资源
│   │           └── interim/     # 临时状态资源
│   └── utils/            # 工具函数
├── logs/                 # 日志文件目录
├── scripts/              # 实用工具脚本
│   ├── config/           # 配置文件
│   │   └── resource_validation_config.yaml  # 服务器资源验证配置
│   ├── tools/            # 工具脚本
│   │   ├── install_mitm.py              # MITM 依赖安装脚本
│   │   └── set_cookie.py                # Cookie 管理工具
│   ├── run_enrollment_manager.py        # 登记管理器
│   ├── run_mitm_server.py              # MITM 代理服务器
│   └── run_validate_server_resources.py # 服务器资源验证工具
└── tests/                # 测试代码
    ├── integration/      # 集成测试
    └── unit/            # 单元测试
```

### 运行测试

执行测试套件：

```bash
pytest tests/
```

运行集成测试：

```bash
pytest tests/integration/
```

运行单元测试：

```bash
pytest tests/unit/
```

#### 集成测试详细说明

集成测试采用分层架构设计，包含以下组件：

- **core/**: 测试框架核心组件，负责测试编排和执行
- **fixtures/**: 测试数据、配置和构建器
  - `data/`: 纯数据定义（Parker数据、登记数据等）
  - `configs/`: 测试场景和配置文件
  - `builders/`: 数据和响应构建器
- **utils/**: 通用工具函数，按功能分类

主要特性：
- 职责分离：每个模块都有明确的职责
- 配置驱动：测试行为通过配置控制
- 时间模拟：支持时间加速和场景模拟
- Mock支持：完整的HTTP响应和数据库操作模拟

运行特定测试场景：

```bash
# 运行主要集成测试
pytest tests/integration/test_runner.py::test_karpark_integration -v

# 使用PyCharm或其他IDE运行
python tests/integration/test_runner.py
```

### 测试 MITM 功能

测试邮件监控功能：

```bash
# 测试邮件监控系统
python scripts/test_email_monitor.py
```

测试 MITM 代理功能：

```bash
# 测试基本功能（不加载脚本）
python scripts/run_interceptor.py --test-mode --no-wait

# 检查代理状态
python scripts/run_interceptor.py --status
```

## 📝 API 响应格式

系统处理以下 JSON 格式的停车队列信息：

```json
{
  "id": null,
  "eeName": "Zhang,San",
  "eeId": "I000001",
  "location": "PVG06",
  "carNo": "沪A001001",
  "registerTime": "2024-01-01T07:31:19.010+00:00",
  "timeStamp": "2024-01-01T07:31:19.010+00:00",
  "error": null,
  "order": 291,
  "total": 582,
  "status": "Waiting"
}
```

## 🤝 贡献

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目根据 LICENSE 文件中指定的条款进行许可。

## 🆘 支持

如需支持和问题咨询：

1. 查看 `docs/` 目录获取详细文档
2. 查看 `logs/` 目录中的日志进行故障排除

## 🔄 版本历史

查看 git 提交历史获取详细的版本信息和更改。

---

## 🐛 故障排除

### Cookie 获取问题

**问题**: 无法从浏览器获取Cookie
**解决方案**: 使用 Fiddler Classic 抓取微信小程序流量

### 认证问题

**问题**: 401 Unauthorized 或 403 Forbidden
**解决方案**:
1. 检查Cookie是否有效
2. 重新使用 Fiddler 获取Cookie
3. 使用Cookie测试功能验证

### 网络问题

**问题**: 连接超时或网络错误
**解决方案**:
1. 检查网络连接
2. 确认能访问目标URL
3. 检查防火墙设置

### 权限问题

**问题**: 无法创建文件或目录
**解决方案**:
1. 检查data目录权限
2. 确保有足够磁盘空间

### MITM 代理问题

**问题**: MITM 代理无法启动
**解决方案**:
1. 检查端口是否被占用：`netstat -an | findstr :8080`
2. 安装必要依赖：`python scripts/run_mitm_server.py --install-deps`
3. 检查Python环境：`python scripts/run_mitm_server.py --system-python`

**问题**: 邮件监控不工作
**解决方案**:
1. 检查邮件配置：`karpark/config/base.yaml` 中的邮件账户设置
2. 验证邮件连接：`python scripts/test_email_monitor.py`
3. 检查配置文件：`karpark/config/_mitmproxy.yaml` 中的邮件监控设置

**问题**: 远程重启不响应
**解决方案**:
1. 确认邮件包含所有必需元素：
   - 正确的主题行
   - 所有关键词："restart", "proxy", "karpark"
   - 正确的密码："karpark_restart_2024"
2. 检查邮件监控状态：`python scripts/run_mitm_server.py --status`
3. 查看日志文件了解详细错误信息

**问题**: 进程管理问题
**解决方案**:
1. 检查PID文件：`karpark/data/mitm_proxy.pid`
2. 清理僵尸进程：`python scripts/run_mitm_server.py --stop`
3. 手动清理：`rm karpark/data/mitm_proxy.*`
4. 检查进程状态：`python scripts/run_mitm_server.py --status`

## 🔒 安全注意事项

### 通用安全
1. **Cookie安全**: Cookie包含敏感信息，不要在公共场所或日志中暴露
2. **文件权限**: 确保cookie.txt文件权限设置正确
3. **定期更新**: 定期更新Cookie以确保持续访问
4. **备份重要**: 重要的基准版本建议定期备份

### MITM 代理安全
1. **邮件重启安全**:
   - 重启密码应定期更换
   - 不要在日志中记录密码
   - 确保邮件账户安全

2. **进程管理安全**:
   - PID文件包含敏感信息，设置适当权限
   - 定期清理过期的进程记录
   - 监控异常的进程重启活动

3. **网络安全**:
   - MITM代理端口应仅对必要的网络开放
   - 考虑使用防火墙限制访问
   - 监控代理流量异常

4. **配置文件安全**:
   - `_mitmproxy.yaml` 包含敏感配置，设置适当权限
   - 不要将配置文件提交到公共代码仓库
   - 使用环境变量覆盖敏感配置

---

## 📚 快速参考

### KarPark 工具命令速查

**推荐的新方式（统一工具入口点）：**
```bash
# 工具菜单
karpark-tools                                        # 显示工具菜单

# 登记管理器
karpark-tools enrollment                             # 启动登记管理器

# MITM 代理基本操作
karpark-tools mitm                                   # 启动（前台）
karpark-tools mitm --no-wait                        # 启动（后台）
karpark-tools mitm --restart                        # 重启
karpark-tools mitm --stop                           # 停止
karpark-tools mitm --status                         # 状态

# MITM 代理高级功能
karpark-tools mitm --email-monitor                  # 邮件监控
karpark-tools mitm --test-mode                      # 测试模式
karpark-tools mitm --install-deps                   # 安装依赖
karpark-tools mitm --system-python                  # 系统Python

# 资源验证工具
karpark-tools validate                               # 交互式菜单
karpark-tools validate init                         # 初始化基准
karpark-tools validate check                        # 检查变化
karpark-tools validate report                       # 显示报告
```

**传统方式（仍然可用）：**
```bash
# MITM 代理基本操作
python scripts/run_interceptor.py              # 启动（前台）
python scripts/run_interceptor.py --no-wait    # 启动（后台）
python scripts/run_interceptor.py --restart    # 重启
python scripts/run_interceptor.py --stop       # 停止
python scripts/run_interceptor.py --status     # 状态

# 或使用传统入口点
karpark-enrollment                                   # 登记管理器
karpark-mitm                                         # MITM 代理
karpark-validate                                     # 资源验证

# 测试工具
python scripts/test_email_monitor.py                # 测试邮件监控
```

### 配置文件位置

- **主配置**: `karpark/config/base.yaml`
- **MITM配置**: `karpark/config/_mitmproxy.yaml`
- **邮件配置**: `karpark/config/_email_address.yaml`
- **访问控制**: `karpark/config/_access_control.yaml`

### 重要文件位置

- **PID文件**: `karpark/data/mitm_proxy.pid`
- **状态文件**: `karpark/data/mitm_proxy_status.json`
- **日志目录**: `logs/`
- **数据目录**: `data/`

### 邮件重启模板

```
主题：karpark restart request

请重启 mitmproxy 服务。
关键词：restart mitmproxy karpark
密码：karpark_restart_2024
```

