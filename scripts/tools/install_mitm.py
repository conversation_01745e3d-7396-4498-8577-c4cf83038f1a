#!/usr/bin/env python3
"""
Karpark 项目安装脚本
支持虚拟环境和系统环境安装
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, check=True):
    """运行命令并处理错误"""
    try:
        # 在 Windows 上指定编码以避免 UnicodeDecodeError
        encoding = 'utf-8' if sys.platform != 'win32' else 'gbk'
        result = subprocess.run(
            command,
            check=check,
            capture_output=True,
            text=True,
            encoding=encoding,
            errors='replace'  # 替换无法解码的字符
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr
    except UnicodeDecodeError:
        # 如果还是有编码问题，尝试不使用文本模式
        try:
            result = subprocess.run(command, check=check, capture_output=True)
            stdout = result.stdout.decode('utf-8', errors='replace')
            stderr = result.stderr.decode('utf-8', errors='replace')
            return result.returncode == 0, stdout, stderr
        except Exception as e:
            return False, "", str(e)

def install_with_venv():
    """使用虚拟环境安装"""
    print("🔧 创建虚拟环境...")
    
    # 创建虚拟环境
    success, stdout, stderr = run_command([sys.executable, "-m", "venv", ".venv"])
    if not success:
        print(f"❌ 创建虚拟环境失败: {stderr}")
        return False
    
    # 确定虚拟环境中的 Python 路径
    if sys.platform == "win32":
        venv_python = Path("../../.venv") / "Scripts" / "python.exe"
    else:
        venv_python = Path("../../.venv") / "bin" / "python"
    
    if not venv_python.exists():
        print("❌ 虚拟环境创建失败")
        return False
    
    print("📦 安装依赖...")
    
    # 升级 pip
    success, stdout, stderr = run_command([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"])
    if not success:
        print(f"⚠️  升级 pip 失败: {stderr}")
    
    # 安装依赖
    success, stdout, stderr = run_command([str(venv_python), "-m", "pip", "install", "-r", "../requirements.txt"])
    if not success:
        print(f"❌ 安装依赖失败: {stderr}")
        return False
    
    print("✅ 虚拟环境安装完成!")
    print(f"🐍 Python 路径: {venv_python}")
    
    # 测试安装
    print("🧪 测试安装...")
    success, stdout, stderr = run_command([
        str(venv_python), "-c",
        "import mitmproxy; import coloredlogs; print('All dependencies installed successfully!')"
    ])
    
    if success:
        print("✅ 测试通过!")
        return True
    else:
        print(f"❌ 测试失败: {stderr}")
        return False

def install_system():
    """使用系统 Python 安装"""
    print("📦 使用系统 Python 安装依赖...")
    
    # 安装依赖
    success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "-r", "../requirements.txt"])
    if not success:
        print(f"❌ 安装依赖失败: {stderr}")
        print("💡 提示: 可能需要管理员权限或使用 --user 参数")

        # 尝试用户级安装
        print("🔄 尝试用户级安装...")
        success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "--user", "-r", "../requirements.txt"])
        if not success:
            print(f"❌ 用户级安装也失败: {stderr}")
            return False
    
    print("✅ 系统环境安装完成!")
    
    # 测试安装
    print("🧪 测试安装...")
    success, stdout, stderr = run_command([
        sys.executable, "-c",
        "import mitmproxy; import coloredlogs; print('All dependencies installed successfully!')"
    ])
    
    if success:
        print("✅ 测试通过!")
        return True
    else:
        print(f"❌ 测试失败: {stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Karpark 项目安装脚本')
    parser.add_argument('--system', action='store_true', 
                       help='使用系统 Python 环境安装（默认使用虚拟环境）')
    parser.add_argument('--test-only', action='store_true',
                       help='仅测试当前环境的依赖')
    
    args = parser.parse_args()
    
    print("🚀 Karpark 项目安装程序")
    print("=" * 40)
    
    if args.test_only:
        print("🧪 测试当前环境...")
        success, stdout, stderr = run_command([
            sys.executable, "-c",
            "import mitmproxy; import coloredlogs; print('Dependencies installed successfully!')"
        ])

        if success:
            print("✅ 当前环境依赖完整!")
            print(f"📍 Python 路径: {sys.executable}")
        else:
            print("❌ 当前环境缺少依赖")
            print(f"错误信息: {stderr}")
            print("请运行安装命令:")
            print("  python install_mitm.py")
        return
    
    # 检查 requirements.txt 是否存在
    if not Path("../../requirements.txt").exists():
        print("❌ 找不到 requirements.txt 文件")
        return
    
    if args.system:
        success = install_system()
    else:
        success = install_with_venv()
    
    if success:
        print("\n🎉 安装完成!")
        print("\n📖 使用说明:")
        if args.system:
            print("  python karpark/mitm/launch_mitm_server.py --system-python")
        else:
            print("  python karpark/mitm/launch_mitm_server.py")
        print("\n📚 更多信息请查看: karpark/mitm/DEPLOYMENT.md")
    else:
        print("\n❌ 安装失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
