#!/usr/bin/env python3
"""
Launcher script for the Enrollment Manager CLI.
"""

import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

console = Console()

def main():
    """Main entry point for the enrollment manager."""
    try:
        from karpark.cli import cli

        # Show stylish welcome banner
        welcome_text = """[bold cyan]🚗 Karpark Enrollment Manager[/bold cyan]

[dim]A stylish CLI tool for managing parking enrollments[/dim]
[dim]Powered by Click & Rich for the best user experience[/dim]"""

        banner = Panel.fit(
            welcome_text,
            title="🎯 Welcome",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(banner)
        console.print()

        # Check if any command line arguments were provided
        if len(sys.argv) == 1:
            # No arguments provided, start interactive menu
            cli(['--interactive'])
        else:
            # Arguments provided, use normal CLI mode
            cli()

    except ImportError as e:
        console.print(f"[red]❌ Import error: {e}[/red]")
        console.print("[yellow]Please make sure you're running this from the project root directory.[/yellow]")
        console.print("[dim]Usage: python scripts/run_enrollment_manager.py[/dim]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
