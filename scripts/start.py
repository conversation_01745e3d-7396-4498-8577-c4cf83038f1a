#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版终端界面框架 - Windows专用
通用终端UI框架，可为多个程序提供统一的界面
"""

import curses
import os
import queue
import subprocess
import sys
import threading
import time
from pathlib import Path

# Project root directory
project_root = Path(__file__).resolve().parent.parent

# 把当前目录和项目根目录都加入PYTHONPATH
sys.path.insert(0, str(project_root))
sys.path.insert(0, os.path.dirname(__file__))

# 确保karpark包可以被找到
karpark_path = project_root / "karpark"
if karpark_path.exists():
    sys.path.insert(0, str(project_root))

# 设置 UTF-8 编码
if sys.platform == "win32":
    # 设置控制台编码为 UTF-8
    os.system('chcp 65001 >nul 2>&1')

# 强制启用颜色输出
os.environ['FORCE_COLOR'] = '1'
os.environ['COLORTERM'] = 'truecolor'
os.environ['TERM'] = 'xterm-256color'
os.environ['PYTHONIOENCODING'] = 'utf-8'


class StableTerminal:
    def __init__(self):
        self.stdscr = None
        self.menu_win = None
        self.overall_win = None
        self.output1_win = None
        self.output2_win = None
        self.status_win = None

        # 菜单选项
        self.menu_items = [
            "Start Interceptor",
            "Start Auto-registrar",
            "Stop Interceptor",
            "Stop Auto-registrar",
            "Clear Interceptor Output",
            "Clear Auto-register Output",
            "Start Enrollment Manager",
            "Start Change Monitor",
            "Stop Enrollment Manager",
            "Stop Change Monitor",
            "System Info",
            "Exit"
        ]
        self.current_menu = 0

        # 子程序管理
        self.processes = {
            'interceptor': None,
            'auto-registrar': None,
            'Enrollment Manager': None,
            'Change Monitor': None
        }

        # 外部程序状态跟踪
        self.external_programs = {
            'Enrollment Manager': False,
            'Change Monito': False
        }

        # 输出缓冲区
        self.output_buffers = {
            'overall-info': [],
            'interceptor': [],
            'auto-registrar': []
        }

        # 输出队列
        self.output_queues = {
            'overall-info': queue.Queue(),
            'interceptor': queue.Queue(),
            'auto-registrar': queue.Queue()
        }

        self.running = True
        self.need_redraw = True
        self.last_terminal_size = (0, 0)
        self.height = 0
        self.width = 0

    def init_colors(self):
        """初始化颜色方案"""
        try:
            curses.start_color()
            curses.init_pair(1, curses.COLOR_WHITE, curses.COLOR_BLUE)
            curses.init_pair(2, curses.COLOR_BLACK, curses.COLOR_WHITE)
            curses.init_pair(3, curses.COLOR_WHITE, curses.COLOR_BLACK)
            curses.init_pair(4, curses.COLOR_GREEN, curses.COLOR_BLACK)
            curses.init_pair(5, curses.COLOR_RED, curses.COLOR_BLACK)
            curses.init_pair(6, curses.COLOR_CYAN, curses.COLOR_BLACK)
            curses.init_pair(7, curses.COLOR_YELLOW, curses.COLOR_BLACK)
            curses.init_pair(8, curses.COLOR_MAGENTA, curses.COLOR_BLACK)
            # 默认颜色对
            curses.init_pair(9, curses.COLOR_WHITE, curses.COLOR_BLACK)
        except:
            pass

    def parse_ansi_colors(self, text):
        """解析ANSI颜色代码并返回带颜色信息的文本段"""
        import re

        # 移除所有ANSI转义序列，保留纯文本
        clean_text = re.sub(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])', '', text)

        # 确定文本颜色 - 默认白色
        current_color = curses.color_pair(9)

        # 查找所有ANSI颜色代码及其位置
        ansi_pattern = re.compile(r'\x1B\[([0-9;]*)m')

        # 找到所有匹配项及其位置
        last_color = None
        for match in ansi_pattern.finditer(text):
            code_sequence = match.group(1)

            if not code_sequence:  # 空代码
                continue

            codes = code_sequence.split(';') if ';' in code_sequence else [code_sequence]

            # 在这个序列中查找颜色代码
            for code in codes:
                if not code:
                    continue

                try:
                    code_num = int(code)
                    if code_num == 31:  # 红色
                        last_color = curses.color_pair(5)
                    elif code_num == 32:  # 绿色
                        last_color = curses.color_pair(4)
                    elif code_num == 33:  # 黄色
                        last_color = curses.color_pair(7)
                    elif code_num == 34:  # 蓝色
                        last_color = curses.color_pair(1)
                    elif code_num == 35:  # 洋红
                        last_color = curses.color_pair(8)
                    elif code_num == 36:  # 青色
                        last_color = curses.color_pair(6)
                    elif code_num in [37, 39]:  # 白色或默认
                        last_color = curses.color_pair(9)
                    elif code_num == 91:  # 亮红色
                        last_color = curses.color_pair(5)
                    elif code_num == 92:  # 亮绿色
                        last_color = curses.color_pair(4)
                    elif code_num == 93:  # 亮黄色
                        last_color = curses.color_pair(7)
                    elif code_num == 94:  # 亮蓝色
                        last_color = curses.color_pair(1)
                    elif code_num == 95:  # 亮洋红
                        last_color = curses.color_pair(8)
                    elif code_num == 96:  # 亮青色
                        last_color = curses.color_pair(6)
                    elif code_num == 97:  # 亮白色
                        last_color = curses.color_pair(9)
                except ValueError:
                    continue

        # 使用找到的最后一个颜色
        if last_color is not None:
            current_color = last_color

        return clean_text, current_color

    def setup_windows(self):
        """设置窗口布局"""
        height, width = self.stdscr.getmaxyx()

        if height < 15 or width < 60:
            raise Exception(f"Terminal too small. Need at least 60x15, got {width}x{height}")

        # 删除旧窗口
        for win_name in ['menu_win', 'overall_win', 'output1_win', 'output2_win', 'status_win']:
            if hasattr(self, win_name) and getattr(self, win_name):
                try:
                    win = getattr(self, win_name)
                    win.clear()
                    del win
                except:
                    pass
                setattr(self, win_name, None)

        # 计算布局 - 增加空白行
        menu_width = min(40, width // 2.5)
        right_width = width - menu_width - 1
        available_height = height - 4  # 标题栏1行 + 空白行1行 + 状态栏1行

        # OVERALL信息窗口
        overall_height = max(4, available_height // 4)
        self.overall_win = curses.newwin(overall_height, right_width, 2, menu_width + 1)

        # 计算剩余空间
        remaining_height = available_height - overall_height
        output_height = remaining_height // 2
        output1_y = 2 + overall_height
        output2_height = remaining_height - output_height
        output2_y = output1_y + output_height

        # 左侧菜单窗口
        self.menu_win = curses.newwin(available_height, menu_width, 2, 0)

        # 右侧输出窗口
        self.output1_win = curses.newwin(output_height, right_width, output1_y, menu_width + 1)

        if output2_height > 0:
            self.output2_win = curses.newwin(output2_height, right_width, output2_y, menu_width + 1)
        else:
            self.output2_win = curses.newwin(1, right_width, height - 2, menu_width + 1)

        # 状态栏
        self.status_win = curses.newwin(1, width, height - 1, 0)
        self.last_terminal_size = (height, width)

    def safe_addstr(self, win, y, x, text, attr=None):
        """安全地添加字符串"""
        try:
            max_y, max_x = win.getmaxyx()
            if y >= max_y or x >= max_x:
                return

            available_width = max_x - x - 1
            if available_width > 0:
                # 计算字符串的显示宽度（中文字符占2个宽度）
                display_width = 0
                truncated_text = ""

                for char in text:
                    char_width = 2 if ord(char) > 127 else 1  # 中文字符宽度为2
                    if display_width + char_width <= available_width:
                        truncated_text += char
                        display_width += char_width
                    else:
                        break

                if attr:
                    win.attron(attr)
                win.addstr(y, x, truncated_text)
                if attr:
                    win.attroff(attr)
        except curses.error:
            pass

    def draw_title(self):
        """绘制标题栏"""
        height, width = self.stdscr.getmaxyx()
        title = " TERMINAL INTERFACE - PYTHON VERSION "
        try:
            self.stdscr.attron(curses.color_pair(1))
            self.stdscr.addstr(0, 0, " " * width)
            title_x = max(0, (width - len(title)) // 2)
            self.stdscr.addstr(0, title_x, title[:width-1])
            self.stdscr.attroff(curses.color_pair(1))
        except curses.error:
            pass

    def draw_menu(self):
        """绘制左侧菜单"""
        try:
            self.menu_win.clear()
            self.menu_win.box()

            self.safe_addstr(self.menu_win, 1, 2, "CONTROL MENU", curses.color_pair(1))

            for i, item in enumerate(self.menu_items):
                y = i + 3
                max_y, max_x = self.menu_win.getmaxyx()
                if y >= max_y - 1:
                    break

                if i == self.current_menu:
                    self.safe_addstr(self.menu_win, y, 1, f" {item} ", curses.color_pair(2))
                else:
                    self.safe_addstr(self.menu_win, y, 2, f" {item}", curses.color_pair(3))

            self.menu_win.refresh()
        except Exception:
            pass

    def draw_output_window(self, win, title, buffer):
        """绘制输出窗口"""
        try:
            win.clear()
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))

            max_y, max_x = win.getmaxyx()
            max_lines = max(0, max_y - 3)
            start_line = max(0, len(buffer) - max_lines)

            for i, line in enumerate(buffer[start_line:]):
                if i < max_lines:
                    # 解析ANSI颜色代码
                    clean_text, color_attr = self.parse_ansi_colors(line)
                    self.safe_addstr(win, i + 2, 2, clean_text, color_attr)

            win.refresh()
        except Exception:
            pass

    def draw_status(self):
        """绘制状态栏"""
        try:
            self.status_win.clear()

            prog1_status = "RUN" if self.processes['interceptor'] and self.processes['interceptor'].poll() is None else "STOP"
            prog2_status = "RUN" if self.processes['auto-registrar'] and self.processes['auto-registrar'].poll() is None else "STOP"

            latest_overall_msg = ""
            if self.output_buffers['overall-info']:
                last_msg = self.output_buffers['overall-info'][-1]
                if "] " in last_msg:
                    latest_overall_msg = last_msg.split("] ", 1)[1]
                else:
                    latest_overall_msg = last_msg

            controls = "UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit"
            programs = f"P1:{prog1_status} P2:{prog2_status}"

            max_y, max_x = self.status_win.getmaxyx()

            if latest_overall_msg:
                status_text = f" {controls} | {programs} | {latest_overall_msg} "
            else:
                status_text = f" {controls} | {programs} "

            if len(status_text) > max_x:
                status_text = status_text[:max_x - 3] + "..."

            try:
                self.status_win.attron(curses.color_pair(6))
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text, curses.color_pair(6))
                self.status_win.attroff(curses.color_pair(6))
            except:
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text)

            self.status_win.refresh()
        except Exception:
            try:
                self.status_win.clear()
                self.status_win.addstr(0, 0, " Terminal Ready ")
                self.status_win.refresh()
            except:
                pass

    def update_outputs(self):
        """更新输出显示"""
        updated = False

        for program_name in ['overall-info', 'interceptor', 'auto-registrar']:
            try:
                while True:
                    line = self.output_queues[program_name].get_nowait()
                    timestamp = time.strftime("%H:%M:%S")
                    formatted_line = f"[{timestamp}] {line}"
                    self.output_buffers[program_name].append(formatted_line)

                    if len(self.output_buffers[program_name]) > 100:
                        self.output_buffers[program_name] = self.output_buffers[program_name][-50:]

                    updated = True
            except queue.Empty:
                pass

        if updated:
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['overall-info'])
            self.draw_output_window(self.output1_win, "INTERCEPTOR", self.output_buffers['interceptor'])
            self.draw_output_window(self.output2_win, "AUTO-REGISTRAR", self.output_buffers['auto-registrar'])
            self.draw_status()

    def start_program(self, program_name: str):
        """启动程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.output_queues['overall-info'].put(f"{program_name} already running")
            return

        try:
            if program_name == 'interceptor':
                interceptor_path = project_root / "scripts/run_interceptor.py"
                cmd = [sys.executable, '-u', str(interceptor_path)]
            else:
                auto_registrar_path = project_root / "scripts/run_auto_registrar.py"
                cmd = [sys.executable, '-u', str(auto_registrar_path)]

            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            env['FORCE_COLOR'] = '1'
            env['COLORTERM'] = 'truecolor'
            env['TERM'] = 'xterm-256color'
            env['PYTHONIOENCODING'] = 'utf-8'
            # 确保子进程也能找到karpark包
            env['PYTHONPATH'] = str(project_root) + os.pathsep + env.get('PYTHONPATH', '')

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,
                env=env,
                encoding='utf-8',
                errors='replace',
                cwd=str(project_root)  # 设置工作目录为项目根目录
            )
            self.processes[program_name] = process

            thread = threading.Thread(
                target=self.read_process_output,
                args=(program_name, process),
                daemon=True
            )
            thread.start()

            self.output_queues['overall-info'].put(f"{program_name} started successfully")

        except Exception as e:
            self.output_queues['overall-info'].put(f"{program_name} start failed: {str(e)}")

    def read_process_output(self, program_name: str, process):
        """读取子程序输出"""
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break

                # 确保字符串是正确编码的
                try:
                    if isinstance(line, bytes):
                        line = line.decode('utf-8', errors='replace')
                    self.output_queues[program_name].put(line.strip())
                except UnicodeDecodeError as ue:
                    # 如果 UTF-8 解码失败，尝试其他编码
                    try:
                        line = line.decode('gbk', errors='replace')
                        self.output_queues[program_name].put(line.strip())
                    except:
                        self.output_queues[program_name].put(f"[Encoding Error: {str(ue)}]")

        except Exception as e:
            self.output_queues[program_name].put(f"Read error: {str(e)}")
        finally:
            if process.stdout:
                process.stdout.close()

    def stop_program(self, program_name: str):
        """停止程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            try:
                self.processes[program_name].terminate()
                self.processes[program_name].wait(timeout=3)
                self.output_queues['overall-info'].put(f"{program_name} stopped")
            except subprocess.TimeoutExpired:
                self.processes[program_name].kill()
                self.output_queues['overall-info'].put(f"{program_name} force killed")
            except Exception as e:
                self.output_queues['overall-info'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['overall-info'].put(f"{program_name} not running")

    def clear_output(self, program_name: str):
        """清空输出缓冲区"""
        self.output_buffers[program_name].clear()
        self.output_queues['overall-info'].put(f"{program_name} output cleared")

        if program_name == 'interceptor':
            self.force_clear_window(self.output1_win, "INTERCEPTOR")
        elif program_name == 'auto-registrar':
            self.force_clear_window(self.output2_win, "AUTO-REGISTRAR")
        elif program_name == 'overall-info':
            self.force_clear_window(self.overall_win, "OVERALL INFO")

    def force_clear_window(self, win, title):
        """强制清空窗口"""
        try:
            max_y, max_x = win.getmaxyx()
            win.clear()
            win.erase()

            for y in range(max_y):
                for x in range(max_x):
                    try:
                        win.addch(y, x, ' ')
                    except:
                        pass

            win.clear()
            win.erase()
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
            win.noutrefresh()
            curses.doupdate()
            win.refresh()

        except Exception:
            try:
                win.clear()
                win.box()
                self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
                win.refresh()
            except:
                pass

    def show_system_info(self):
        """显示系统信息"""
        try:
            import platform
            import psutil

            info = [
                f"System: {platform.system()} {platform.release()}",
                f"Python: {platform.python_version()}",
                f"CPU: {psutil.cpu_count()} cores",
                f"Memory: {psutil.virtual_memory().total // (1024**3)} GB"
            ]

            for line in info:
                self.output_queues['overall-info'].put(line)

        except ImportError:
            self.output_queues['overall-info'].put("System info requires psutil package")
        except Exception as e:
            self.output_queues['overall-info'].put(f"System info error: {str(e)}")

    def handle_menu_action(self):
        """处理菜单操作"""
        try:
            action = self.menu_items[self.current_menu]

            if action == "Start Interceptor":
                self.start_program('interceptor')
            elif action == "Stop Interceptor":
                self.stop_program('interceptor')
            elif action == "Clear Output 1":
                self.clear_output('interceptor')
            elif action == "Start Auto-registrar":
                self.start_program('auto-registrar')
            elif action == "Stop Auto-registrar":
                self.stop_program('auto-registrar')
            elif action == "Clear Output 2":
                self.clear_output('auto-registrar')
            elif action == "Start Enrollment Manager":
                self.start_program_new_window('enrollment-manager')
            elif action == "Stop Enrollment Manager":
                self.stop_program_external('enrollment-manager')
            elif action == "Start Change Monitor":
                self.start_program_new_window('change-monitor')
            elif action == "Stop Change Monitor":
                self.stop_program_external('change-monitor')
            elif action == "System Info":
                self.show_system_info()
            elif action == "Exit":
                self.running = False

            self.draw_status()

        except Exception as e:
            self.output_queues['overall-info'].put(f"Menu error: {e}")

    def start_program_new_window(self, program_name: str):
        """在新窗口启动程序"""
        if self.external_programs[program_name]:
            self.output_queues['overall-info'].put(f"{program_name} already running in separate window")
            return

        try:
            if program_name == 'enrollment-manager':
                # 执行 run_karpark_tools.py
                script_path = 'scripts/run_karpark_tools.py'
                title = "Karpark_Tools_Program_3"
            else:  # change-monitor
                # 执行 run_monitor_resources.py
                script_path = './scripts/run_monitor_resources.py'
                title = "Monitor_Resources_Program_4"

            # 在新窗口启动实际的Python脚本
            cmd = f'start "{title}" cmd /k "python {script_path}"'

            process = subprocess.Popen(
                cmd,
                shell=True
            )

            # 标记外部程序为运行状态
            self.external_programs[program_name] = True
            self.output_queues['overall-info'].put(f"{program_name} started in new window: {script_path}")

        except Exception as e:
            self.output_queues['overall-info'].put(f"{program_name} start failed: {str(e)}")

    def stop_program_external(self, program_name: str):
        """停止外部窗口程序"""
        if self.external_programs[program_name]:
            try:
                # 使用taskkill命令终止Python进程
                # 根据程序名确定窗口标题
                if program_name == 'enrollment-manager':
                    title = "run_enrollment_manager.py"
                else:  # change-monitor
                    title = "run_change_monitor.py"

                cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'

                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                # 标记程序为停止状态
                self.external_programs[program_name] = False

                if result.returncode == 0:
                    self.output_queues['overall-info'].put(f"{program_name} window terminated successfully")
                else:
                    self.output_queues['overall-info'].put(f"{program_name} marked as stopped (window may need manual close)")

            except Exception as e:
                self.external_programs[program_name] = False
                self.output_queues['overall-info'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['overall-info'].put(f"{program_name} not running")

    def run(self):
        """主运行循环"""
        try:
            self.stdscr = curses.initscr()
            curses.noecho()
            curses.cbreak()
            self.stdscr.keypad(True)
            curses.curs_set(0)
            self.stdscr.timeout(200)

            self.init_colors()
            self.height, self.width = self.stdscr.getmaxyx()
            self.setup_windows()

            self.draw_title()
            self.draw_menu()
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['overall-info'])
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['interceptor'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['auto-registrar'])
            self.draw_status()
            self.stdscr.refresh()

            self.output_queues['overall-info'].put("Terminal System Ready")
            self.output_queues['interceptor'].put("Interceptor Window Ready")
            self.output_queues['auto-registrar'].put("Auto-registrar Window Ready")

            last_menu = -1
            refresh_counter = 0

            while self.running:
                try:
                    # 检查窗口大小变化
                    try:
                        new_height, new_width = self.stdscr.getmaxyx()
                        if new_height != self.height or new_width != self.width:
                            self.height, self.width = new_height, new_width
                            self.stdscr.clear()
                            self.stdscr.refresh()

                            # 重新设置窗口
                            self.setup_windows()

                            # 重新绘制所有内容
                            self.draw_title()
                            self.draw_menu()
                            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['overall-info'])
                            self.draw_output_window(self.output1_win, "INTERCEPTOR", self.output_buffers['interceptor'])
                            self.draw_output_window(self.output2_win, "AUTO-REGISTRAR", self.output_buffers['auto-registrar'])
                            self.draw_status()
                            self.stdscr.refresh()
                            refresh_counter = 0  # 强制重绘
                            last_menu = -1  # 强制重绘菜单
                    except Exception as e:
                        # 如果窗口大小调整失败，记录错误但继续运行
                        try:
                            self.output_queues['overall-info'].put(f"Window resize error: {str(e)}")
                        except:
                            pass

                    self.update_outputs()
                    refresh_counter += 1

                    if last_menu != self.current_menu or refresh_counter % 10 == 0:
                        self.draw_menu()
                        last_menu = self.current_menu

                    if refresh_counter % 5 == 0:
                        self.draw_status()

                    key = self.stdscr.getch()

                    if key == curses.KEY_UP:
                        self.current_menu = (self.current_menu - 1) % len(self.menu_items)
                    elif key == curses.KEY_DOWN:
                        self.current_menu = (self.current_menu + 1) % len(self.menu_items)
                    elif key == ord('\n') or key == ord('\r'):
                        self.handle_menu_action()
                    elif key == 27 or key == ord('q') or key == ord('Q'):
                        self.running = False

                except KeyboardInterrupt:
                    self.running = False
                except Exception as e:
                    try:
                        self.output_queues['overall-info'].put(f"Error: {e}")
                    except:
                        pass

        except Exception as e:
            print(f"Fatal error: {e}")
        finally:
            try:
                # 停止内部程序
                for process in [self.processes['interceptor'], self.processes['auto-registrar']]:
                    if process and process.poll() is None:
                        process.terminate()

                # 停止外部程序
                for program_name in ['enrollment-manager', 'change-monitor']:
                    if self.external_programs[program_name]:
                        try:
                            title = f"Program_{program_name[-1]}"
                            cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'
                            subprocess.run(cmd, shell=True, capture_output=True)
                        except:
                            pass

                # 清理临时文件
                for temp_file in ['temp_enrollment-manager.py', 'temp_change-monitor.py']:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except:
                        pass
            except:
                pass
            try:
                curses.endwin()
            except:
                pass


def main():
    """主函数"""
    try:
        os.system('chcp 65001 >nul')
        print("Starting Terminal Interface...")
        print("Make sure your terminal is at least 80x18 characters")
        time.sleep(1)

        terminal = StableTerminal()
        terminal.run()

        print("Terminal Interface exited.")

    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have installed: pip install windows-curses psutil")


if __name__ == "__main__":
    main()