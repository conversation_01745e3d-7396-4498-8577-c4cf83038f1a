# Server Resource Validation Configuration
# This file configures which resources to monitor and validation settings

# Target URL to validate
target_url: "https://parking.labs.sap.cn/mobile/parking/regist"

# Request settings
request_settings:
  timeout: 30
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  follow_redirects: true
  verify_ssl: true

# Resource types to monitor
resource_types:
  html: true
  css: true
  js: true
  
# Resource filtering (optional)
# If specified, only these resources will be monitored
include_patterns:
  - "*.css"
  - "*.js"
  - "*.html"

# Resources to exclude from monitoring
exclude_patterns:
  - "*analytics*"
  - "*tracking*"
  - "*ads*"

# Validation settings
validation:
  # Whether to check for new resources not in baseline
  check_new_resources: true
  
  # Whether to check for missing resources from baseline
  check_missing_resources: true
  
  # Whether to perform content hash comparison
  check_content_changes: true
  
  # Whether to save detailed diff files for changed resources
  save_diffs: false

# Notification settings (future enhancement)
notifications:
  enabled: false
  email:
    enabled: false
    recipients: []
  webhook:
    enabled: false
    url: ""

# Retention settings
retention:
  # Number of validation reports to keep
  max_reports: 10
  
  # Number of days to keep temporary files
  temp_file_retention_days: 7
