#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 blessed 的终端界面框架 - 支持 ANSI 颜色
通用终端UI框架，可为多个程序提供统一的界面，支持完整的ANSI颜色输出
"""

import os
import queue
import subprocess
import sys
import threading
import time
from pathlib import Path

# 安装 blessed 包的提示
try:
    from blessed import Terminal
except ImportError:
    print("❌ 需要安装 blessed 包:")
    print("pip install blessed")
    sys.exit(1)

# Project root directory
project_root = Path(__file__).resolve().parent.parent

# 把当前目录和项目根目录都加入PYTHONPATH
sys.path.insert(0, str(project_root))
sys.path.insert(0, os.path.dirname(__file__))

# 确保karpark包可以被找到
karpark_path = project_root / "karpark"
if karpark_path.exists():
    sys.path.insert(0, str(project_root))

# 设置 UTF-8 编码
if sys.platform == "win32":
    # 设置控制台编码为 UTF-8
    os.system('chcp 65001 >nul 2>&1')

# 强制启用颜色输出
os.environ['FORCE_COLOR'] = '1'
os.environ['COLORTERM'] = 'truecolor'
os.environ['TERM'] = 'xterm-256color'
os.environ['PYTHONIOENCODING'] = 'utf-8'


class BlessedTerminal:
    def __init__(self):
        self.term = Terminal()
        
        # 菜单选项
        self.menu_items = [
            "Start Interceptor",
            "Start Auto-registrar", 
            "Stop Interceptor",
            "Stop Auto-registrar",
            "Clear Interceptor Output",
            "Clear Auto-register Output",
            "Start Enrollment Manager",
            "Start Change Monitor",
            "Stop Enrollment Manager",
            "Stop Change Monitor",
            "System Info",
            "Exit"
        ]
        self.current_menu = 0

        # 子程序管理
        self.processes = {
            'interceptor': None,
            'auto-registrar': None,
            'enrollment-manager': None,
            'change-monitor': None
        }

        # 外部程序状态跟踪
        self.external_programs = {
            'enrollment-manager': False,
            'change-monitor': False
        }

        # 输出缓冲区
        self.output_buffers = {
            'overall-info': [],
            'interceptor': [],
            'auto-registrar': []
        }

        # 输出队列
        self.output_queues = {
            'overall-info': queue.Queue(),
            'interceptor': queue.Queue(),
            'auto-registrar': queue.Queue()
        }

        self.running = True
        self.need_redraw = True
        self.last_terminal_size = (0, 0)
        
        # 窗口布局参数
        self.layout = {}
        self.calculate_layout()

    def calculate_layout(self):
        """计算窗口布局"""
        height = self.term.height
        width = self.term.width

        if height < 30 or width < 120:
            raise Exception(f"Terminal too small. Need at least 120x30, got {width}x{height}")

        # 计算布局 - 菜单宽度保持不变，右侧区域变大
        menu_width = min(40, width // 2.5)
        right_width = width - menu_width - 1
        available_height = height - 4  # 标题栏1行 + 空白行1行 + 状态栏1行

        # OVERALL信息窗口 - 高度增加
        overall_height = max(8, available_height // 4)
        
        # 计算剩余空间
        remaining_height = available_height - overall_height
        output_height = remaining_height // 2
        output1_y = 2 + overall_height
        output2_height = remaining_height - output_height
        output2_y = output1_y + output_height

        self.layout = {
            'height': height,
            'width': width,
            'menu_width': int(menu_width),
            'right_width': int(right_width),
            'available_height': available_height,
            'overall_height': overall_height,
            'output_height': output_height,
            'output1_y': output1_y,
            'output2_height': output2_height,
            'output2_y': output2_y,
            'menu_x': 0,
            'menu_y': 2,
            'right_x': int(menu_width) + 1,
            'overall_y': 2,
            'status_y': height - 1
        }

    def safe_print_at(self, x, y, text, color=None):
        """安全地在指定位置打印文本"""
        try:
            if y >= 0 and y < self.term.height and x >= 0:
                # 计算可用宽度
                available_width = self.term.width - x
                if available_width > 0:
                    # 截断文本以适应可用宽度
                    display_text = text[:available_width]
                    
                    if color:
                        print(self.term.move(y, x) + color + display_text + self.term.normal, end='')
                    else:
                        print(self.term.move(y, x) + display_text, end='')
        except Exception:
            pass

    def draw_box(self, x, y, width, height, title="", color=None):
        """绘制边框"""
        try:
            box_color = color or self.term.white
            
            # 顶部边框
            top_line = "┌" + "─" * (width - 2) + "┐"
            if title:
                title_text = f" {title} "
                if len(title_text) < width - 2:
                    title_pos = 2
                    top_line = "┌─" + title_text + "─" * (width - len(title_text) - 4) + "┐"
            
            self.safe_print_at(x, y, top_line, box_color)
            
            # 侧边框
            for i in range(1, height - 1):
                self.safe_print_at(x, y + i, "│", box_color)
                self.safe_print_at(x + width - 1, y + i, "│", box_color)
            
            # 底部边框
            bottom_line = "└" + "─" * (width - 2) + "┘"
            self.safe_print_at(x, y + height - 1, bottom_line, box_color)
            
        except Exception:
            pass

    def clear_screen(self):
        """清屏"""
        print(self.term.clear, end='')

    def draw_title(self):
        """绘制标题栏"""
        title = " TERMINAL INTERFACE - BLESSED VERSION "
        title_color = self.term.white_on_blue
        
        # 清空标题行
        self.safe_print_at(0, 0, " " * self.term.width, title_color)
        
        # 居中显示标题
        title_x = max(0, (self.term.width - len(title)) // 2)
        self.safe_print_at(title_x, 0, title, title_color)

    def draw_menu(self):
        """绘制左侧菜单"""
        layout = self.layout
        
        # 绘制菜单边框
        self.draw_box(
            layout['menu_x'], 
            layout['menu_y'], 
            layout['menu_width'], 
            layout['available_height'],
            "CONTROL MENU",
            self.term.cyan
        )
        
        # 绘制菜单项
        for i, item in enumerate(self.menu_items):
            y = layout['menu_y'] + i + 3
            if y >= layout['menu_y'] + layout['available_height'] - 1:
                break
                
            if i == self.current_menu:
                # 高亮当前选中项
                self.safe_print_at(
                    layout['menu_x'] + 1, 
                    y, 
                    f" {item} ".ljust(layout['menu_width'] - 2),
                    self.term.black_on_white
                )
            else:
                # 普通菜单项
                self.safe_print_at(
                    layout['menu_x'] + 2,
                    y,
                    f" {item}",
                    self.term.white
                )

    def draw_output_window(self, x, y, width, height, title, buffer):
        """绘制输出窗口"""
        # 绘制边框
        self.draw_box(x, y, width, height, title, self.term.cyan)

        # 显示缓冲区内容
        max_lines = height - 3  # 减去边框和标题
        start_line = max(0, len(buffer) - max_lines)

        for i, line in enumerate(buffer[start_line:]):
            if i < max_lines:
                line_y = y + i + 2
                # 保留 ANSI 颜色代码
                self.safe_print_at(x + 2, line_y, line[:width-4])

    def draw_status(self):
        """绘制状态栏"""
        layout = self.layout

        prog1_status = "RUN" if self.processes['interceptor'] and self.processes['interceptor'].poll() is None else "STOP"
        prog2_status = "RUN" if self.processes['auto-registrar'] and self.processes['auto-registrar'].poll() is None else "STOP"

        latest_overall_msg = ""
        if self.output_buffers['overall-info']:
            last_msg = self.output_buffers['overall-info'][-1]
            if "] " in last_msg:
                latest_overall_msg = last_msg.split("] ", 1)[1]
            else:
                latest_overall_msg = last_msg

        controls = "UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit"
        programs = f"P1:{prog1_status} P2:{prog2_status}"

        if latest_overall_msg:
            status_text = f" {controls} | {programs} | {latest_overall_msg} "
        else:
            status_text = f" {controls} | {programs} "

        # 截断状态文本以适应屏幕宽度
        if len(status_text) > layout['width']:
            status_text = status_text[:layout['width'] - 3] + "..."

        # 清空状态行并显示状态
        self.safe_print_at(0, layout['status_y'], " " * layout['width'], self.term.cyan_on_black)
        self.safe_print_at(0, layout['status_y'], status_text, self.term.cyan_on_black)

    def update_outputs(self):
        """更新输出显示"""
        updated = False

        for program_name in ['overall-info', 'interceptor', 'auto-registrar']:
            try:
                while True:
                    line = self.output_queues[program_name].get_nowait()
                    timestamp = time.strftime("%H:%M:%S")
                    formatted_line = f"[{timestamp}] {line}"
                    self.output_buffers[program_name].append(formatted_line)

                    if len(self.output_buffers[program_name]) > 100:
                        self.output_buffers[program_name] = self.output_buffers[program_name][-50:]

                    updated = True
            except queue.Empty:
                pass

        if updated:
            self.draw_all_windows()

    def draw_all_windows(self):
        """绘制所有窗口"""
        layout = self.layout

        # 绘制输出窗口
        self.draw_output_window(
            layout['right_x'],
            layout['overall_y'],
            layout['right_width'],
            layout['overall_height'],
            "OVERALL INFO",
            self.output_buffers['overall-info']
        )

        self.draw_output_window(
            layout['right_x'],
            layout['output1_y'],
            layout['right_width'],
            layout['output_height'],
            "INTERCEPTOR",
            self.output_buffers['interceptor']
        )

        self.draw_output_window(
            layout['right_x'],
            layout['output2_y'],
            layout['right_width'],
            layout['output2_height'],
            "AUTO-REGISTRAR",
            self.output_buffers['auto-registrar']
        )

    def start_program(self, program_name: str):
        """启动程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.output_queues['overall-info'].put(f"{program_name} already running")
            return

        try:
            if program_name == 'interceptor':
                interceptor_path = project_root / "scripts/run_interceptor.py"
                cmd = [sys.executable, '-u', str(interceptor_path)]
            else:
                auto_registrar_path = project_root / "scripts/run_auto_registrar.py"
                cmd = [sys.executable, '-u', str(auto_registrar_path)]

            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            env['FORCE_COLOR'] = '1'
            env['COLORTERM'] = 'truecolor'
            env['TERM'] = 'xterm-256color'
            env['PYTHONIOENCODING'] = 'utf-8'
            # 确保子进程也能找到karpark包
            env['PYTHONPATH'] = str(project_root) + os.pathsep + env.get('PYTHONPATH', '')

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,
                env=env,
                encoding='utf-8',
                errors='replace',
                cwd=str(project_root)
            )
            self.processes[program_name] = process

            thread = threading.Thread(
                target=self.read_process_output,
                args=(program_name, process),
                daemon=True
            )
            thread.start()

            self.output_queues['overall-info'].put(f"{program_name} started successfully")

        except Exception as e:
            self.output_queues['overall-info'].put(f"{program_name} start failed: {str(e)}")

    def read_process_output(self, program_name: str, process):
        """读取子程序输出 - 保留ANSI颜色代码"""
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break

                # 确保字符串是正确编码的，但保留ANSI颜色代码
                try:
                    if isinstance(line, bytes):
                        line = line.decode('utf-8', errors='replace')
                    # 不去除ANSI颜色代码，直接传递
                    self.output_queues[program_name].put(line.strip())
                except UnicodeDecodeError as ue:
                    try:
                        line = line.decode('gbk', errors='replace')
                        self.output_queues[program_name].put(line.strip())
                    except:
                        self.output_queues[program_name].put(f"[Encoding Error: {str(ue)}]")

        except Exception as e:
            self.output_queues[program_name].put(f"Read error: {str(e)}")
        finally:
            if process.stdout:
                process.stdout.close()

    def stop_program(self, program_name: str):
        """停止程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            try:
                self.processes[program_name].terminate()
                self.processes[program_name].wait(timeout=3)
                self.output_queues['overall-info'].put(f"{program_name} stopped")
            except subprocess.TimeoutExpired:
                self.processes[program_name].kill()
                self.output_queues['overall-info'].put(f"{program_name} force killed")
            except Exception as e:
                self.output_queues['overall-info'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['overall-info'].put(f"{program_name} not running")

    def clear_output(self, program_name: str):
        """清空输出缓冲区"""
        self.output_buffers[program_name].clear()
        self.output_queues['overall-info'].put(f"{program_name} output cleared")

    def show_system_info(self):
        """显示系统信息"""
        try:
            import platform
            import psutil

            info = [
                f"System: {platform.system()} {platform.release()}",
                f"Python: {platform.python_version()}",
                f"CPU: {psutil.cpu_count()} cores",
                f"Memory: {psutil.virtual_memory().total // (1024**3)} GB",
                f"Terminal: {self.term.width}x{self.term.height}"
            ]

            for line in info:
                self.output_queues['overall-info'].put(line)

        except ImportError:
            self.output_queues['overall-info'].put("System info requires psutil package")
        except Exception as e:
            self.output_queues['overall-info'].put(f"System info error: {str(e)}")

    def start_program_new_window(self, program_name: str):
        """在新窗口启动程序"""
        if self.external_programs[program_name]:
            self.output_queues['overall-info'].put(f"{program_name} already running in separate window")
            return

        try:
            if program_name == 'enrollment-manager':
                # 执行 run_karpark_tools.py
                script_path = 'scripts/run_karpark_tools.py'
                title = "Karpark_Tools_Program_3"
            else:  # change-monitor
                # 执行 run_change_monitor.py
                script_path = 'scripts/run_change_monitor.py'
                title = "Monitor_Resources_Program_4"

            # 在新窗口启动实际的Python脚本
            cmd = f'start "{title}" cmd /k "python {script_path}"'

            process = subprocess.Popen(
                cmd,
                shell=True
            )

            # 标记外部程序为运行状态
            self.external_programs[program_name] = True
            self.output_queues['overall-info'].put(f"{program_name} started in new window: {script_path}")

        except Exception as e:
            self.output_queues['overall-info'].put(f"{program_name} start failed: {str(e)}")

    def stop_program_external(self, program_name: str):
        """停止外部窗口程序"""
        if self.external_programs[program_name]:
            try:
                # 使用taskkill命令终止Python进程
                if program_name == 'enrollment-manager':
                    title = "run_enrollment_manager.py"
                else:  # change-monitor
                    title = "run_change_monitor.py"

                cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                # 标记程序为停止状态
                self.external_programs[program_name] = False

                if result.returncode == 0:
                    self.output_queues['overall-info'].put(f"{program_name} window terminated successfully")
                else:
                    self.output_queues['overall-info'].put(f"{program_name} marked as stopped (window may need manual close)")

            except Exception as e:
                self.external_programs[program_name] = False
                self.output_queues['overall-info'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['overall-info'].put(f"{program_name} not running")

    def handle_menu_action(self):
        """处理菜单操作"""
        try:
            action = self.menu_items[self.current_menu]

            if action == "Start Interceptor":
                self.start_program('interceptor')
            elif action == "Stop Interceptor":
                self.stop_program('interceptor')
            elif action == "Clear Interceptor Output":
                self.clear_output('interceptor')
            elif action == "Start Auto-registrar":
                self.start_program('auto-registrar')
            elif action == "Stop Auto-registrar":
                self.stop_program('auto-registrar')
            elif action == "Clear Auto-register Output":
                self.clear_output('auto-registrar')
            elif action == "Start Enrollment Manager":
                self.start_program_new_window('enrollment-manager')
            elif action == "Stop Enrollment Manager":
                self.stop_program_external('enrollment-manager')
            elif action == "Start Change Monitor":
                self.start_program_new_window('change-monitor')
            elif action == "Stop Change Monitor":
                self.stop_program_external('change-monitor')
            elif action == "System Info":
                self.show_system_info()
            elif action == "Exit":
                self.running = False

        except Exception as e:
            self.output_queues['overall-info'].put(f"Menu error: {e}")

    def run(self):
        """主运行循环"""
        try:
            # 检查终端大小
            self.calculate_layout()

            # 隐藏光标
            print(self.term.hide_cursor, end='')

            # 初始绘制
            self.clear_screen()
            self.draw_title()
            self.draw_menu()
            self.draw_all_windows()
            self.draw_status()

            # 初始化消息
            self.output_queues['overall-info'].put("Terminal System Ready (Blessed Version)")
            self.output_queues['interceptor'].put("Interceptor Window Ready")
            self.output_queues['auto-registrar'].put("Auto-registrar Window Ready")

            last_menu = -1
            refresh_counter = 0

            with self.term.cbreak(), self.term.hidden_cursor():
                while self.running:
                    try:
                        # 检查终端大小变化
                        if (self.term.height, self.term.width) != self.last_terminal_size:
                            self.last_terminal_size = (self.term.height, self.term.width)
                            try:
                                self.calculate_layout()
                                self.clear_screen()
                                self.draw_title()
                                self.draw_menu()
                                self.draw_all_windows()
                                self.draw_status()
                                refresh_counter = 0
                                last_menu = -1
                            except Exception as e:
                                self.output_queues['overall-info'].put(f"Window resize error: {str(e)}")

                        # 更新输出
                        self.update_outputs()
                        refresh_counter += 1

                        # 重绘菜单（如果需要）
                        if last_menu != self.current_menu or refresh_counter % 10 == 0:
                            self.draw_menu()
                            last_menu = self.current_menu

                        # 定期重绘状态栏
                        if refresh_counter % 5 == 0:
                            self.draw_status()

                        # 刷新屏幕
                        sys.stdout.flush()

                        # 处理键盘输入（非阻塞）
                        key = self.term.inkey(timeout=0.2)

                        if key.code == self.term.KEY_UP:
                            self.current_menu = (self.current_menu - 1) % len(self.menu_items)
                        elif key.code == self.term.KEY_DOWN:
                            self.current_menu = (self.current_menu + 1) % len(self.menu_items)
                        elif key == '\n' or key == '\r':
                            self.handle_menu_action()
                        elif key.code == self.term.KEY_ESCAPE or key.lower() == 'q':
                            self.running = False
                        elif key.lower() == 'r':
                            # 强制刷新
                            self.clear_screen()
                            self.draw_title()
                            self.draw_menu()
                            self.draw_all_windows()
                            self.draw_status()

                    except KeyboardInterrupt:
                        self.running = False
                    except Exception as e:
                        try:
                            self.output_queues['overall-info'].put(f"Error: {e}")
                        except:
                            pass

        except Exception as e:
            print(f"Fatal error: {e}")
        finally:
            try:
                # 停止内部程序
                for process in [self.processes['interceptor'], self.processes['auto-registrar']]:
                    if process and process.poll() is None:
                        process.terminate()

                # 停止外部程序
                for program_name in ['enrollment-manager', 'change-monitor']:
                    if self.external_programs[program_name]:
                        try:
                            title = f"Program_{program_name[-1]}"
                            cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'
                            subprocess.run(cmd, shell=True, capture_output=True)
                        except:
                            pass
            except:
                pass

            # 恢复光标和清屏
            print(self.term.show_cursor + self.term.clear, end='')


def main():
    """主函数"""
    try:
        if sys.platform == "win32":
            os.system('chcp 65001 >nul')

        print("Starting Blessed Terminal Interface...")
        print("Make sure your terminal is at least 120x30 characters (double the original size)")
        print("Menu width remains the same, but output areas are larger")
        print("This version supports full ANSI color output!")
        time.sleep(2)

        terminal = BlessedTerminal()
        terminal.run()

        print("Blessed Terminal Interface exited.")

    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have installed: pip install blessed psutil")


if __name__ == "__main__":
    main()
