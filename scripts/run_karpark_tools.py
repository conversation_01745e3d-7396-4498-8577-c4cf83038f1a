#!/usr/bin/env python3
"""
KarPark 工具集统一入口点

这个脚本提供了一个统一的入口点来运行各种 KarPark 工具：
- enrollment: 登记管理器
- mitm: MITM 代理服务器
- validate: 服务器资源验证工具

使用方法:
    karpark-tools enrollment          # 启动登记管理器
    karpark-tools mitm [options]      # 启动 MITM 代理服务器
    karpark-tools validate [options]  # 启动资源验证工具
    karpark-tools --help              # 显示帮助信息
"""

import sys
import argparse
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

console = Console()


def show_main_menu():
    """显示主菜单"""
    console.print()
    
    # 创建标题面板
    title_panel = Panel.fit(
        "[bold cyan]🛠️  KarPark 工具集[/bold cyan]\n"
        "[dim]统一的工具管理入口点[/dim]",
        title="🎯 欢迎使用",
        border_style="blue",
        padding=(1, 2)
    )
    console.print(title_panel)
    
    # 创建工具表格
    table = Table(title="📋 可用工具", show_header=True, header_style="bold magenta")
    table.add_column("工具名称", style="cyan", width=15)
    table.add_column("命令", style="green", width=25)
    table.add_column("描述", style="white")
    
    table.add_row(
        "登记管理器", 
        "karpark-tools enrollment", 
        "交互式停车登记管理工具"
    )
    table.add_row(
        "MITM 代理", 
        "karpark-tools mitm [选项]", 
        "MITM 代理服务器启动器"
    )
    table.add_row(
        "资源验证", 
        "karpark-tools validate [选项]", 
        "服务器资源变化验证工具"
    )
    
    console.print(table)
    
    # 显示使用示例
    examples_panel = Panel(
        "[bold yellow]📝 使用示例:[/bold yellow]\n\n"
        "[green]# 启动登记管理器[/green]\n"
        "karpark-tools enrollment\n\n"
        "[green]# 启动 MITM 代理（前台模式）[/green]\n"
        "karpark-tools mitm\n\n"
        "[green]# 启动 MITM 代理（后台模式）[/green]\n"
        "karpark-tools mitm --no-wait\n\n"
        "[green]# 检查服务器资源变化[/green]\n"
        "karpark-tools validate check\n\n"
        "[green]# 显示特定工具的帮助[/green]\n"
        "karpark-tools mitm --help\n"
        "karpark-tools validate --help",
        title="💡 快速开始",
        border_style="yellow"
    )
    console.print(examples_panel)


def run_enrollment_manager(args):
    """运行登记管理器"""
    try:
        from karpark.cli import cli
        
        # Show stylish welcome banner
        welcome_text = """[bold cyan]🚗 Karpark 登记管理器[/bold cyan]

[dim]用于管理停车登记的交互式 CLI 工具[/dim]
[dim]由 Click & Rich 提供最佳用户体验[/dim]"""

        banner = Panel.fit(
            welcome_text,
            title="🎯 欢迎使用",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(banner)
        console.print()

        # 启动交互式菜单
        cli(['--interactive'])
        
    except ImportError as e:
        console.print(f"[red]❌ 导入错误: {e}[/red]")
        console.print("[yellow]请确保您在项目根目录下运行此脚本。[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ 错误: {e}[/red]")
        sys.exit(1)


def run_mitm_server(args):
    """运行 MITM 代理服务器"""
    try:
        from karpark.mitm.launch_mitm_server import MitmdumpLauncher
        
        console.print("🚀 Karpark MITM 代理服务器启动器")
        console.print("=" * 40)

        # Create launcher instance
        launcher = MitmdumpLauncher()

        # Handle dependency installation
        if args.install_deps:
            python_exe = launcher.find_python_executable()
            console.print(f"📦 使用 {python_exe} 安装依赖...")
            import subprocess
            result = subprocess.run([
                python_exe, "-m", "pip", "install", "mitmproxy", "coloredlogs", "psutil"
            ])
            if result.returncode == 0:
                console.print("✅ 依赖安装成功!")
            else:
                console.print("❌ 依赖安装失败!")
                sys.exit(1)
            return

        # Handle restart command
        if args.restart:
            success = launcher.restart()
            sys.exit(0 if success else 1)

        # Handle stop command
        if args.stop:
            success = launcher.stop()
            sys.exit(0 if success else 1)

        # Handle status command
        if args.status:
            launcher.show_status()
            return

        # Handle email monitor only
        if args.email_monitor:
            launcher.start_email_monitoring()
            return

        # Set system python if requested
        if args.system_python:
            launcher.set_system_python()

        # Launch the server
        try:
            success = launcher.run(
                wait=not args.no_wait,
                test_mode=args.test_mode
            )

            # Auto-start email monitoring if successful and enabled
            if success:
                from karpark.common.config import MitmproxyConfig
                if MitmproxyConfig.EmailMonitor.enabled:
                    launcher.start_email_monitoring()
                    if not args.no_wait:
                        console.print("📧 邮件监控自动启动")

            if not success:
                console.print("❌ 启动 mitmproxy 服务器失败!")
                sys.exit(1)

        except KeyboardInterrupt:
            console.print("\n🛑 用户中断服务器启动")
            sys.exit(0)
        except Exception as e:
            console.print(f"❌ 意外错误: {e}")
            sys.exit(1)
            
    except ImportError as e:
        console.print(f"[red]❌ 导入错误: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ 错误: {e}[/red]")
        sys.exit(1)


def run_validate_resources(args):
    """运行资源验证工具"""
    try:
        # 导入资源验证工具的 CLI
        from scripts.run_monitor_resources import cli
        
        # 构建参数列表传递给 click CLI
        click_args = []
        
        # 添加全局选项
        if args.interactive:
            click_args.append('--interactive')
        if args.url:
            click_args.extend(['--url', args.url])
        if args.cookie:
            click_args.extend(['--cookie', args.cookie])
            
        # 添加子命令
        if hasattr(args, 'validate_command') and args.validate_command:
            click_args.append(args.validate_command)
            
        # 调用 click CLI
        cli(click_args, standalone_mode=False)
        
    except ImportError as e:
        console.print(f"[red]❌ 导入错误: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ 错误: {e}[/red]")
        sys.exit(1)


def main():
    """主入口点"""
    parser = argparse.ArgumentParser(
        description='🛠️ KarPark 工具集统一入口点',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
工具说明:
  enrollment    启动登记管理器
  mitm          启动 MITM 代理服务器
  validate      启动服务器资源验证工具

使用示例:
  karpark-tools enrollment                    # 启动登记管理器
  karpark-tools mitm                          # 启动 MITM 代理（前台）
  karpark-tools mitm --no-wait               # 启动 MITM 代理（后台）
  karpark-tools validate check               # 检查资源变化
  karpark-tools validate --interactive       # 交互式资源验证
        """
    )
    
    subparsers = parser.add_subparsers(dest='tool', help='选择要使用的工具')
    
    # 登记管理器子命令
    enrollment_parser = subparsers.add_parser('enrollment', help='启动登记管理器')
    
    # MITM 代理子命令
    mitm_parser = subparsers.add_parser('mitm', help='启动 MITM 代理服务器')
    mitm_parser.add_argument('--no-wait', action='store_true', help='后台模式启动')
    mitm_parser.add_argument('--system-python', action='store_true', help='使用系统 Python（覆盖配置）')
    mitm_parser.add_argument('--test-mode', action='store_true', help='测试模式（不加载脚本）')
    mitm_parser.add_argument('--install-deps', action='store_true', help='安装依赖')
    mitm_parser.add_argument('--restart', action='store_true', help='重启代理')
    mitm_parser.add_argument('--stop', action='store_true', help='停止代理')
    mitm_parser.add_argument('--status', action='store_true', help='显示状态')
    mitm_parser.add_argument('--email-monitor', action='store_true', help='仅启动邮件监控')
    
    # 资源验证子命令
    validate_parser = subparsers.add_parser('validate', help='启动服务器资源验证工具')
    validate_parser.add_argument('--interactive', '-i', action='store_true', help='交互式菜单模式')
    validate_parser.add_argument('--url', help='要验证的目标 URL')
    validate_parser.add_argument('--cookie', help='访问受保护资源的认证 cookie')
    validate_parser.add_argument('validate_command', nargs='?', choices=['init', 'check', 'report', 'status'], 
                                help='验证命令 (init/check/report/status)')
    
    args = parser.parse_args()
    
    # 如果没有指定工具，显示主菜单
    if not args.tool:
        show_main_menu()
        return
    
    # 根据选择的工具执行相应功能
    if args.tool == 'enrollment':
        run_enrollment_manager(args)
    elif args.tool == 'mitm':
        run_mitm_server(args)
    elif args.tool == 'validate':
        run_validate_resources(args)
    else:
        console.print(f"[red]❌ 未知工具: {args.tool}[/red]")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
