#!/usr/bin/env python3
"""
Server Resource Validation CLI Tool

This tool validates that server-side resources (HTML, CSS, JS) haven't changed
by downloading them and comparing with a baseline version.

Features:
1. Download and save baseline resources with proper authentication
2. Compare current resources against baseline
3. Generate detailed validation reports
4. Interactive menu and command-line interface
"""

import sys
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urljoin, urlparse
from datetime import datetime, timezone

import requests
from bs4 import BeautifulSoup
import click

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from karpark.common.paths import paths
from karpark.colorlog import logger
from karpark.registrar.entities.headers import create_headers
from karpark.utils.curses_ui import console, Table, Panel, Prompt, Confirm

# Constants
TARGET_URL = "https://parking.labs.sap.cn/mobile/parking/regist"
BASELINE_DIR = Path(paths.data_dir) / "server_resources" / "baseline"
TEMP_DIR = Path(paths.data_dir) / "server_resources" / "temp"
REPORT_FILE = Path(paths.data_dir) / "server_resources" / "validation_report.json"

class ResourceType:
    """Resource type constants"""
    HTML = "html"
    CSS = "css"
    JS = "js"

class ResourceValidator:
    """Main class for validating server resources"""

    def __init__(self, cookie: Optional = None):
        self.session = requests.Session()
        self.cookie = cookie

        # Use proper headers from the project's header configuration
        if cookie:
            headers = create_headers("GET", cookie)
            self.session.headers.update(headers)
            logger.info("Authentication headers configured with cookie")
        else:
            # Use basic headers without cookie
            headers = create_headers("GET", "")
            # Remove empty cookie header
            headers.pop('Cookie', None)
            self.session.headers.update(headers)
            logger.info("Basic headers configured without authentication")

        self.baseline_resources: Dict = {}
        self.current_resources: Dict = {}

    def ensure_directories(self):
        """Ensure required directories exist"""
        BASELINE_DIR.mkdir(parents=True, exist_ok=True)
        TEMP_DIR.mkdir(parents=True, exist_ok=True)
        REPORT_FILE.parent.mkdir(parents=True, exist_ok=True)
        
    def download_resource(self, url: str, save_path: Path) -> Tuple:
        """Download a resource and save to file

        Args:
            url: Resource URL to download
            save_path: Path to save the downloaded content

        Returns:
            Tuple of (success, content_hash)
        """
        try:
            logger.info(f"Downloading: {url}")
            response = self.session.get(url, timeout=30, allow_redirects=True)

            # Check for authentication redirects
            if self._is_auth_redirect(response):
                logger.error(f"Authentication required for {url}")
                logger.error(f"Redirected to: {response.url}")
                return False, ""

            response.raise_for_status()

            # Check if we got a valid response
            if not self._is_valid_content(response, url):
                logger.error(f"Invalid content received from {url}")
                return False, ""

            # Save content to file
            save_path.parent.mkdir(parents=True, exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(response.text)

            # Calculate content hash
            content_hash = hashlib.sha256(response.text.encode('utf-8')).hexdigest()

            logger.info(f"Downloaded successfully: {save_path}")
            return True, content_hash

        except Exception as e:
            logger.error(f"Failed to download {url}: {e}")
            return False, ""

    def _is_auth_redirect(self, response) -> bool:
        """Check if response indicates authentication redirect"""
        # Check for WeChat OAuth redirect
        if "open.weixin.qq.com" in response.url:
            return True

        # Check for other common auth redirects
        auth_indicators = ['login', 'auth', 'oauth', 'sso', 'weixin', 'wechat']

        return any(indicator in response.url.lower() for indicator in auth_indicators)

    def _is_valid_content(self, response, original_url: str) -> bool:
        """Check if response contains valid content (not error pages)"""
        content = response.text.lower()

        # Check for common error indicators
        error_indicators = ['login', 'wechat', 'unauthorized', 'error', '登录', '微信']

        if any(indicator in content for indicator in error_indicators):
            return False

        # For HTML pages, check if we have actual content
        if original_url.endswith(('.html', '/regist')) or 'html' in response.headers.get('content-type', ''):
            # Should have basic HTML structure
            if not ('<html' in content or '<!doctype' in content):
                return False

        return True
    
    def extract_resource_urls(self, html_content: str, base_url: str) -> Dict[str, List[str]]:
        """Extract CSS and JS URLs from HTML content
        
        Args:
            html_content: HTML content to parse
            base_url: Base URL for resolving relative URLs
            
        Returns:
            Dictionary with 'css' and 'js' lists of URLs
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        resources = {ResourceType.CSS: [], ResourceType.JS: []}  # type: Dict[str, List[str]]
        
        # Extract CSS links
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                resources[ResourceType.CSS].append(full_url)
                
        # Extract JS scripts
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            if src:
                full_url = urljoin(base_url, src)
                resources[ResourceType.JS].append(full_url)
                
        return resources
    
    def get_resource_filename(self, url: str, resource_type: str) -> str:
        """Generate a filename for a resource based on its URL
        
        Args:
            url: Resource URL
            resource_type: Type of resource (css, js, html)
            
        Returns:
            Generated filename
        """
        parsed = urlparse(url)
        path_parts = parsed.path.strip('/').split('/')
        
        if path_parts and path_parts:
            filename = path_parts
            # Remove query parameters
            if '?' in filename:
                filename = filename.split('?')
        else:
            filename = f"index.{resource_type}"
            
        # Ensure proper extension
        if not filename.endswith(f'.{resource_type}'):
            filename += f'.{resource_type}'
            
        return filename
    
    def download_all_resources(self, target_dir: Path) -> Dict:
        """Download all resources and return metadata
        
        Args:
            target_dir: Directory to save resources
            
        Returns:
            Dictionary with resource metadata
        """
        resources_metadata = {}
        
        console.print("Downloading main page...")

        # Download main HTML page
        html_path = target_dir / "register.html"
        success, html_hash = self.download_resource(TARGET_URL, html_path)

        if not success:
            logger.error("Failed to download main HTML page")
            return {}

        resources_metadata = {
            "url": TARGET_URL,
            "type": ResourceType.HTML,
            "hash": html_hash,
            "path": str(html_path.relative_to(target_dir)),
            "downloaded_at": datetime.now(timezone.utc).isoformat()
        }

        # Read HTML content and extract resource URLs
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        resource_urls = self.extract_resource_urls(html_content, TARGET_URL)
        total_resources = len(resource_urls) + len(resource_urls)

        console.print(f"Found {total_resources} additional resources")

        # Download CSS files
        for css_url in resource_urls[ResourceType.CSS]:
            filename = self.get_resource_filename(css_url, ResourceType.CSS)
            css_path = target_dir / "css" / filename
            success, css_hash = self.download_resource(css_url, css_path)

            if success:
                resources_metadata[f"css/{filename}"] = {
                    "url": css_url,
                    "type": ResourceType.CSS,
                    "hash": css_hash,
                    "path": str(css_path.relative_to(target_dir)),
                    "downloaded_at": datetime.now(timezone.utc).isoformat()
                }

        # Download JS files
        for js_url in resource_urls[ResourceType.JS]:
            filename = self.get_resource_filename(js_url, ResourceType.JS)
            js_path = target_dir / "js" / filename
            success, js_hash = self.download_resource(js_url, js_path)

            if success:
                resources_metadata[f"js/{filename}"] = {
                    "url": js_url,
                    "type": ResourceType.JS,
                        "hash": js_hash,
                        "path": str(js_path.relative_to(target_dir)),
                        "downloaded_at": datetime.now(timezone.utc).isoformat()
                    }
        
        return resources_metadata
    
    def save_metadata(self, metadata: Dict, metadata_file: Path):
        """Save resource metadata to JSON file"""
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            logger.info(f"Metadata saved to: {metadata_file}")
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def load_metadata(self, metadata_file: Path) -> Dict:
        """Load resource metadata from JSON file"""
        try:
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Failed to load metadata: {e}")
            return {}

    def initialize_baseline(self) -> bool:
        """Download and save baseline resources

        Returns:
            True if successful, False otherwise
        """
        console.print(Panel(
            "Initializing Baseline Resources\n"
            f"Target URL: {TARGET_URL}\n"
            f"Baseline Directory: {BASELINE_DIR}",
            title="🔄 Resource Download",
            border_style="blue"
        ))

        self.ensure_directories()

        # Validate cookie if provided
        if self.cookie:
            is_valid, error_msg = self.validate_cookie()
            if not is_valid:
                console.print(f"❌ Cookie validation failed: {error_msg}")
                console.print("💡 Suggestions:")
                console.print("  1. Update your cookie using the cookie management tool")
                console.print("  2. Use Fiddler Classic to capture a fresh cookie from WeChat")
                console.print("  3. Ensure you're accessing the parking system through WeChat mini-program")
                return False
            else:
                console.print(f"✅ Cookie validation successful")

        # Download all resources
        metadata = self.download_all_resources(BASELINE_DIR)

        if not metadata:
            console.print("❌ Failed to download baseline resources")
            self._show_troubleshooting_tips()
            return False

        # Save metadata
        metadata_file = BASELINE_DIR / "metadata.json"
        self.save_metadata(metadata, metadata_file)

        console.print(f"✅ Successfully downloaded {len(metadata)} resources")
        console.print(f"Baseline saved to: {BASELINE_DIR}")

        return True

    def compare_resources(self) -> Dict:
        """Compare current resources with baseline

        Returns:
            Comparison report dictionary
        """
        console.print(Panel(
            "Validating Server Resources\n"
            f"Comparing against baseline in: {BASELINE_DIR}",
            title="🔍 Resource Validation",
            border_style="yellow"
        ))

        self.ensure_directories()

        # Load baseline metadata
        baseline_metadata_file = BASELINE_DIR / "metadata.json"
        baseline_metadata = self.load_metadata(baseline_metadata_file)

        if not baseline_metadata:
            console.print("❌ No baseline found. Run with --init first.")
            return {"error": "No baseline found"}

        # Validate cookie if provided
        if self.cookie:
            is_valid, error_msg = self.validate_cookie()
            if not is_valid:
                console.print(f"❌ Cookie validation failed: {error_msg}")
                self._show_troubleshooting_tips()
                return {"error": f"Cookie validation failed: {error_msg}"}
            else:
                console.print(f"✅ Cookie validation successful")

        # Download current resources
        current_metadata = self.download_all_resources(TEMP_DIR)

        if not current_metadata:
            console.print("❌ Failed to download current resources")
            self._show_troubleshooting_tips()
            return {"error": "Failed to download current resources"}

        # Compare resources
        report = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "baseline_count": len(baseline_metadata),
            "current_count": len(current_metadata),
            "changes": [],
            "new_resources": [],
            "missing_resources": [],
            "unchanged_resources": []
        }

        # Check for changes and missing resources
        for resource_name, baseline_info in baseline_metadata.items():
            if resource_name in current_metadata:
                current_info = current_metadata
                if baseline_info != current_info:
                    report.append({
                        "resource": resource_name,
                        "type": baseline_info,
                        "url": baseline_info,
                        "baseline_hash": baseline_info,
                        "current_hash": current_info
                    })
                else:
                    report.append(resource_name)
            else:
                report.append({
                    "resource": resource_name,
                    "type": baseline_info,
                    "url": baseline_info
                })

        # Check for new resources
        for resource_name, current_info in current_metadata.items():
            if resource_name not in baseline_metadata:
                report.append({
                    "resource": resource_name,
                    "type": current_info,
                    "url": current_info,
                    "hash": current_info
                })

        # Save comparison report
        self.save_metadata(report, REPORT_FILE)

        # Auto-clean temporary files after comparison
        self._clean_temp_files()

        return report

    def display_report(self, report: Dict):
        """Display validation report in a formatted way"""
        if "error" in report:
            console.print(f"❌ Error: {report}")
            return

        # Summary panel
        summary_text = (
            f"Baseline Resources: {report}\n"
            f"Current Resources: {report}\n"
            f"Changed: {len(report)}\n"
            f"New: {len(report)}\n"
            f"Missing: {len(report)}\n"
            f"Unchanged: {len(report)}"
        )

        panel_style = "green" if len(report) == 0 and len(report) == 0 else "red"
        status_icon = "✅" if panel_style == "green" else "⚠️"

        console.print(Panel(
            summary_text,
            title=f"{status_icon} Validation Summary",
            border_style=panel_style
        ))

        # Changes table
        if report:
            console.print("\n🔄 Changed Resources:")
            changes_table = Table(show_header=True, header_style="bold red")
            changes_table.add_column("Resource", style="cyan")
            changes_table.add_column("Type", style="magenta")
            changes_table.add_column("Baseline Hash", style="dim")
            changes_table.add_column("Current Hash", style="dim")

            for change in report:
                changes_table.add_row(
                    change,
                    change.upper(),
                    change + "...",
                    change + "..."
                )

            console.print(changes_table)

        # New resources table
        if report:
            console.print("\n➕ New Resources:")
            new_table = Table(show_header=True, header_style="bold blue")
            new_table.add_column("Resource", style="cyan")
            new_table.add_column("Type", style="magenta")
            new_table.add_column("URL", style="dim")

            for new_resource in report:
                new_table.add_row(
                    new_resource,
                    new_resource.upper(),
                    new_resource
                )

            console.print(new_table)

        # Missing resources table
        if report:
            console.print("\n➖ Missing Resources:")
            missing_table = Table(show_header=True, header_style="bold yellow")
            missing_table.add_column("Resource", style="cyan")
            missing_table.add_column("Type", style="magenta")
            missing_table.add_column("URL", style="dim")

            for missing_resource in report:
                missing_table.add_row(
                    missing_resource,
                    missing_resource.upper(),
                    missing_resource
                )

            console.print(missing_table)

        console.print(f"\nReport saved to: {REPORT_FILE}")



    def update_cookie(self, cookie: str):
        """Update session cookie and headers"""
        self.cookie = cookie

        if cookie:
            # Update headers with new cookie
            headers = create_headers("GET", cookie)
            self.session.headers.update(headers)
            logger.info("Cookie and headers updated in session")
        else:
            # Remove cookie and update headers
            headers = create_headers("GET", "")
            headers.pop('Cookie', None)
            self.session.headers.update(headers)
            self.cookie = None
            logger.info("Cookie removed and headers updated")

    def validate_cookie(self) -> Tuple:
        """Validate if the current cookie is working

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.cookie:
            return False, "No cookie provided"

        try:
            logger.info("Validating cookie...")
            response = self.session.get(TARGET_URL, timeout=15, allow_redirects=True)

            # Check for authentication redirects
            if self._is_auth_redirect(response):
                return False, f"Cookie expired or invalid - redirected to: {response.url}"

            # Check response status
            if response.status_code == 401:
                return False, "Cookie authentication failed (401 Unauthorized)"
            elif response.status_code == 403:
                return False, "Access forbidden with current cookie (403 Forbidden)"
            elif response.status_code != 200:
                return False, f"Unexpected response status: {response.status_code}"

            # Check if we got valid content
            if not self._is_valid_content(response, TARGET_URL):
                return False, "Cookie appears invalid - received error page content"

            logger.info("Cookie validation successful")
            return True, "Cookie is valid"

        except Exception as e:
            return False, f"Cookie validation failed: {e}"

    def _show_troubleshooting_tips(self):
        """Show troubleshooting tips for authentication issues"""
        console.print("\n🔧 Troubleshooting Tips:")
        console.print()

        tips_panel = Panel(
            "Authentication Issues:\n"
            "• The parking system requires WeChat authentication\n"
            "• Cookies expire and need to be refreshed regularly\n"
            "• Access must be through WeChat mini-program\n\n"
            "How to get a fresh cookie:\n"
            "1. Install and start Fiddler Classic\n"
            "2. Configure phone WiFi proxy (IP: your computer IP, Port: 8888)\n"
            "3. Open parking registration in WeChat mini-program\n"
            "4. Find request to 'parking.labs.sap.cn' in Fiddler\n"
            "5. Copy the complete 'Cookie' header value\n"
            "6. Use the cookie management tool to update\n\n"
            "Alternative approaches:\n"
            "• Try running validation during active WeChat session\n"
            "• Check if the parking system is accessible\n"
            "• Verify network connectivity to parking.labs.sap.cn",
            title="💡 Help",
            border_style="yellow"
        )
        console.print(tips_panel)

    def _clean_temp_files(self):
        """Clean temporary files automatically after operations"""
        try:
            import shutil
            if TEMP_DIR.exists():
                shutil.rmtree(TEMP_DIR)
                logger.info(f"Auto-cleaned temporary files: {TEMP_DIR}")
        except Exception as e:
            logger.error(f"Failed to auto-clean temporary files: {e}")


def load_and_display_report():
    """Load and display the latest validation report"""
    if not REPORT_FILE.exists():
        console.print("❌ No validation report found. Run validation first.")
        return

    try:
        with open(REPORT_FILE, 'r', encoding='utf-8') as f:
            report = json.load(f)

        validator = ResourceValidator()
        console.print(f"Loading report from: {REPORT_FILE}\n")
        validator.display_report(report)

    except Exception as e:
        console.print(f"❌ Failed to load report: {e}")


# Click command group
@click.group(invoke_without_command=True)
@click.pass_context
@click.option('--interactive', '-i', is_flag=True, help='Start interactive menu mode')
@click.option('--url', default=TARGET_URL, help='Target URL to validate')
@click.option('--cookie', help='Authentication cookie for accessing protected resources')
def cli(ctx, interactive, url, cookie):
    """🔍 Server Resource Validation Tool

    A stylish CLI tool for validating server-side resources with rich formatting and interactive features.
    """
    global TARGET_URL
    TARGET_URL = url

    # Handle cookie authentication
    if not cookie:
        console.print("⚠️  No cookie provided. Authentication may be required for some operations.")

    if ctx.invoked_subcommand is None:
        if interactive:
            show_interactive_menu(cookie)
        else:
            # Show welcome message and available commands
            welcome_panel = Panel.fit(
                "🔍 Welcome to Server Resource Validator!\n\n"
                "Choose your preferred way to interact:\n"
                "• --interactive or -i - Interactive menu mode\n"
                "• <command> - Direct command execution\n\n"
                "Available commands:\n"
                "• init - Download baseline resources (with cookie input)\n"
                "• check - Check for resource changes (with cookie input)\n"
                "• report - Show validation report\n"
                "• status - Check cookie status\n\n"
                "Use --help to see detailed options for each command",
                title="🎯 Quick Start",
                border_style="blue"
            )
            console.print(welcome_panel)


# Click subcommands
@cli.command()
@click.option('--url', default=TARGET_URL, help='Target URL to validate')
@click.option('--cookie', help='Authentication cookie for accessing protected resources')
def init(url, cookie):
    """Download and save baseline resources"""
    global TARGET_URL
    TARGET_URL = url

    console.print("🔄 Initialize Baseline Resources\n")

    # Handle cookie
    if not cookie:
        console.print("⚠️  No cookie provided. Some resources may not be accessible.")

    validator = ResourceValidator(cookie)
    success = validator.initialize_baseline()

    if success:
        console.print("\n🎉 Baseline initialization completed successfully!")
        console.print("You can now run validation checks with 'check' command")
    else:
        console.print("\n❌ Baseline initialization failed!")
        sys.exit(1)


@cli.command()
@click.option('--url', default=TARGET_URL, help='Target URL to validate')
@click.option('--cookie', help='Authentication cookie for accessing protected resources')
def check(url, cookie):
    """Check current resources against baseline"""
    global TARGET_URL
    TARGET_URL = url

    console.print("🔍 Check Resource Changes\n")

    # Handle cookie
    if not cookie:
        console.print("⚠️  No cookie provided. Authentication may be required.")

    validator = ResourceValidator(cookie)
    report = validator.compare_resources()
    validator.display_report(report)

    # Exit with error code if changes detected
    if "error" not in report:
        has_changes = len(report) > 0 or len(report) > 0
        if has_changes:
            console.print("\n⚠️  Changes detected in server resources!")
            sys.exit(1)
        else:
            console.print("\n✅ No changes detected. All resources match baseline.")


@cli.command()
def report():
    """Display the latest validation report"""
    console.print("📊 Validation Report\n")
    load_and_display_report()





@cli.command()
@click.option('--cookie', help='Authentication cookie to check (optional)')
def status(cookie):
    """Check authentication cookie status"""
    console.print("🔐 Cookie Status Check\n")

    # Check if cookie is provided
    if not cookie:
        console.print("⚠️  No cookie provided")
        console.print("Please provide a cookie using the --cookie option")
        sys.exit(1)

    # Show masked cookie
    masked = cookie + "..." + cookie if len(cookie) > 20 else cookie
    console.print(f"Current cookie: {masked}")

    # Validate cookie
    validator = ResourceValidator(cookie)
    is_valid, message = validator.validate_cookie()

    if is_valid:
        console.print(f"✅ {message}")
        console.print("Cookie is working and can access the parking system")
    else:
        console.print(f"❌ {message}")
        console.print("💡 Cookie may be expired, please get a fresh one from WeChat")
        sys.exit(1)


def show_interactive_menu(cookie: Optional = None):
    """Show interactive menu with arrow key navigation."""

    menu_options = [
        ("Initialize baseline resources", "init"),
        ("Check resource changes", "check"),
        ("Show validation report", "report"),
        ("Check cookie status", "status"),
        ("Exit", "exit")
    ]

    while True:
        console.clear()

        # Show welcome banner
        welcome_panel = Panel.fit(
            "🔍 Server Resource Validator\n\n"
            "Use number keys to select, or type the number",
            title="🎯 Interactive Menu",
            border_style="blue"
        )
        console.print(welcome_panel)
        console.print()

        # Show menu options
        menu_table = Table(show_header=False, box=None, padding=(0, 2))
        menu_table.add_column("Option", style="bold")
        menu_table.add_column("Description", style="dim")

        for i, (description, _) in enumerate(menu_options, 1):
            menu_table.add_row(f"{i}.", description)

        console.print(menu_table)
        console.print()

        # Get user choice
        try:
            choice = Prompt.ask("Select an option", default="5")

            choice_idx = int(choice) - 1
            _, command = menu_options[choice_idx]

            if command == "exit":
                console.print("👋 Goodbye!")
                break
            elif command == "help":
                show_help_info()
            elif command == "init":
                run_init_command(cookie)
            elif command == "check":
                run_check_command(cookie)
            elif command == "report":
                run_report_command()
            elif command == "info":
                run_info_command()

        except (ValueError, KeyboardInterrupt):
            console.print("\n👋 Goodbye!")
            break
        except Exception as e:
            console.print(f"❌ Error: {e}")
            console.input("\nPress Enter to continue...")


def run_init_command(cookie: Optional):
    """Run initialization command"""
    console.print("\n🔄 Initialize Baseline Resources")

    if Confirm.ask("This will overwrite existing baseline. Continue?", default=True):
        # Get cookie input
        current_cookie = get_cookie_input(cookie)
        if not current_cookie:
            console.print("❌ Cookie is required for baseline initialization")
            console.input("\nPress Enter to continue...")
            return

        # Display cookie status
        display_cookie_status(current_cookie)

        validator = ResourceValidator(current_cookie)
        success = validator.initialize_baseline()
        if success:
            console.print("\n✅ Baseline initialized successfully!")
        else:
            console.print("\n❌ Failed to initialize baseline!")

    console.input("\nPress Enter to continue...")


def run_check_command(cookie: Optional):
    """Run check command with cookie input and validation"""
    console.print("\n🔍 Check Resource Changes")

    # Get cookie input
    current_cookie = get_cookie_input(cookie)
    if not current_cookie:
        console.print("❌ Cookie is required for resource validation")
        console.input("\nPress Enter to continue...")
        return

    # Display cookie status
    display_cookie_status(current_cookie)

    # Proceed with validation
    validator = ResourceValidator(current_cookie)
    report = validator.compare_resources()
    validator.display_report(report)

    console.input("\nPress Enter to continue...")


def run_report_command():
    """Run report command"""
    console.print("\n📊 Validation Report")
    load_and_display_report()
    console.input("\nPress Enter to continue...")


def run_info_command():
    """Run info command"""
    console.print("\n📋 Baseline Information")
    show_baseline_info()
    console.input("\nPress Enter to continue...")


def get_cookie_input(existing_cookie: Optional) -> Optional:
    """Get cookie input from user"""
    # Get new cookie input directly
    console.print("\nPlease paste your Cookie (obtained from Fiddler Classic):")
    console.print("Tip: Right-click to paste, or use Ctrl+Shift+V")

    try:
        cookie = console.input("Cookie: ").strip()
        if cookie and "=" in cookie and len(cookie) > 10:
            console.print("✅ Cookie accepted")
            return cookie
        else:
            console.print("❌ Invalid cookie format")
            return None
    except (KeyboardInterrupt, EOFError):
        console.print("\nOperation cancelled")
        return None


def display_cookie_status(cookie: str):
    """Display cookie status information"""
    console.print("\n🔐 Cookie Status")

    # Show masked cookie
    masked = cookie + "..." + cookie if len(cookie) > 20 else cookie
    console.print(f"Cookie: {masked}")

    # Validate cookie
    validator = ResourceValidator(cookie)
    is_valid, message = validator.validate_cookie()

    if is_valid:
        console.print(f"✅ {message}")
    else:
        console.print(f"❌ {message}")
        console.print("💡 Cookie may be expired, please get a fresh one from WeChat")

    console.print()  # Add spacing





def show_help_info():
    """Show help information"""
    console.print("\n❓ Help Information")

    help_panel = Panel(
        "Server Resource Validator Help\n\n"
        "Commands:\n"
        "• Initialize baseline - Download current resources as reference\n"
        "• Check changes - Compare current vs baseline resources\n"
        "• Show report - Display detailed validation results\n"
        "• Baseline info - Show information about saved baseline\n\n"
        "Authentication:\n"
        "• Cookie input is integrated into init and check commands\n"
        "• Cookie status is displayed automatically during validation\n"
        "• Cookie is requested each time (not saved)\n\n"
        "File Locations:\n"
        f"• Baseline: {BASELINE_DIR}\n"
        f"• Reports: {REPORT_FILE}",
        title="📖 Help",
        border_style="green"
    )
    console.print(help_panel)
    console.input("\nPress Enter to continue...")


def show_baseline_info():
    """Show information about the current baseline"""
    baseline_metadata_file = BASELINE_DIR / "metadata.json"

    if not baseline_metadata_file.exists():
        console.print("⚠️  No baseline found.")
        console.print("Run initialization first to create a baseline.")
        return

    try:
        with open(baseline_metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # Get the earliest download time
        download_times = [
            datetime.fromisoformat(info.get('downloaded_at'))
            for info in metadata.values()
            if 'downloaded_at' in info
        ]
        earliest_time = min(download_times).strftime('%Y-%m-%d %H:%M:%S') if download_times else 'Unknown'

        console.print(Panel(
            f"Baseline Information\n\n"
            f"Location: {BASELINE_DIR}\n"
            f"Resources: {len(metadata)}\n"
            f"Created: {earliest_time}",
            title="📋 Baseline Status",
            border_style="blue"
        ))

        # Show resource breakdown
        resource_types = {}
        for info in metadata.values():
            res_type = info.get('type', 'unknown')
            resource_types[res_type] = resource_types.get(res_type, 0) + 1

        if resource_types:
            console.print("\nResource Breakdown:")
            for res_type, count in resource_types.items():
                console.print(f"  {res_type.upper()}: {count}")

    except Exception as e:
        console.print(f"❌ Failed to load baseline info: {e}")




if __name__ == "__main__":
    cli()
