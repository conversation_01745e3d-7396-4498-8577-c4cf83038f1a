#!/usr/bin/env python3
"""
KarPark 项目安装配置文件

setup.py 是 Python 项目的标准安装配置文件，它定义了：
1. 项目的基本信息（名称、版本、描述等）
2. 项目的依赖关系
3. 包的结构和入口点
4. 安装时的自定义操作

主要功能：
- 将项目根目录添加到 PYTHONPATH，使得可以直接导入 karpark 模块
- 安装项目依赖
- 调用 scripts/install_mitm.py 安装 MITM 代理相关依赖
- 设置命令行入口点，方便直接运行项目
"""

import subprocess
import sys
from pathlib import Path

from setuptools import setup, find_packages
from setuptools.command.develop import develop
from setuptools.command.install import install


def read_requirements():
    """读取 requirements.txt 文件中的依赖"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        return []
    
    requirements = []
    with open(requirements_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过注释和空行
            if line and not line.startswith('#'):
                requirements.append(line)
    return requirements


def read_long_description():
    """读取 README.md 作为长描述"""
    readme_file = Path(__file__).parent / "README.md"
    if readme_file.exists():
        with open(readme_file, 'r', encoding='utf-8') as f:
            return f.read()
    return ""


class PostInstallCommand(install):
    """安装后执行的自定义命令"""
    
    def run(self):
        # 先执行标准安装
        install.run(self)
        # 然后执行自定义安装步骤
        self.execute_post_install()
    
    def execute_post_install(self):
        """执行安装后的自定义步骤"""
        print("\n" + "="*50)
        print("KarPark Post-Installation Configuration")
        print("="*50)

        # 调用 install_mitm.py 安装 MITM 相关依赖
        self.install_mitm_dependencies()

        # 显示安装完成信息
        self.show_completion_info()
    
    def install_mitm_dependencies(self):
        """Install MITM proxy dependencies"""
        print("\nInstalling MITM proxy dependencies...")

        install_script = Path(__file__).parent / "scripts" / "tools" / "install_mitm.py"
        if not install_script.exists():
            print("install_mitm.py script not found, skipping MITM dependency installation")
            return

        try:
            # Check if dependencies already exist
            result = subprocess.run([
                sys.executable, str(install_script), "--test-only"
            ], capture_output=True, text=True, cwd=str(install_script.parent))

            if result.returncode == 0:
                print("MITM dependencies already exist, skipping installation")
                return

            # Install dependencies
            print("Installing MITM dependencies...")
            result = subprocess.run([
                sys.executable, str(install_script), "--system"
            ], cwd=str(install_script.parent))

            if result.returncode == 0:
                print("MITM dependencies installed successfully")
            else:
                print("MITM dependency installation failed, please run manually:")
                print(f"   python {install_script} --system")

        except Exception as e:
            print(f"MITM dependency installation error: {e}")
            print("Please run manually:")
            print(f"   python {install_script} --system")
    
    def show_completion_info(self):
        """Show installation completion information"""
        print("\nKarPark installation completed!")
        print("\nQuick start:")
        print("   # Run main program")
        print("   karpark")
        print("   # or")
        print("   python -m karpark.main")
        print("\n   # Run tools (unified entry point)")
        print("   karpark-tools                    # Show available tools")
        print("   karpark-tools enrollment         # Run enrollment manager")
        print("   karpark-tools mitm               # Start MITM proxy")
        print("   karpark-tools validate check     # Validate server resources")
        print("\n   # Traditional methods (still available)")
        print("   python scripts/run_enrollment_manager.py")
        print("   python scripts/run_interceptor.py")
        print("   python scripts/run_change_monitor.py")
        print("\nFor more information, see README.md")
        print("="*50)


class PostDevelopCommand(develop):
    """开发模式安装后执行的自定义命令"""

    def run(self):
        develop.run(self)
        # Create a temporary PostInstallCommand instance to call the method
        temp_cmd = PostInstallCommand(self.distribution)
        temp_cmd.execute_post_install()


# 项目元数据
setup(
    # 基本信息
    name="karpark",
    version="0.5.1",
    description="KarPark - SAP Labs Digital Parking 自动抢注系统",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    
    # 作者信息
    author="KarPark Team",
    author_email="<EMAIL>",
    
    # 项目链接
    url="https://github.com/newhua/karpark-project",
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Topic :: Office/Business",
        "Topic :: Utilities",
    ],
    
    # Python 版本要求
    python_requires=">=3.8",
    
    # 包配置
    packages=find_packages(include=["karpark", "karpark.*", "scripts", "scripts.*"]),
    include_package_data=True,
    
    # 包数据
    package_data={
        "karpark": [
            "config/*.yaml",
            "config/static/*.yaml",
            "mitm/replacement/**/*",
            "mitm/admin/**/*",
        ],
    },
    
    # 依赖
    install_requires=read_requirements(),
    
    # 可选依赖
    extras_require={
        "dev": [
            "pytest>=8.0.0",
            "pytest-cov",
            "black",
            "flake8",
            "mypy",
        ],
        "mitm": [
            "mitmproxy>=12.0.0",
            "coloredlogs",
            "psutil",
        ],
    },
    
    # 命令行入口点
    entry_points={
        "console_scripts": [
            "karpark=karpark.main:main",
            "karpark-tools=scripts.run_karpark_tools:main",
            # 保留旧的入口点以确保向后兼容性
            "karpark-enrollment=scripts.run_enrollment_manager:main",
            "karpark-mitm=scripts.run_mitm_server:main",
            "karpark-validate=scripts.run_validate_server_resources:cli",
        ],
    },
    
    # 自定义安装命令
    cmdclass={
        "install": PostInstallCommand,
        "develop": PostDevelopCommand,
    },
    
    # 项目关键词
    keywords="parking automation sap labs registration queue monitoring",
    
    # 项目状态
    zip_safe=False,
)
