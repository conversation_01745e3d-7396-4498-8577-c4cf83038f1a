<!DOCTYPE html>
<html>

<head>
   <meta charset="utf-8">
   <meta name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, minimal-ui">
   <meta name="apple-mobile-web-app-capable" content="yes">
   <meta name="apple-mobile-web-app-status-bar-style" content="black">
   <meta content="telephone=no" name="format-detection">
   <title>SAP Labs Parking</title>
   <link rel="stylesheet" href="css/weui.min.css">
   <link rel="stylesheet" href="css/style.css">
</head>

<body>
   <div class="weui-cells weui-cells_form parking-cells">
      <!-- Employee Id -->
      <div class="weui-cell larger-cell parking-cell">
         <div class="weui-cell__hd">
            <label class="weui-label parking-label">Employee ID</label>
         </div>
         <div class="weui-cell__bd">
            <input id="parking-ee-id" class="weui-input parking-input" disabled="disabled" placeholder="" />
         </div>
      </div>
      <!-- Employee name -->
      <div class="weui-cell parking-cell">
         <div class="weui-cell__hd"><label class="weui-label parking-label">Employee Name</label></div>
         <div class="weui-cell__bd">
            <input id="parking-ee-name" class="weui-input parking-input" type="text" disabled="disabled"
               placeholder="" />
         </div>
      </div>
      <!-- Car Number -->
      <div class="weui-cell parking-cell">
         <div class="weui-cell__hd">
            <label class="weui-label parking-label">Car Number</label>
         </div>
         <div class="weui-cell__ft province-btn-block">
            <a href="javascript:;" id="openKeyboard" class="weui-vcode-btn province-btn">省</a>
         </div>
         <div class="weui-cell__bd">
            <input id="car-number" onkeyup="this.value=this.value.replace(/[^a-zA-Z0-9]/g,'');"
               class="weui-input parking-input" type="email" maxlength="7" placeholder="Type car number">
         </div>
      </div>
      <!-- Parking Area -->
      <div class="weui-cell parking-cell">
         <div class="weui-cell__hd"><label class="weui-label parking-label">Parking Area</label></div>
         <div class="weui-cell__bd">
            <input id="parking-area" class="weui-input parking-input" onfocus='this.blur();' type="text" readonly="true"
               placeholder="Select parking area" />
         </div>
      </div>
      <!-- Waiting status -->
      <div class="weui-cell parking-cell" style="display:none">
         <div class="weui-cell__hd"><label class="weui-label parking-label">Waiting Status</label></div>
         <div class="weui-cell__bd">
            <input id="queued" class="weui-input parking-input" onfocus='this.blur();' type="text" readonly="true" />
         </div>
      </div>


      <div class="weui-cell weui-cell_vcode"></div>
   </div>
   <div id="keyboardBox">
      <div class="provienceBox" id="provienceBox">
         <ul>
            <li>京</li>
            <li>津</li>
            <li>渝</li>
            <li>沪</li>
            <li>冀</li>
            <li>晋</li>
            <li>辽</li>
            <li>吉</li>
            <li>黑</li>
            <li>苏</li>
         </ul>
         <ul>
            <li>浙</li>
            <li>皖</li>
            <li>闽</li>
            <li>赣</li>
            <li>鲁</li>
            <li>豫</li>
            <li>鄂</li>
            <li>湘</li>
            <li>粤</li>
            <li>琼</li>
         </ul>
         <ul>
            <li>川</li>
            <li>贵</li>
            <li>云</li>
            <li>陕</li>
            <li>甘</li>
            <li>青</li>
            <li>蒙</li>
            <li>桂</li>
            <li>宁</li>
            <li>新</li>
         </ul>
         <ul>
            <li class="changeContentBtn other"></li>
            <li>藏</li>
            <li>使</li>
            <li>领</li>
            <li>警</li>
            <li>学</li>
            <li>港</li>
            <li>澳</li>
            <li class="deleteBtn other"></li>
         </ul>
      </div>
   </div>
   <div class="weui-agree__checkbox-group" id="weuiCheckGroup" bindchange="bindAgreeChange" style="display:block">
      <label class="weui-agree">
         <input type="checkbox" id="agreeInput" class="weui-agree__checkbox">
         <span class="weui-agree__text">
            Read and agree to <a id="userMenuContent" href="javascript:;"> Privacy Statement</a>
      </label>
   </div>
   <div id="userMenuDialog" style="display: none;">
      <div class="weui-mask"></div>
      <div class="weui-dialog">
         <div>
            
         </div>
         <div class="weui-dialog__hd"><strong class="weui-dialog__title">声明</strong></div>
         <div class="weui-dialog__bd">
            <p align="left">
               思爱普（中国）有限公司（SAP (China) Co., Ltd， “SAP”）深知个人信息对您的重要性，并会依照SAP全球数据隐私保护政策全力保护您的个人信息安全。<br>
               <br>
               为改善员工对Digital Parking的用户体验，基于为员工提供停车位注册排队之目的，我们将收集您以下个人信息：<br>
               <br>
               1. SAP ID<br>
               2. 车牌号XXXXXX<br>
               <br>
               SAP不会共享、转让或披露您的个人信息给任何其他第三人，详情可参见隐私政策。<br>
               <br>
               如果您对收集和使用您的个人信息有任何疑问，您可以通过 <a style="color:#00a4ff" href="https://fiorilaunchpad.sap.com/sap/hana/uis/clients/ushell-app/shells/fiori/FioriLaunchpad.html">FINANCEDirect</a> 与我们取得联系，反馈您的意见或建议。<br>
               <br>
               谢谢！<br>
               <br><br>
               <br>

               SAP (China) Co., Ltd （SAP）cares about employees’ personal information and will make effort to protect your personal information security in accordance with SAP's Global Data Privacy Protection Policy.<br>
               <br>
               Under your authorization, Digital Parking will collect your mobile phone number and SAP Employee Badge number and Car plate number for the purpose of parking registration and queue.<br>
               <br>
               SAP will not share, transfer or disclose your personal information to any other third party,pleaes refer to the Privacy Statement(added link).<br>
               <br>
               If you have any questions or comments, please contact us through <a style="color:#00a4ff" href="https://fiorilaunchpad.sap.com/sap/hana/uis/clients/ushell-app/shells/fiori/FioriLaunchpad.html">FINANCEDirect</a>.<br>
               <br>
               Thank you!<br>
               <br>

            </p>
         </div>
         <div class="weui-dialog__ft">
            <a id="agreeBtn" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_primary">同意</a>
            <a id="disagreeBtn" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_primary">拒绝</a>
         </div>
      </div>
   </div>
   <!-- Regist button -->
   <div class="weui-btn-area">
      <a id="sumitBtn" class="weui-btn weui-btn_primary" href="javascript:">Regist Now</a>
   </div>
   <label for="weuiAgree" class="weui-agree parking-regist-time">
      <span class="weui-agree__text">
         Regist Time: <a id="regist-time" href="javascript:void(0);"></a>
      </span>
   </label>
   <script type="text/javascript" src="js/jquery-3.1.1.min.js?v4"></script>
   <script type="text/javascript" src="js/weui.min.js"></script>
   <script type="text/javascript" src="js/parking.js?v3"></script>
</body>

</html>