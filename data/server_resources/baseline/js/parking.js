$(document).ready(function() {
    var postId;
    var loading = weui.loading('loading', {
        className: 'custom-classname'
    });
    loading.hide();
    $.get("/mobile/parking/getEntry", function(res) {
        loading.hide();
    }).done(function(res) {
        if (res) {
            if(res.error === null || res.error.errorCode === null || res.error.errorCode === "") {
                postId = res.id;
                $("#parking-ee-id").val(res.eeId);
                $("#parking-ee-name").val(res.eeName);
                if (res.location && res.carNo &&
                    (res.status.toUpperCase() === "WAITING"
                        || res.status.toUpperCase() === "PENDING"
                        || res.status.toUpperCase() === "ACCEPTED"
                        || res.status.toUpperCase() === "REJECTED")) {
                    setInputFieldsDisable();
                    $("#sumitBtn").text("View Waiting List")
                    $("#parking-area").val(res.location);
                    $("#car-number").val(
                        res.carNo.substring(1, res.carNo.length));
                    $("#openKeyboard").text(res.carNo.charAt(0));
                    $("#queued").val((res.order) + " / "
                        + res.total).parent().parent().show();

                    $(".parking-regist-time").show();
                    $("#regist-time").text(
                        new Date(res.registerTime).toLocaleString());
                    $('#sumitBtn').attr('class',
                        'weui-btn weui-btn_primary').unbind().click(
                        function () {
                            location.href = "./list.html?p=PVG06&user=" + $(
                                "#parking-ee-id").val();
                        });
                }
            } else {
                $("#parking-ee-id").val(res.eeId);
                $("#parking-ee-name").val(res.eeName);
                weui.topTips(res.error.errorMessage, 50000);
                if (res.error.errorCode === "40003") {
                    $('#sumitBtn').attr('class',
                        'weui-btn weui-btn_disabled weui-btn_primary')
                }
            }
        }else {
            weui.alert('Get data failed.');
        }
    }).fail(function(res) {
        weui.alert('Get data failed.');
    });

    $('#openKeyboard').unbind().click(function(event) {
        if (event.currentTarget.className.indexOf('disabled') < 0) {
            $("#keyboardBox").show();
        }
    });

    $('#parking-area').unbind().click(function() {
        weui.picker([{
            label: 'PVG 06',
            value: 'PVG06'
        }, {
            label: 'PVG 12',
            value: 'PVG12'
        }], {
            defaultValue: [0],
            className: 'custom-classname',
            onChange: function(result) {

            },
            onConfirm: function(result) {
                document.querySelector('#parking-area').value = result[0].label;
            },
            id: 'picker'
        });
    });

    $('#sumitBtn').unbind().click(function(event) {
        if (event.currentTarget.className.indexOf('disabled') < 0) {
            var employeeId, employeeName, carNumber, parkingArea, loading, alertMsg;
            var registObj = {
                "id": postId,
                "eeId": $("#parking-ee-id").val().trim(),
                "eeName": $("#parking-ee-name").val().trim(),
                "location": $("#parking-area").val(),
                "carNo": $("#openKeyboard").text() + $("#car-number").val().trim().toUpperCase(),
                "comment": "",
                "registerTime": null,
                "status":"WAITING"

            }
            alertMsg = checkInputFields(registObj);
            if (alertMsg === true) {
                if($('#agreeInput')[0].checked !== true){
                    weui.topTips("Please read and agree the privacy statement if you want to use Digital Parking.", 3000);
                    return
                }
                var confirm = weui.confirm('提交信息后请联系管理员修改. </br>Please contact administrator if you need to update your information.', {
                    title: '确认提交',
                    buttons: [{
                        label: 'NO',
                        type: 'default',
                        onClick: function() {
                        }
                    }, {
                        label: 'YES',
                        type: 'primary',
                        onClick: function() {
                            loading = weui.loading('loading', {
                                className: 'custom-classname'
                            });
                            $.ajax({
                                headers: {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                },
                                url: "/mobile/parking/saveEntry",
                                type: "POST",
                                contentType: "application/json; charset=utf-8",
                                cache: false,
                                dataType: 'json',
                                data: JSON.stringify(registObj),
                                success: function(res) {
                                    loading.hide();
                                    if(res.error) {
                                        if(res.error.errorCode === null){
                                            weui.toast('Regist Successfuly!', 3000);
                                            setTimeout(function(){location.reload()},2800);
                                            $(".parking-regist-time").show();
                                            $("#regist-time").text(new Date(res.registerTime).toLocaleString());
                                            setInputFieldsDisable();
                                        }else {
                                            weui.topTips(res.error.errorMessage, 5000);
                                        }
                                    }
                                },
                                error: function(res) {
                                    loading.hide();
                                }
                            });
                        }
                    }]
                });
            } else {
                weui.topTips(alertMsg, 3000);
            }
        }
    });

    $('#weuiAgree').unbind().click(function(event) {
        if (event.currentTarget.className.indexOf('disagree') < 0) {
            // agree->disagree
            $('#weuiAgree').attr('class', 'weui-agree__checkbox disagree');
        } else {
            $('#weuiAgree').attr('class', 'weui-agree__checkbox agree');
        }
    })

    $('#userMenuContent').unbind().click(function(event) {
        $("#userMenuDialog")[0].style.display = "block"
    })

    $('#agreeBtn').unbind().click(function(event) {
        $("#userMenuDialog")[0].style.display = "none"
        $('#agreeInput')[0].checked = true
    })

    $('#disagreeBtn').unbind().click(function(event) {
        $("#userMenuDialog")[0].style.display = "none"
        $('#agreeInput')[0].checked = false
    })

    var num = 6;
    $('.xinnengyuan').unbind().click(function() {
        num = 7;
        $(this).removeClass('xinnengyuan');
    })

    function getIndex() {
        var index = 0;
        $('.carLicenseMain ul li').each(function() {
            var reg = new RegExp('active');
            if (reg.test($(this).attr('class'))) {
                index = $(this).index();
            }
        })
        return index;
    }

    function keyboard(num, self) {
        var index = getIndex();
        if (index <= num) {
            if (index == num) {
                $('#openKeyboard').html($(self).html());
            } else {
                $('#openKeyboard').html($(self).html()).removeClass('active').next().addClass('active');
            }
            $("#keyboardBox").hide();
            $("#car-number").focus();
        }
    }

    function setInputFieldsDisable() {
        $("#car-number").prop('disabled', true);
        $("#parking-area").prop('disabled', true);
        $('#openKeyboard').attr('class', 'weui-vcode-btn province-btn disabled');
        $('#sumitBtn').attr('class', 'weui-btn weui-btn_disabled weui-btn_primary');
    }

    function checkInputFields(obj) {
        if (obj) {
            if (obj.eeid === null && obj.eeid === '') {
                return 'Your employee id is error.';
            }
            if (obj.eename === null && obj.eename === '') {
                return 'Your employee name is error.';
            }
            if (obj.carNo.indexOf('省') >= 0) {
                return 'Please select your car number location.';
            }
            if (obj.carNo.length < 7 || obj.carNo.length > 8) {
                return 'Your car number is error';
            }
            if (obj.location.length === 0) {
                return 'Please select your car parking area.';
            }
            return true;
        }
    }

    $('#provienceBox ul li:not(.other)').click(function() {
        var self = this;
        keyboard(num, self);
    })
});