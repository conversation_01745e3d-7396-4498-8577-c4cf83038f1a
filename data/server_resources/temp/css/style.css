html, body {
    height: 100%;
    -webkit-tap-highlight-color: transparent;

}

body{
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

ul{
    list-style: none;
}

body, .page {
    background-color: #F8F8F8;
}

input,
input::-webkit-input-placeholder {
    font-size: 16px;
}

.parking-cells {
    margin-top: 0;
}

.parking-cell {
    height: 48px;
}

.parking-label {
    width: 130px;
}

.parking-input {
    text-align: right;
}
.parking-regist-time {
	display: none;
}
.new-energy {
	float: left;
}

.car-number-span {
	display: block;
    overflow: hidden;
    padding: 0 4px 0 6px
}
#car-number {
    text-transform: uppercase;
    text-overflow: ellipsis;
}

#xxxx {
	display: block;
    overflow: hidden;
    padding: 0 4px 0 6px
}
::placeholder { /* Recent browsers */
    text-transform: none;
}

.page {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    opacity: 0;
    z-index: 1;
    &.js_show{
        opacity: 1;
    }
}

.page__hd {
    padding: 40px;
}

.page__bd {}

.page__bd_spacing {
    padding: 0 15px;
}

.page__ft{
    padding-top: 40px;
    padding-bottom: 10px;
    text-align: center;
    img{
        height: 19px;
    }
    &.j_bottom{
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
    }
}

.page__title {
    text-align: left;
    font-size: 20px;
    font-weight: 400;
}

.page__desc {
    margin-top: 5px;
    color: #888888;
    text-align: left;
    font-size: 14px;
}
@media (min-device-width: 360px) and (-webkit-min-device-pixel-ratio: 2) {
	.province-btn-block {
	padding-left: 50px;
	}
}

@media(min-device-width:410px) and (-webkit-min-device-pixel-ratio:3) {
	.province-btn-block {
	padding-left: 80px;
	}
}


.province-btn {
    margin-left: 0px;
    border-left: 0;
    border-right: 1px solid #e5e5e5;
    padding: 0 0.6em 0 0em;
}

#keyboardBox{
    display: none;
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0px;
    z-index: 999;
}
#keyboardBox .provienceBox,#keyboardBox .textBox{
    width: 100%;
    background-color: #D0D5D9;
    padding-top: 10px;
    padding-bottom: 4px;
}
#keyboardBox .provienceBox ul,#keyboardBox .textBox ul{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    margin-top: 10px;
}
#keyboardBox .provienceBox ul:first-of-type, #keyboardBox .textBox ul:first-of-type{
    margin-top: 0px;
}
#keyboardBox .provienceBox ul li,#keyboardBox .textBox ul li{
    width: 30px;
    height: 40px;
    border-radius: 6px;
    text-align: center;
    line-height: 40px;
    float: left;
    background-color: #ffffff;
}
#keyboardBox .textBox{
    display: none;
}
#keyboardBox .provienceBox ul .changeContentBtn,#keyboardBox .provienceBox ul .deleteBtn,#keyboardBox .textBox ul .changeContentBtn,#keyboardBox .textBox ul .deleteBtn{
    width: 40px;
    height: 40px;
    background-color: #d1d5d9;
    text-align: center;
    line-height: 40px;
}
#keyboardBox .provienceBox ul .deleteBtn img,#keyboardBox .textBox ul .deleteBtn img{
    width: 23px;
    height: 16px;
    margin: 0px;
    margin-top: 12px;
}
.weui-dialog__bd {
	word-wrap: break-word;
	word-break: keep-all;
    max-height: 250px;
    overflow: scroll;
}