我需要在reminder.py里建一个类，叫ReminderManager，这个类按status发送的reminder，所以按status发送的情况要记录下来，分几种情况：
0， 不发送reminder的status： 
SATUS.STATIC 不发送

1， 发送给全体的（不区分parker的）发送过就不需要再发送的status；下面这个是有顺序的，如果后面的发送过就不用再对前面的发送，比如CLOSED发送过，就不用再发送SLIDED,以此类推
- SATUS.SLIDED 发送
- SATUS.CLOSED  发送
- SATUS.DELETED 发送
- SATUS.DELETED_REOPENED 发送
- SATUS.NOT_DELETED_REOPENED 发送

2， 发送给全体的（不区分parker的），发送过，但是TIME_AFTER秒后可以再发送的status，TIME_AFTER是个常量，定义在class外
3， 按parker记录发送情况的，发送过就不需要再发送的status
- STATUS.REGISTERED
4， 按parker记录发送情况的，发送过，但是TIME_AFTER秒后可以再发送的status，TIME_AFTER是个常量，定义在class外
- STATUS.ERROR

5. 只发送给admin的，发送过就不需要再发送的status
- STATUS.FAILURE

6. 只发送给admin的，发送过，但是TIME_AFTER秒后可以再发送的status，TIME_AFTER是个常量，定义在class外
- STATUS.FAILURE


发送给全局是mail地址在EmailAddress.SUBSCRIBER_LIST，发送给admin时地址在地址文件是 EmailAddress.ADMIN_LIST，每一行就是一个邮件地址。
如果是发送给特定parker的话，地址在EmailAddress.BY_PARKER,是一个csv文件，两个column：一个是parker.ee_id,一个是地址

能不能单独提供几个函数，这样我就不要考虑参数，比较容易地调用函数

send_global_reminder(status)
send_parker_reminder(status, parker)
send_admin_reminder(status)

发送邮件可以调用，
from karpark.mail.send_mail import send_mail


我在这里想实现的逻辑，其实知道status，也就是parker.current_status之后，要调用哪个_send*email函数，因为status和_send_*函数之间其实有一个对应关系，这个对应关系举个例子的化FAILURE时调用_send_admin_email， ERROR时调用_send_parker_email，DELETED_REOPENED时调用_send_global_email大概是这种关系，你先写出个样板，我后面再补齐。
