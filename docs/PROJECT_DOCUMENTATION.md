# KarPark 项目文档

## 1. 项目概述

KarPark 是一个用于管理停车注册和队列位置的系统。它允许用户通过邮件或Mitmproxy拦截请求来注册停车，并能监控队列状态，在状态变化时发送邮件通知。

## 2. 项目结构

```
karpark-project/
├── data/                         # 数据存储目录
│   ├── server_resources/         # 服务器资源缓存
│   └── sqlite/                   # SQLite数据库文件
├── docs/                         # 项目文档
│   ├── PROJECT_DOCUMENTATION.md  # 项目详细文档
│   └── reference/                # 参考资料
├── karpark/                      # 项目核心代码
│   ├── __init__.py
│   ├── cli/                      # 命令行界面模块
│   │   ├── cli_utils.py          # CLI工具函数
│   │   └── enrollment_manager.py # 注册管理CLI
│   ├── colorlog/                 # 日志配置模块
│   │   └── logger.py             # 日志记录配置
│   ├── common/                   # 通用模块 (配置, 常量, 路径)
│   │   ├── config.py             # 配置加载和管理类
│   │   ├── const.py              # 项目常量
│   │   └── paths.py              # 项目路径管理
│   ├── config/                   # 配置文件目录
│   │   ├── __init__.py
│   │   ├── _access_control.yaml  # 访问控制配置
│   │   ├── _email_address.yaml   # 邮件地址配置
│   │   ├── base.yaml             # 基础配置文件
│   │   └── static/               # 静态配置资源
│   ├── core/                     # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── db_operations.py      # 数据库操作函数 (SQLite)
│   │   ├── entities/             # 数据实体 (请求数据, 响应数据, 状态等)
│   │   ├── interval.py           # 时间间隔处理
│   │   ├── magic_plate.py        # 特殊车牌处理逻辑
│   │   ├── notification.py       # 通知管理
│   │   ├── parker.py             # 单个车辆的停车注册和队列管理
│   │   ├── status_manager.py     # 状态管理
│   │   └── task_scheduler.py     # 任务调度器，管理多个Parker实例
│   ├── mail/                     # 邮件处理模块
│   │   ├── __init__.py
│   │   ├── email_client.py       # 邮件发送和接收客户端
│   │   ├── email_db_operations.py # 邮件相关数据库操作
│   │   ├── format/               # 邮件格式相关
│   │   └── template.py           # 邮件模板
│   ├── main.py                   # 项目主入口
│   ├── mitm/                     # Mitmproxy 相关脚本
│   │   ├── __init__.py
│   │   ├── admin/                # 管理员页面相关资源
│   │   │   ├── admin.html        # 管理员主页面
│   │   │   ├── web_enrollment_manager.py # Web端注册管理
│   │   │   └── *.html            # 其他管理页面
│   │   ├── enrollment/           # 注册页面替换资源
│   │   │   └── replacement/      # 替换资源目录
│   │   │       ├── authorized/   # 授权用户资源
│   │   │       │   ├── css/      # 样式文件
│   │   │       │   ├── js/       # JavaScript文件
│   │   │       │   └── register.html # 注册页面
│   │   │       └── interim/      # 临时状态资源
│   │   │           ├── css/      # 样式文件
│   │   │           ├── js/       # JavaScript文件
│   │   │           └── register.html # 注册页面
│   │   ├── intercept.py          # Mitmproxy拦截脚本
│   │   ├── interceptor_utils.py  # Mitmproxy工具函数
│   │   ├── launch_mitm_server.py # MITM代理启动脚本（优化版）
│   │   └── script.py             # 运行Mitmproxy的脚本
│   └── utils/                    # 工具类
│       ├── __init__.py
│       ├── auto_name_enum.py     # 自动名称枚举
│       ├── normalize.py          # 数据规范化工具
│       ├── singleton.py          # 单例模式实现
│       ├── temporal.py           # 时间处理工具
│       └── thread_.py            # 线程相关工具
├── logs/                         # 日志文件目录
├── scripts/                      # 实用工具脚本
│   ├── run_karpark_tools.py      # 统一工具入口点（新增）
│   ├── run_enrollment_manager.py # 运行注册管理器
│   ├── run_mitmproxy_server.py   # 运行MITM代理服务器
│   ├── run_monitor_resources.py  # 服务器资源验证工具
│   └── tools/                    # 工具脚本子目录
│       ├── install_mitm.py       # MITM安装脚本
│       └── set_cookie.py         # Cookie设置工具
├── tests/                        # 测试代码目录
│   ├── integration/              # 集成测试
│   └── unit/                     # 单元测试
├── .gitignore
├── LICENSE
├── README.md                     # 项目说明、安装和使用指南
└── requirements.txt              # Python依赖包
```

## 3. 主要模块功能

### 3.1. `karpark.main`

-   **`main.py`**: 项目的启动入口。调用 `TaskScheduler` 来运行主程序。

### 3.2. `karpark.core` (核心逻辑)

-   **`task_scheduler.py` (`TaskScheduler`)**: 管理和调度多个 `Parker` 实例。负责从数据库加载注册信息，处理新的注册和取消注册，选择下一个要处理的任务，并根据任务状态进行智能休眠。
-   **`parker.py` (`Parker`)**: 管理单个车辆的停车注册和队列位置。处理与停车系统的所有交互，包括获取队列信息、注册停车、跟踪状态和监控队列位置。支持使用“魔法车牌”（特殊字符组合的车牌）进行注册。
-   **`status_manager.py` (`StatusManager`)**: 管理 `Parker` 实例的状态。根据当前状态执行相应的处理逻辑（例如，发送通知，尝试重新注册等）。
-   **`notification.py` (`Notification`)**: 处理基于 `Parker` 状态的邮件通知。根据预设规则（状态优先级、可重复状态、冷却时间）向管理员、用户或全局列表发送邮件。
-   **`magic_plate.py`**: 实现特殊车牌（魔法车牌）的生成和处理逻辑，用于提高注册成功率。
-   **`entities/`**: 包含项目中使用的数据结构，如请求数据 (`PostData`)、响应数据 (`RespData`)、状态枚举 (`Status`) 等。

### 3.3. `karpark.common` (通用模块)

-   **`config.py`**: 定义了项目中各种配置类（如 `ServiceURL`, `ParkerConfig`, `NotificationConfig`, `TaskSchedulerConfig`, `MagicPlateConfig`, `MailConfig`）。它负责从 YAML 配置文件加载配置，并提供静态访问这些配置的方法。
-   **`paths.py`**: 管理项目中的文件和目录路径。
-   **`const.py`**: 定义项目中使用的常量。

### 3.4. `karpark.config` (配置)

-   **`config.yaml`**: 主要的应用配置文件，包含了邮件账户、任务调度参数、服务阈值、通知阈值等。
-   **`_email_address.yaml`**: 存储全局通知和管理员通知的邮件地址。
-   **`_magic_plate.yaml`**: 配置魔法车牌的字符映射和优先级规则。
-   **`_mitm_access.yaml`**: Mitmproxy 相关的访问配置 (具体用途需查看文件内容)。
-   **`_service_url.yaml`**: 配置停车服务的API接口URL。

### 3.5. `karpark.core` (核心业务逻辑 - 数据库部分)

-   **`db_operations.py`**: 封装了所有与 SQLite 数据库的交互操作。包括初始化数据库、创建表、保存/获取注册信息、管理已处理邮件ID等。
    -   主要表结构：
        -   `enrollments`: 存储用户注册信息 (ee_id, cookie, 车牌号, 是否使用魔法车牌, 邮件地址等)。
        -   `cookie_owner`: 存储 cookie 和对应的 ee_id。
        -   `delta_enrollment_state`: 跟踪增量获取注册信息的最后时间点。
        -   `processed_emails`: 记录已处理的邮件ID，防止重复处理。
-   **`interval.py`**: 时间间隔处理相关功能。

### 3.6. `karpark.mail` (邮件处理)

-   **`email_client.py` (`EmailClient`)**: 提供了发送和接收邮件的功能。支持SMTP和POP3协议，处理邮件的创建、附件、收件人列表等。
-   **`email_db_operations.py`**: 邮件相关的数据库操作，包括已处理邮件ID的管理等。
-   **`template.py`**: 定义了不同通知场景下的邮件模板 (主题和内容)。
-   **`format/`**: 邮件格式相关的处理模块。

### 3.7. `karpark.mitm` (Mitmproxy)

-   **`script.py`**: 启动 `mitmdump` 的脚本，并加载 `intercept.py` 作为拦截脚本。
-   **`launch_mitm_server.py`**: MITM代理服务器的启动脚本，支持配置文件和命令行参数管理Python环境。
-   **`intercept.py`**: Mitmproxy 的核心拦截脚本。用于拦截特定的HTTP请求，提取或修改数据，并将数据保存到数据库 (例如，通过拦截到的请求自动注册停车)。
-   **`interceptor_utils.py`**: 拦截器的工具函数，包含授权类型判断、响应构建、静态资源替换等核心逻辑。
-   **`enrollment/replacement/`**: 存放替换资源的目录，包含 `authorized/` 和 `interim/` 两个子目录，分别对应不同授权状态下的页面资源。
-   **`admin/`**: 管理员页面相关资源，包含Web端的用户管理界面和API。

### 3.8. `karpark.utils` (工具类)

-   **`singleton.py`**: 实现单例模式的装饰器。
-   **`temporal.py`**: 提供时间处理相关的工具函数和类。
-   **`auto_name_enum.py`**: 自动为枚举成员赋值名称的工具。
-   **`normalize.py`**: 数据规范化工具，包含cookie规范化等功能。
-   **`thread_.py`**: 线程相关的工具函数。

### 3.9. `karpark.colorlog` (日志模块)

-   **`logger.py`**: 配置和提供日志记录功能，支持控制台和文件输出，并使用 `loguru` 进行日志管理。

### 3.10. `karpark.cli` (命令行界面)

-   **`enrollment_manager.py`**: 提供命令行界面的注册管理功能。
-   **`cli_utils.py`**: CLI相关的工具函数。

### 3.11. 顶级目录

-   **`data/`**: 数据存储目录
    -   **`server_resources/`**: 服务器资源缓存，用于资源验证工具
    -   **`sqlite/`**: SQLite数据库文件存储位置
-   **`docs/`**: 项目文档目录
    -   **`PROJECT_DOCUMENTATION.md`**: 详细的项目文档
    -   **`reference/`**: 参考资料和备份文件
-   **`logs/`**: 日志文件存储目录，按日期组织日志文件
-   **`scripts/`**: 实用工具脚本目录
    -   **`run_karpark_tools.py`**: 统一工具入口点（新增），提供所有工具的统一访问接口
    -   **`run_enrollment_manager.py`**: 注册管理器启动脚本
    -   **`run_mitmproxy_server.py`**: MITM代理服务器启动脚本
    -   **`run_monitor_resources.py`**: 服务器资源验证工具，集成Cookie管理和状态验证
    -   **`tools/`**: 工具脚本子目录
        -   **`install_mitm.py`**: MITM代理安装脚本
        -   **`set_cookie.py`**: Cookie设置工具
-   **`tests/`**: 测试代码目录
    -   **`integration/`**: 集成测试
    -   **`unit/`**: 单元测试

## 4. 主要功能流程

1.  **用户注册**:
    *   **通过邮件**: 用户发送特定格式的邮件，`readmail.py` 模块会定期收取邮件，解析邮件内容（使用正则或AI），提取注册信息（员工ID、车牌号、是否使用魔法车牌等），并将这些信息存入数据库的 `enrollments` 表。
    *   **通过Mitmproxy**: 用户通过配置了Mitmproxy代理的设备访问停车系统，`mitm/intercept.py` 脚本会拦截相关请求，从中提取注册信息并存入数据库。
2.  **任务调度 (`TaskScheduler`)**:
    *   从数据库加载所有激活的注册任务 (`Parker` 实例)。
    *   根据每个任务的最后检查时间等因素，决定下一个要处理的任务。
    *   调用选中 `Parker` 实例的方法来获取最新的队列信息。
3.  **停车处理 (`Parker`)**:
    *   向停车服务API发送请求，获取当前车辆的排队位置、总人数等信息。
    *   如果条件满足（例如，队列开放且车辆未注册），则尝试注册停车。
    *   支持使用 `magic_plate.py` 生成的特殊车牌进行注册，以提高成功率。
4.  **状态管理和通知 (`StatusManager`, `Notification`)**:
    *   `StatusManager` 监控 `Parker` 实例的状态变化（如排队中、注册成功、注册失败、队列位置变化等）。
    *   当状态发生有意义的变化时，`Notification` 模块会根据预设的规则，通过 `EmailClient`向相关人员（用户、管理员）发送邮件通知。
5.  **配置管理 (`common/config.py`, `config/*.yaml`)**:
    *   项目启动时，从 `config/` 目录下的 YAML 文件加载各项配置参数。
    *   配置信息在整个应用中通过静态类属性访问。
6.  **日志记录 (`utils/logger.py`)**:
    *   所有模块在运行过程中都会记录详细的日志，方便调试和追踪问题。

## 5. 如何运行

1.  确保已安装 Python 和 `requirements.txt` 中列出的所有依赖。
2.  配置 `karpark/config/` 目录下的 `.yaml` 文件，特别是 `config.yaml` (邮件账户、服务地址等) 和 `_email_address.yaml` (通知邮箱)。
3.  运行主程序:
    ```bash
    # 推荐方式
    karpark
    # 或传统方式
    python karpark/main.py
    ```
4.  如果需要使用其他工具，可以使用统一工具入口点:
    ```bash
    # 显示工具菜单
    karpark-tools

    # 启动登记管理器
    karpark-tools enrollment

    # 启动MITM代理
    karpark-tools mitm

    # 启动资源验证工具
    karpark-tools validate --interactive
    ```
    或使用传统方式:
    ```bash
    # MITM代理
    python karpark/mitm/launch_mitm_server.py
    # 或
    karpark-mitm

    # 登记管理器
    python scripts/run_enrollment_manager.py
    # 或
    karpark-enrollment
    ```
    并确保客户端设备已配置HTTP代理到Mitmproxy监听的端口 (默认为8080)。

### 5.1. MITM代理环境配置

MITM代理的Python环境选择现在通过配置文件进行管理：

**配置文件**: `karpark/config/_mitmproxy.yaml`
```yaml
python:
  use_system_python: false  # 默认使用虚拟环境
  # 设置为 true 使用系统Python
```

**命令行临时覆盖**:
```bash
# 临时使用系统Python（覆盖配置文件设置）
python karpark/mitm/launch_mitm_server.py --system-python
```

**配置优先级**:
1. 命令行参数 `--system-python`（最高优先级）
2. 配置文件中的 `python.use_system_python` 设置
3. 自动检测（虚拟环境检测和项目环境查找）

## 6. API 响应示例及前端分析

### 6.1. `saveEntry` API

当 `saveEntry` 发生错误时，返回的 JSON 结构示例如下：

```json
{
    "id": null,
    "eeId": "E001234",
    "eeName": "WANG, Wu",
    "carPlateNumber": "沪B01A01",
    "location": "PVG 06",
    "status": "WAITING",
    "order": 0,
    "total": 0,
    "registerTime": null,
    "timeStamp": null,
    "error": {
        "errorCode": "40004",
        "errorMessage": "Car Plate Number 沪B01A01 was already registered by E001234, please confirm with administrator."
    }
}
```

**说明:**

*   此处使用 `carPlateNumber` 而不是 `carNo`，可能是为了避免覆盖UI上已有的 `carNo` 字段。
*   其他值的返回格式应与POST请求时一致，例如：`"WAITING"` 而不是 `"Waiting"`，`"PVG 06"` 而不是 `"PVG06"`。
*   **注意**：`saveEntry` 成功时的具体JSON结构尚不明确，需要通过程序日志进一步确认。建议避免在不确定的情况下进行操作。

### 6.2. `getEntry` API

`getEntry` API 返回的 JSON 结构示例如下：

```json
{
    "id": null,
    "eeName": "Zhang, San",
    "eeId": "E001234",
    "location": "PVG06",
    "carNo": "沪B01A01",
    "registerTime": "2024-06-07T07:31:19.010+00:00",
    "timeStamp": "2024-06-07T07:31:19.010+00:00",
    "error": null,
    "order": 44,
    "total": 626,
    "status": "Accepted"
}
```

**说明:**

*   该结构与 `saveEntry` 的错误响应结构基本相同，主要区别在于 `carNo` 字段（`getEntry`）和 `carPlateNumber` 字段（`saveEntry` 错误时）。

### 6.3. `parking.js` 分析

`parking.js` 文件中的提交逻辑在成功处理后包含刷新机制。具体分析如下：

1.  **提交后的刷新逻辑**：
    *   在 `$.ajax` 的 `success` 回调函数中，当检测到 `res.error.errorCode === null` 时，会执行以下代码：
        ```javascript
        setTimeout(function(){location.reload()},2800);
        ```
        这里通过 `location.reload()` 实现了页面刷新，重新加载时会再次触发 `document.ready` 中的初始化代码。

2.  **数据加载逻辑**：
    *   页面初始化时通过 `$.get("/mobile/parking/getEntry")` 获取数据。如果用户已有登记信息（例如处于 `WAITING`/`PENDING` 等状态），以下代码会将接口返回的 `location` 和 `carNo` 填充到表单字段中：
        ```javascript
        $("#parking-area").val(res.location);
        $("#car-number").val(res.carNo.substring(1));
        $("#openKeyboard").text(res.carNo.charAt(0));
        ```

3.  **显示未刷新的原因**：
    *   当用户首次提交时，服务器会返回新的 `registObj` 数据。此时虽然调用了 `location.reload()`，但存在 2800ms 的延迟。在等待刷新期间，以下代码会直接操作 DOM 更新部分字段（登记时间、禁用输入）：
        ```javascript
        $(".parking-regist-time").show();
        $("#regist-time").text(new Date(res.registerTime).toLocaleString());
        setInputFieldsDisable();
        ```
        因此，用户会立即看到这些变化，而无需等待页面刷新。

4.  **最终一致性保证**：
    *   页面刷新后，初始化代码会重新请求最新数据，确保 `location` 和 `carNo` 与服务端完全一致。即使提交后不立即刷新，短暂的本地展示也能提升用户体验。

**结论**：

代码通过 `location.reload()` 实现了最终一致性刷新，同时利用本地 DOM 操作在等待刷新期间部分更新界面。这种混合策略既保证了数据准确性，又优化了用户感知。

## 7. MITM 拦截器详细逻辑

### 7.1. 拦截器架构概述

MITM拦截器 (`karpark.mitm.intercept`) 是一个基于mitmproxy的HTTP请求/响应拦截系统，主要用于：

1. **身份识别和授权管理**：通过cookie识别用户身份，判断用户的授权级别
2. **静态资源替换**：根据用户授权状态，动态替换HTML、CSS、JS等静态资源
3. **API响应修改**：修改停车系统API的响应数据，为授权用户提供增强功能
4. **数据收集和存储**：拦截用户提交的注册数据并存储到本地数据库

### 7.2. 三种授权类型及其作用

拦截器根据用户身份将请求分为三种授权类型：

#### 7.2.1. `unauthorized` (未授权用户)
- **特征**：用户的eeId不在授权用户列表中
- **处理方式**：保持原始页面和API响应不变，不进行任何替换或修改
- **目的**：确保普通用户的正常使用不受影响

#### 7.2.2. `interim` (临时状态)
- **特征**：无法从cookie中获取到有效的eeId信息
- **核心作用**：解决身份识别的时序问题
- **处理方式**：
  - 提供临时版本的静态资源 (`replacement/interim/`)
  - 保持原始API响应数据不变
  - 等待用户身份信息通过API响应获取后再进行后续判断

#### 7.2.3. `authorized` (已授权用户)
- **特征**：用户的eeId在授权用户列表中
- **处理方式**：
  - 提供增强版本的静态资源 (`replacement/authorized/`)
  - 修改API响应，添加额外的功能和数据
  - 支持高级功能如魔法车牌、管理员权限等

### 7.3. interim状态的特殊意义

#### 7.3.1. 问题背景
由于前端架构的限制，存在一个关键的时序问题：

1. **HTML页面加载**：用户访问停车注册页面时，HTML首先被加载
2. **parking.js延迟加载**：JavaScript文件在HTML加载完成后才开始加载和执行
3. **API数据获取**：parking.js执行后才会调用 `/mobile/parking/getEntry` API获取用户数据
4. **身份识别延迟**：只有在API响应中才能获取到用户的eeId信息

#### 7.3.2. interim状态的解决方案
为了解决这个时序问题，拦截器引入了interim（临时）状态：

1. **初始判断**：当拦截器无法从cookie中获取有效的eeId时，将授权类型设为 `interim`
2. **临时资源**：提供interim版本的静态资源，这些资源包含了获取用户身份的逻辑
3. **身份确认**：当 `/mobile/parking/getEntry` API被调用时，拦截器从响应中提取eeId
4. **Cookie更新**：将cookie与eeId的对应关系存储到数据库中
5. **后续请求**：用户的后续请求将能够正确识别身份，从interim转为authorized或unauthorized

#### 7.3.3. 工作流程示例
```
用户首次访问 → Cookie中无eeId → 授权类型=interim → 提供interim版本页面
     ↓
parking.js加载 → 调用getEntry API → 响应包含eeId → 更新cookie-eeId映射
     ↓
用户后续操作 → Cookie中有eeId → 授权类型=authorized/unauthorized → 提供对应版本页面
```

### 7.4. 静态资源替换机制

#### 7.4.1. 替换路径映射
```
原始路径                              → 替换路径
/mobile/parking/regist               → replacement/{auth_type}/register.html
/mobile/parking/js/parking.js?v3     → replacement/{auth_type}/js/parking.js
/mobile/parking/css/style.css        → replacement/{auth_type}/css/style.css
/mobile/parking/css/weui.min.css     → replacement/{auth_type}/css/weui.min.css
/mobile/parking/js/weui.min.js       → replacement/{auth_type}/js/weui.min.js
```

其中 `{auth_type}` 可以是：
- `interim`: 临时状态资源
- `authorized`: 授权用户资源
- `unauthorized`: 不进行替换，使用原始资源

#### 7.4.2. 替换逻辑
1. **授权检查**：首先通过 `determine_auth_type()` 确定用户的授权类型
2. **路径构建**：根据授权类型和请求的资源文件名构建替换文件的完整路径
3. **文件替换**：读取替换文件内容，更新HTTP响应的内容和头部信息
4. **错误处理**：如果替换文件不存在或读取失败，记录错误但不影响原始响应

### 7.5. API响应处理逻辑

#### 7.5.1. `/mobile/parking/getEntry` API处理
这是最重要的API端点，拦截器对其进行特殊处理：

**对于interim状态**：
- 保持原始响应数据不变
- 从响应中提取eeId并更新cookie-eeId映射关系
- 为后续请求的正确身份识别做准备

**对于authorized状态**：
- 调用 `build_authorized_response()` 构建增强响应
- 从数据库获取用户的最新注册信息
- 添加额外字段：
  - `registrationNumber`: 用户注册的车牌号
  - `registrationLocation`: 注册地点
  - `useMagicPlate`: 是否启用魔法车牌功能
  - `advancedFeatureOn`: 是否启用高级功能
  - `isAdmin`: 是否为管理员
  - `isAuthorized`: 标识为已授权用户
  - `emails`: 关联的邮箱地址列表

**对于unauthorized状态**：
- 保持原始响应数据完全不变

#### 7.5.2. `/mobile/parking/saveEnrollment` API处理
用于处理用户提交的注册数据：

1. **数据解析**：从POST请求中解析JSON数据
2. **字段验证**：验证必需字段（eeId, eeName, registrationNumber, registrationLocation）
3. **数据提取**：提取并规范化数据（去除空格、类型转换等）
4. **数据存储**：将注册信息保存到数据库的enrollments表
5. **响应生成**：返回成功或错误响应

### 7.6. 请求拦截和路由

#### 7.6.1. GET请求处理
```python
def _handle_get_request(flow, auth_type):
    # 未授权用户保持原始资源
    if auth_type == "unauthorized":
        return

    # 静态资源映射和替换
    static_resource_mapping = {
        "/mobile/parking/regist": "register.html",
        "/mobile/parking/js/parking.js?v3": "js/parking.js",
        "/mobile/parking/css/style.css": "css/style.css",
        # ... 其他资源
    }

    # API端点特殊处理
    if flow.request.path == "/mobile/parking/getEntry":
        _handle_get_entry_request(flow, auth_type)
```

#### 7.6.2. POST请求处理
主要处理以下端点：
- `/mobile/parking/saveEnrollment`: 用户注册数据提交
- `/mobile/parking/adminPage`: 管理员页面访问
- `/mobile/parking/admin/*`: 管理员API调用

### 7.7. 管理员功能

#### 7.7.1. 权限验证
所有管理员相关的请求都需要通过权限验证：
1. 从请求cookie中提取并规范化cookie值
2. 查询数据库获取cookie对应的eeId
3. 检查eeId是否在管理员用户列表中
4. 验证失败返回403错误，成功则继续处理

#### 7.7.2. 管理员页面和API
- **管理员页面**：提供用户管理、数据查看等功能的Web界面
- **管理员API**：处理各种管理操作的后端接口
- **访问控制**：所有管理员功能都受到严格的权限控制

### 7.8. 拦截器工作流程总结

#### 7.8.1. 完整的请求处理流程
```
1. 请求到达 → 检查目标域名 → 非目标域名直接放行
2. 目标域名请求 → 提取cookie → 查询数据库获取eeId
3. 根据eeId确定授权类型：
   - 无eeId → interim
   - 有eeId且在授权列表 → authorized
   - 有eeId但不在授权列表 → unauthorized
4. 根据授权类型处理请求：
   - GET请求 → 静态资源替换 + API响应修改
   - POST请求 → 数据验证存储 + 权限检查
5. 返回处理后的响应
```

#### 7.8.2. 关键设计理念
1. **渐进式增强**：不影响普通用户的正常使用，只为授权用户提供额外功能
2. **时序问题解决**：通过interim状态优雅地处理前端加载时序问题
3. **数据一致性**：确保cookie-eeId映射关系的准确性和及时更新
4. **安全性**：严格的权限验证和访问控制
5. **可扩展性**：模块化的设计便于添加新功能和资源类型

#### 7.8.3. 核心技术要点
- **Cookie规范化**：统一处理不同格式的cookie字符串
- **数据库集成**：与SQLite数据库紧密集成，实现数据持久化
- **错误处理**：完善的异常处理机制，确保系统稳定性
- **日志记录**：详细的日志输出，便于调试和监控
- **内容类型处理**：正确设置HTTP响应头，确保浏览器正确解析

## 8. MITM代理启动脚本优化

### 8.1. 优化概述

`launch_mitm_server.py` 经过重大优化，简化了代码结构并移除了不必要的环境变量依赖。

### 8.2. 主要改进

#### 8.2.1. 移除冗余环境变量
- **移除**: `KARPARK_USE_SYSTEM_PYTHON` 环境变量支持
- **原因**: 该环境变量从未被实际使用，造成代码冗余
- **替代**: 统一使用配置文件 `_mitmproxy.yaml` 中的 `python.use_system_python` 设置

#### 8.2.2. 简化方法签名
- **之前**: `run(self, wait=True, force_system=False, test_mode=False)`
- **优化后**: `run(self, wait=True, test_mode=False)`
- **改进**: 移除了 `force_system` 参数，改为通过配置直接控制

#### 8.2.3. 新增配置管理
- **新增**: `set_system_python()` 方法用于临时覆盖配置
- **功能**: 允许命令行参数临时修改Python环境设置

#### 8.2.4. 配置优先级
现在的配置优先级更加清晰：
1. **命令行参数** (`--system-python`) - 最高优先级，临时覆盖
2. **配置文件** (`_mitmproxy.yaml` 中的 `python.use_system_python`) - 默认设置
3. **自动检测** - 虚拟环境检测和项目环境查找

### 8.3. 迁移指南

如果之前使用环境变量 `KARPARK_USE_SYSTEM_PYTHON`，请：

1. **临时使用**: 改为使用 `--system-python` 命令行参数
2. **永久设置**: 在 `karpark/config/_mitmproxy.yaml` 中设置 `python.use_system_python: true`

### 8.4. 向后兼容性

- 所有现有的命令行参数仍然有效
- 配置文件格式保持不变
- Python环境检测逻辑保持一致

## 9. 隐藏功能：魔法模式

### 9.1. 功能概述

魔法模式是一个隐藏的开发者功能，允许用户通过特殊操作启用车牌变体生成功能。该功能主要用于提高停车注册的成功率。

### 9.2. 功能特性

#### 9.2.1. 触发方式
- **隐藏触发**：快速点击 Employee ID 输入框 10 次（3秒内）
- **无视觉提示**：没有明显的UI指示器，保持功能的隐蔽性
- **视觉反馈**：启用时界面切换为亮橙色主题，显示 "Magic Mode Enabled! 🔥" 提示

#### 9.2.2. 主题切换
- **亮橙色主题**：使用鲜明的橙色配色方案替代默认主题
- **CSS实现**：通过添加 `magic-theme` 类到 `body` 元素实现主题切换
- **样式位置**：主题样式定义在 `karpark/mitm/replacement/unregistered/css/style.css` 中

#### 9.2.3. 后端处理
- **URL切换**：魔法模式下POST请求发送到 `/mobile/parking/saveMagic` 而非 `/mobile/parking/saveEntry`
- **车牌变体生成**：后端使用 `karpark.core.magic_plate.create_plate_variants()` 生成车牌变体
- **随机选择**：从生成的变体中随机选择一个替换原始车牌号

### 9.3. 技术实现

#### 9.3.1. 前端实现 (`parking.js`)
```javascript
// 魔法模式变量
var clickCount = 0;
var lastClickTime = 0;
var magicModeEnabled = false;
var CLICK_THRESHOLD = 10;
var CLICK_TIME_WINDOW = 3000; // 3秒

// 点击检测逻辑
$('#parking-ee-id').click(function(event) {
    var currentTime = Date.now();

    // 重置计数器如果时间间隔过长
    if (currentTime - lastClickTime > CLICK_TIME_WINDOW) {
        clickCount = 0;
    }

    clickCount++;
    lastClickTime = currentTime;

    if (clickCount >= CLICK_THRESHOLD) {
        if (!magicModeEnabled) {
            enableMagicMode();
        } else {
            disableMagicMode();
        }
        clickCount = 0;
    }
});

// 主题切换函数
function enableMagicMode() {
    magicModeEnabled = true;
    $('body').addClass('magic-theme');
    weui.toast('Magic Mode Enabled! 🔥', 2000);
}

// URL切换逻辑
var submitUrl = magicModeEnabled ? "/mobile/parking/saveMagic" : "/mobile/parking/saveEntry";
```

#### 9.3.2. 后端实现 (`intercept.py`)

```python
def handle_save_magic_request(flow: http.HTTPFlow) -> None:
    """处理魔法停车注册请求，使用车牌变体替换原始车牌号"""
    try:
        from karpark.registrar.magic_plate import create_plate_variants
        import random

        post_data = _parse_request_content(flow)
        original_car_no = post_data.get('carNo', '')

        # 生成魔法车牌变体
        if original_car_no:
            variants = create_plate_variants(original_car_no)
            if variants:
                magic_car_no = random.choice(variants)
                post_data['carNo'] = magic_car_no
                print(f"[DEBUG] 车牌变体: {original_car_no} → {magic_car_no}")

        # 继续正常处理流程
        request_data = _extract_request_data(flow, post_data)
        save_enrollment_data(flow, request_data)
        _create_success_response_for_save(flow)
    except Exception as e:
        print(f"[ERROR] 魔法模式处理错误: {e}")
```



### 9.4. 使用场景

#### 9.4.1. 生产环境
- **提高成功率**：当正常车牌注册失败时，魔法模式可能提供更高的成功率
- **隐蔽性**：功能对普通用户不可见，避免误用

#### 9.4.2. 功能验证
- **测试验证**：验证车牌变体生成和处理逻辑的正确性

### 9.5. 安全考虑

#### 9.5.1. 访问控制
- **隐藏性**：功能没有明显的UI入口，降低被滥用的风险
- **生产安全**：仅通过特定操作触发，避免意外启用

#### 9.5.2. 日志记录
- **操作记录**：所有魔法模式操作都会记录详细日志
- **审计追踪**：便于追踪和分析功能使用情况

## 10. 注意事项和潜在改进点

*   **AI依赖**: `readmail.py` 中的邮件解析功能依赖DeepSeek AI，需要确保相关API可用或有替代方案。
*   **错误处理**: 需要检查各模块的错误处理和重试机制是否完善。
*   **安全性**: 邮件账户密码、API Cookie等敏感信息直接存储在配置文件中，应考虑更安全的存储方式（如环境变量、密钥管理服务）。
*   **Mitmproxy配置**: `_mitm_access.yaml` 的具体用途和配置方式需要进一步明确。
*   **测试覆盖**: `tests/` 目录的存在说明有测试代码，但文档未涉及测试覆盖情况和如何运行测试。
*   **MITM拦截器**:
    - interim状态的时序处理虽然解决了身份识别问题，但增加了系统复杂性
    - 静态资源替换依赖文件系统，需要确保替换文件的完整性和版本一致性
    - Cookie-eeId映射的数据库存储需要定期清理过期记录
    - 管理员权限验证应考虑添加会话超时和多因素认证
*   **魔法模式**:
    - 隐藏功能的安全性需要持续监控，防止被恶意利用
    - 车牌变体生成算法需要定期更新以适应系统变化

## 8. 统一工具入口点 (karpark-tools)

### 8.1. 概述

为了简化工具管理和提供更好的用户体验，KarPark 项目引入了统一工具入口点 `karpark-tools`。这个新的入口点整合了所有辅助工具，提供了一致的命令行界面。

### 8.2. 设计理念

#### 8.2.1. 统一管理
- **单一入口点**: 通过 `karpark-tools` 访问所有工具
- **一致的用户体验**: 所有工具使用相同的命令行参数风格
- **向后兼容**: 保留原有的独立入口点，确保现有脚本和文档的兼容性

#### 8.2.2. 工具整合
统一工具入口点整合了以下工具：
- **enrollment**: 登记管理器 (`run_enrollment_manager.py`)
- **mitm**: MITM代理服务器 (`run_mitmproxy_server.py`)
- **validate**: 服务器资源验证工具 (`run_monitor_resources.py`)

### 8.3. 使用方法

#### 8.3.1. 基本用法
```bash
# 显示工具菜单和帮助信息
karpark-tools

# 显示特定工具的帮助
karpark-tools mitm --help
karpark-tools validate --help
```

#### 8.3.2. 登记管理器
```bash
# 启动登记管理器（交互式模式）
karpark-tools enrollment
```

#### 8.3.3. MITM代理服务器
```bash
# 启动MITM代理（前台模式）
karpark-tools mitm

# 启动MITM代理（后台模式）
karpark-tools mitm --no-wait

# 重启代理
karpark-tools mitm --restart

# 停止代理
karpark-tools mitm --stop

# 查看状态
karpark-tools mitm --status
```

#### 8.3.4. 资源验证工具
```bash
# 交互式模式
karpark-tools validate --interactive

# 初始化基准资源
karpark-tools validate init

# 检查资源变化
karpark-tools validate check

# 显示验证报告
karpark-tools validate report
```

### 8.4. 向后兼容性

为了确保现有脚本和文档的兼容性，所有原有的入口点仍然保留：
- `karpark-enrollment` → `karpark-tools enrollment`
- `karpark-mitm` → `karpark-tools mitm`
- `karpark-validate` → `karpark-tools validate`

### 8.5. 技术实现

#### 8.5.1. 文件结构
```
scripts/
├── run_karpark_tools.py          # 统一工具入口点
├── run_enrollment_manager.py     # 登记管理器（保留）
├── run_mitmproxy_server.py       # MITM代理（保留）
└── run_monitor_resources.py      # 资源验证（保留）
```

#### 8.5.2. setup.py 配置
```python
entry_points={
    "console_scripts": [
        "karpark=karpark.main:main",
        "karpark-tools=scripts.run_karpark_tools:main",  # 新增
        # 保留旧的入口点以确保向后兼容性
        "karpark-enrollment=scripts.run_enrollment_manager:main",
        "karpark-mitm=scripts.run_mitm_server:main",
        "karpark-validate=scripts.run_validate_server_resources:cli",
    ],
},
```

### 8.6. 迁移建议

#### 8.6.1. 新用户
推荐新用户直接使用 `karpark-tools` 入口点：
```bash
karpark-tools enrollment
karpark-tools mitm
karpark-tools validate
```

#### 8.6.2. 现有用户
现有用户可以继续使用原有的命令，也可以逐步迁移到新的统一入口点：
```bash
# 原有方式（仍然可用）
karpark-enrollment
karpark-mitm
karpark-validate

# 新的统一方式
karpark-tools enrollment
karpark-tools mitm
karpark-tools validate
```

## 9. 服务器资源验证工具

### 9.1. 工具概述

**文件**: `scripts/run_validate_server_resources.py`

服务器资源验证工具用于监控SAP Labs Digital Parking系统的前端资源变化。由于本项目基于对parking.js?v3等JavaScript文件的逆向工程，当服务器端资源发生变化时，项目可能需要相应调整。

### 9.2. 核心功能

#### 9.2.1. 资源发现与下载
- **自动解析**: 解析HTML页面中的CSS和JS资源链接
- **批量下载**: 并发下载所有发现的资源文件
- **完整性验证**: 使用SHA256哈希值确保下载完整性

#### 9.2.2. 基准版本管理
- **基准建立**: 下载并保存当前资源作为比较基准
- **元数据存储**: 保存资源URL、类型、哈希值、下载时间等信息
- **版本控制**: 支持基准版本的更新和重建

#### 9.2.3. 变化检测
- **哈希比较**: 通过SHA256哈希值快速检测内容变化
- **详细报告**: 生成包含变化、新增、缺失资源的详细报告
- **分类统计**: 按资源类型（HTML、CSS、JS）分类显示变化

#### 9.2.4. 智能Cookie管理
- **集成输入**: 在需要时自动提示用户输入Cookie
- **状态验证**: 实时验证Cookie有效性和认证状态
- **自动保存**: Cookie自动保存供后续使用
- **智能重用**: 优先使用已保存的有效Cookie

### 9.3. 认证机制

#### 9.3.1. WeChat认证要求
- 目标系统只能通过微信小程序访问
- 需要有效的JSESSIONID和__VCAP_ID__Cookie
- Cookie具有时效性，需要定期更新

#### 9.3.2. Cookie获取流程
1. 安装并启动Fiddler Classic
2. 配置手机WiFi代理（IP:电脑IP，端口:8888）
3. 在微信中打开停车登记小程序
4. 在Fiddler中找到对parking.labs.sap.cn的请求
5. 复制完整的Cookie头部值

#### 9.3.3. 认证验证
- **重定向检测**: 识别WeChat OAuth重定向
- **内容验证**: 检查响应内容是否为有效页面
- **状态码检查**: 验证HTTP响应状态
- **实时反馈**: 显示Cookie状态和有效性

### 9.4. 使用方式

#### 9.4.1. 命令行模式
```bash
# 初始化基准版本（集成Cookie输入）
python scripts/run_change_monitor.py init

# 检查资源变化（集成Cookie输入和状态显示）
python scripts/run_change_monitor.py check

# 显示验证报告
python scripts/run_change_monitor.py report

# 检查Cookie状态
python scripts/run_change_monitor.py status
```

#### 9.4.2. 交互式模式
```bash
# 启动交互式菜单
python scripts/run_change_monitor.py --interactive
```

交互式菜单包含：
- 🔍 检查资源变化（集成Cookie管理）
- 📊 显示验证报告
- 🔄 初始化基准资源（集成Cookie管理）
- 📋 显示基准信息
- 🧹 清理临时文件
- ❓ 显示帮助信息

### 9.5. 技术实现

#### 9.5.1. 核心类结构
- **ResourceValidator**: 主要验证类，处理下载、比较、报告生成
- **ResourceType**: 资源类型常量（HTML、CSS、JS）
- **认证处理**: 集成的Cookie输入、验证、保存机制

#### 9.5.2. 文件组织
```
data/server_resources/
├── baseline/           # 基准版本资源
│   ├── register.html   # 主页面
│   ├── css/           # CSS文件
│   ├── js/            # JavaScript文件
│   └── metadata.json  # 资源元数据
├── temp/              # 临时下载资源
├── cookie.txt         # 保存的Cookie
└── validation_report.json # 验证报告
```

#### 9.5.3. 错误处理
- **网络异常**: 超时、连接错误的重试机制
- **认证失败**: 详细的错误信息和解决建议
- **文件操作**: 目录创建、权限检查、磁盘空间验证

### 9.6. 集成与自动化

#### 9.6.1. CI/CD集成
```yaml
# GitHub Actions示例
- name: Validate Server Resources
  run: |
    python scripts/run_validate_server_resources.py check --cookie "${{ secrets.PARKING_COOKIE }}"
```

#### 9.6.2. 监控建议
- 定期运行资源验证（建议每日）
- 设置变化检测告警
- 维护Cookie的有效性
- 监控验证工具的执行状态

### 9.7. 维护要点

#### 9.7.1. Cookie管理
- Cookie具有时效性，需要定期更新
- 建议建立Cookie更新提醒机制
- 保持Fiddler Classic工具的可用性

#### 9.7.2. 资源监控
- 关注parking.js版本变化（当前v3）
- 监控CSS样式文件的更新
- 注意HTML结构的变化

#### 9.7.3. 故障排除
- 验证网络连接到parking.labs.sap.cn
- 检查Cookie格式和有效性
- 确认微信小程序的可访问性
- 查看详细的错误日志和建议