# MITM Proxy Restart and Email Monitoring Guide

This guide explains the new restart functionality and email monitoring system for the Karpark MITM proxy.

## Features

### 1. Process Management
- **Process Tracking**: Automatically tracks running MITM proxy processes with PID files
- **Restart Functionality**: Graceful restart of MITM proxy processes
- **Status Monitoring**: Real-time status information including CPU, memory usage
- **Automatic Cleanup**: Handles stale PID files and process cleanup

### 2. Email Monitoring
- **Remote Restart**: Restart MITM proxy via email commands
- **Security**: Password-protected restart commands
- **Configurable**: Customizable keywords, subjects, and check intervals
- **Automatic**: Runs in background when MITM proxy is started

## Configuration

### Email Monitor Settings (`karpark/config/_mitmproxy.yaml`)

```yaml
email_monitor:
  # Enable email monitoring for restart commands
  enabled: true
  
  # Check interval in minutes (how often to check for restart emails)
  check_interval: 5
  
  # Restart command configuration
  restart_command:
    # Password required in email for restart (security measure)
    # If this password appears anywhere in email subject or body, restart will be triggered
    password: "karpark_restart_2024"
  
  # Email retention settings
  retention:
    # Days to keep processed restart emails in database
    days: 7
```

## Usage

### Command Line Options

#### Start MITM Proxy (with email monitoring)
```bash
# Start in background with automatic email monitoring
python scripts/run_interceptor.py --no-wait

# Start in foreground (interactive mode)
python scripts/run_interceptor.py
```

#### Restart MITM Proxy
```bash
# Restart the proxy
python scripts/run_interceptor.py --restart
```

#### Stop MITM Proxy
```bash
# Stop the proxy
python scripts/run_interceptor.py --stop
```

#### Check Status
```bash
# Check proxy status
python scripts/run_interceptor.py --status
```

#### Email Monitoring Only
```bash
# Start only email monitoring (proxy must be running separately)
python scripts/run_interceptor.py --email-monitor
```

### Email-Based Restart

To restart the MITM proxy via email:

1. **Send an email** to the configured email account with:
   - **Subject**: One of the allowed subjects (e.g., "karpark restart request")
   - **Content**: Must contain all required keywords and the password

2. **Example Email**:
   ```
   Subject: karpark restart request
   
   Please restart the mitmproxy service for karpark.
   Password: karpark_restart_2024
   ```

3. **Security Requirements**:
   - Email subject must match one of the allowed patterns
   - Email content must contain all required keywords: "restart", "mitmproxy", "karpark"
   - Email content must contain the correct password
   - All checks are case-insensitive

## Process Management

### PID Files
- **Location**: `karpark/data/mitm_proxy.pid`
- **Metadata**: `karpark/data/mitm_proxy_status.json`
- **Auto-cleanup**: Stale files are automatically removed

### Process Information
The status command shows:
- Process ID (PID)
- CPU usage percentage
- Memory usage in MB
- Start time
- Port number
- Email monitoring status

## Security Considerations

### Email Security
- **Password Protection**: All restart commands require a password
- **Keyword Validation**: Multiple keywords must be present
- **Subject Filtering**: Only specific subject patterns are allowed
- **Processing Tracking**: Prevents duplicate processing of emails

### Process Security
- **Graceful Shutdown**: Processes are terminated gracefully before restart
- **Timeout Handling**: Force kill if graceful shutdown fails
- **Permission Checks**: Proper permission handling for process management

## Troubleshooting

### Common Issues

#### Email Monitoring Not Working
1. Check configuration in `_mitmproxy.yaml`
2. Verify email account settings in `base.yaml`
3. Check logs for email connection errors
4. Ensure required dependencies are installed: `pip install psutil`

#### Process Management Issues
1. Check if PID files exist in `karpark/data/`
2. Verify process permissions
3. Check for zombie processes: `ps aux | grep mitmdump`
4. Clean up manually if needed: `rm karpark/data/mitm_proxy.*`

#### Restart Not Working
1. Verify email contains all required elements
2. Check email subject matches allowed patterns
3. Ensure password is correct and present in email
4. Check email processing logs

### Logs
- Email monitoring logs are written to the main application log
- Process management events are logged with appropriate levels
- Use debug logging for detailed troubleshooting

## Testing

### Test Email Monitoring
```bash
# Run email monitoring test
python scripts/test_email_monitor.py
```

### Manual Testing
1. Start MITM proxy: `python scripts/run_mitm_server.py --no-wait`
2. Check status: `python scripts/run_mitm_server.py --status`
3. Send test email with restart command
4. Monitor logs for restart activity
5. Verify proxy restarted: `python scripts/run_mitm_server.py --status`

## Dependencies

### Required Packages
- `psutil`: For process management
- `mitmproxy`: Core proxy functionality
- `coloredlogs`: Enhanced logging

### Installation
```bash
python scripts/run_interceptor.py --install-deps
```

## Integration

### With Existing Systems
- Email monitoring integrates with existing `EmailClient`
- Process management works with current launcher
- Configuration follows existing YAML structure
- Logging uses existing `colorlog` system

### Automatic Startup
When starting MITM proxy in background mode (`--no-wait`), email monitoring automatically starts if enabled in configuration.

## Future Enhancements

- Web-based management interface
- Multiple restart triggers (file-based, API-based)
- Enhanced security with token-based authentication
- Process health monitoring and auto-restart
- Integration with system service managers
