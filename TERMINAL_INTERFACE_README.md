# 终端界面版本说明

本项目提供了两个版本的终端界面：

## 1. Curses 版本 (`scripts/start.py`)

### 特点：
- ✅ 基于标准 `curses` 库
- ✅ 无需额外依赖（Windows 需要 `windows-curses`）
- ✅ 稳定可靠
- ❌ **不支持 ANSI 颜色输出** - 子程序的彩色输出会被过滤
- ❌ 颜色支持有限

### 启动方式：
```bash
python scripts/start.py
# 或
start.bat
```

### 最小终端要求：
- 120x30 字符（已调整为原来的两倍）

## 2. Blessed 版本 (`scripts/start_blessed.py`) ⭐ 推荐

### 特点：
- ✅ 基于 `blessed` 库
- ✅ **完整支持 ANSI 颜色输出** - 保留子程序的所有颜色信息
- ✅ 更好的跨平台兼容性
- ✅ 更丰富的颜色和样式支持
- ✅ 更流畅的界面体验
- ❌ 需要安装额外依赖

### 安装依赖：
```bash
pip install blessed psutil
```

### 启动方式：
```bash
python scripts/start_blessed.py
# 或
start_blessed.bat
```

### 最小终端要求：
- 120x30 字符（已调整为原来的两倍）

## 主要区别

| 特性 | Curses 版本 | Blessed 版本 |
|------|-------------|--------------|
| ANSI 颜色支持 | ❌ 不支持 | ✅ 完整支持 |
| 依赖要求 | 最小 | 需要 blessed |
| 子程序颜色输出 | 被过滤 | 完整保留 |
| 界面流畅度 | 良好 | 更好 |
| 跨平台兼容性 | 一般 | 优秀 |

## 推荐使用

**推荐使用 Blessed 版本**，因为它能够：

1. **保留完整的 ANSI 颜色输出** - 这是主要优势
2. 显示子程序（如 interceptor、auto-registrar）的彩色日志
3. 提供更好的用户体验
4. 支持更丰富的终端特性

## 功能对比

两个版本在功能上完全相同：

- ✅ 启动/停止 Interceptor
- ✅ 启动/停止 Auto-registrar  
- ✅ 清空输出缓冲区
- ✅ 在新窗口启动 Enrollment Manager
- ✅ 在新窗口启动 Change Monitor
- ✅ 停止外部程序
- ✅ 显示系统信息
- ✅ 实时输出显示
- ✅ 键盘导航（上下箭头、回车、ESC）

## 界面布局

两个版本都使用相同的布局：

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                    TERMINAL INTERFACE - [VERSION]                          │
├─────────────────┬───────────────────────────────────────────────────────────┤
│ CONTROL MENU    │ OVERALL INFO                                              │
│ > Start Inter.. │ [timestamp] System messages...                            │
│   Start Auto... │                                                           │
│   Stop Inter... │                                                           │
│   ...           ├───────────────────────────────────────────────────────────┤
│                 │ INTERCEPTOR                                               │
│                 │ [timestamp] Interceptor output with colors...             │
│                 │                                                           │
│                 ├───────────────────────────────────────────────────────────┤
│                 │ AUTO-REGISTRAR                                            │
│                 │ [timestamp] Auto-registrar output with colors...          │
│                 │                                                           │
├─────────────────┴───────────────────────────────────────────────────────────┤
│ UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit | P1:RUN P2:STOP | Status   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 注意事项

1. **菜单宽度保持不变** - 只有右侧输出区域变大
2. **窗口大小要求** - 最小 120x30 字符
3. **颜色输出** - Blessed 版本能显示子程序的完整颜色信息
4. **兼容性** - 两个版本可以并存，根据需要选择使用
