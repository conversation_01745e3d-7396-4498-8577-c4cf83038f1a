# 终端界面说明

本项目使用基于 `blessed` 库的现代终端界面。

- ✅ 基于 `blessed` 库
- ✅ **完整支持 ANSI 颜色输出** - 保留子程序的所有颜色信息
- ✅ 智能时间戳处理 - 避免重复时间戳
- ✅ 更好的跨平台兼容性
- ✅ 更丰富的颜色和样式支持
- ✅ 更流畅的界面体验
- ✅ 菜单高亮显示优化

## 安装依赖

```bash
pip install blessed psutil
```

## 启动方式

```bash
python scripts/start.py
# 或
start.bat
```

## 最小终端要求

- 120x30 字符（已调整为原来的两倍）

## 主要功能

- ✅ 启动/停止 Interceptor
- ✅ 启动/停止 Auto-registrar
- ✅ 清空输出缓冲区
- ✅ 在新窗口启动 Enrollment Manager
- ✅ 在新窗口启动 Change Monitor
- ✅ 停止外部程序
- ✅ 显示系统信息
- ✅ 实时输出显示（保留完整 ANSI 颜色）
- ✅ 智能时间戳处理（避免重复）
- ✅ 键盘导航（上下箭头、回车、ESC、R刷新）

## 界面布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                    TERMINAL INTERFACE - PYTHON VERSION                     │
├─────────────────┬───────────────────────────────────────────────────────────┤
│ CONTROL MENU    │ OVERALL INFO                                              │
│ > Start Inter.. │ [timestamp] System messages...                            │
│   Start Auto... │                                                           │
│   Stop Inter... │                                                           │
│   ...           ├───────────────────────────────────────────────────────────┤
│                 │ INTERCEPTOR                                               │
│                 │ 2024-01-01 12:34:56 Interceptor output with colors...    │
│                 │                                                           │
│                 ├───────────────────────────────────────────────────────────┤
│                 │ AUTO-REGISTRAR                                            │
│                 │ 2024-01-01 12:34:56 Auto-registrar output with colors... │
│                 │                                                           │
├─────────────────┴───────────────────────────────────────────────────────────┤
│ UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit | P1:RUN P2:STOP | Status   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 重要特性

1. **完整 ANSI 颜色支持** - 显示子程序的完整颜色信息
2. **智能时间戳处理** - 自动检测并避免重复时间戳
3. **菜单高亮优化** - 解决了菜单选择时的颜色残留问题
4. **窗口大小要求** - 最小 120x30 字符（双倍大小）
5. **菜单宽度保持不变** - 只有右侧输出区域变大

## 使用说明

- **上下箭头**: 选择菜单项
- **回车**: 执行选中的菜单项
- **ESC 或 Q**: 退出程序
- **R**: 强制刷新界面
