#!/usr/bin/env python3

import time
from dataclasses import dataclass
from enum import auto, Enum
from functools import wraps


class TimeUnit(Enum):
    """Time unit enumeration"""
    SECOND = auto()
    MINUTE = auto()
    HOUR = auto()


@dataclass
class TimeInterval:
    """Time interval configuration"""
    value: float
    unit: TimeUnit

    @property
    def seconds(self) -> float:
        """Convert to seconds"""
        if self.unit == TimeUnit.SECOND:
            return self.value
        elif self.unit == TimeUnit.MINUTE:
            return self.value * 60
        elif self.unit == TimeUnit.HOUR:
            return self.value * 3600
        raise ValueError(f"Unknown time unit: {self.unit}")
