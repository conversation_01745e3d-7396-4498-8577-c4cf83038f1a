#!/usr/bin/env python3
""""""
import re
from typing import Optional, Union, List


def normalize_cookie(cookie_str):
    """Normalize cookie string by extracting JSESSIONID and __VCAP_ID__ and reassembling them in a fixed order.
    Returns the original cookie if it doesn't match the expected pattern."""
    # Check if the string contains the two cookie keys we care about
    if 'JSESSIONID=' in cookie_str and '__VCAP_ID__=' in cookie_str:
        jsessionid = None
        vcap_id = None

        # Split the cookie string and extract values
        cookie_parts = cookie_str.split(';')
        for part in cookie_parts:
            part = part.strip()
            if part.startswith('JSESSIONID='):
                jsessionid = part
            elif part.startswith('__VCAP_ID__='):
                vcap_id = part

        # Reassemble the cookie in a fixed order
        normalized_cookie = ""
        if jsessionid:
            normalized_cookie += jsessionid
        if vcap_id:
            if normalized_cookie:
                normalized_cookie += "; "
            normalized_cookie += vcap_id

        return normalized_cookie
    else:
        # Return the original cookie if it doesn't match the expected pattern
        return cookie_str

def normalize_emails(emails: Optional[Union[List[str], str]]) -> List[str]:
    """Convert emails input to list format"""
    if not emails:
        return []
    if isinstance(emails, str):
        separators = r'[,;]'
        return [e.strip() for e in re.split(separators, emails) if e.strip()]
    return emails