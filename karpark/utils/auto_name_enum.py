from enum import Enum, auto

class AutoNameEnum(Enum):
    """Convert enum name to upper case format.

    Examples:
        A -> A
        B -> B
        C -> C
    """
    @staticmethod
    def _generate_next_value_(name, start, count, last_values):  # pytype: disable=signature-mismatch
        return f"#{name.upper()}#"


class CamelCaseEnum(Enum):
    @staticmethod
    def _generate_next_value_(name, start, count, last_values):  # pytype: disable=signature-mismatch
        """Convert enum name to camelCase format.

        Examples:
            CAR_PLATE_NUMBER -> carPlateNumber
            USER_ID -> userId
            STATUS -> status
        """
        # Split by underscore and convert to lowercase
        parts = name.lower().split('_')

        # First part stays lowercase, capitalize first letter of subsequent parts
        if len(parts) == 1:
            return parts[0]

        camel_case = parts[0] + ''.join(word.capitalize() for word in parts[1:])
        return camel_case

_ = auto()  # Keep, auto import for enum subclasses

if __name__ == "__main__":
    # Test AutoNameEnum
    class Foo(AutoNameEnum):
        A = auto()
        B = auto()
        C = auto()

    print("=== AutoNameEnum Test ===")
    print(Foo.A)
    print(Foo.B)
    print(Foo.C)
    print(Foo.A.value)
    print(Foo.B.value)
    print(Foo.C.value)

    # Test CamelCaseEnum
    class TestCamelCase(CamelCaseEnum):
        CAR_PLATE_NUMBER = auto()
        USER_ID = auto()
        STATUS = auto()
        FIRST_NAME = auto()
        LAST_LOGIN_TIME = auto()

    print("\n=== CamelCaseEnum Test ===")
    print(f"CAR_PLATE_NUMBER -> {TestCamelCase.CAR_PLATE_NUMBER.value}")
    print(f"USER_ID -> {TestCamelCase.USER_ID.value}")
    print(f"STATUS -> {TestCamelCase.STATUS.value}")
    print(f"FIRST_NAME -> {TestCamelCase.FIRST_NAME.value}")
    print(f"LAST_LOGIN_TIME -> {TestCamelCase.LAST_LOGIN_TIME.value}")