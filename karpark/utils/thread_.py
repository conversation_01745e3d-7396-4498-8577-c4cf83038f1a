#!/usr/bin/env python3

from functools import wraps
from threading import Thread

from karpark.colorlog import logger


def new_thread(func):
    @wraps(func)
    def new_thread_wrapper(*args, **kwargs):
        logger.debug("Function name：{}".format(func.__name__))
        logger.debug("Positional arguments：{}".format({args}))
        # logger.debug("Positional arguments：{}".format({kwargs}))
        t = Thread(target=func, args=args, kwargs=kwargs)
        try:
            t.start()
        except Exception as e:
            logger.error("Error starting thread: {}".format(e))
            raise e

    return new_thread_wrapper
