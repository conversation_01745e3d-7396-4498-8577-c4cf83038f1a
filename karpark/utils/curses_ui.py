#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Curses UI utilities to replace Rich functionality

This module provides curses-based alternatives to Rich components like
Console, Panel, Table, etc.
"""

import curses
import sys
import os
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path


class CursesConsole:
    """Curses-based console replacement for Rich Console"""
    
    def __init__(self):
        self.stdscr = None
        self.colors_initialized = False
        
    def init_colors(self):
        """Initialize color pairs"""
        if not self.colors_initialized and curses.has_colors():
            curses.start_color()
            curses.init_pair(1, curses.COLOR_WHITE, curses.COLOR_BLACK)   # Default
            curses.init_pair(2, curses.COLOR_RED, curses.COLOR_BLACK)     # Error
            curses.init_pair(3, curses.COLOR_GREEN, curses.COLOR_BLACK)   # Success
            curses.init_pair(4, curses.COLOR_YELLOW, curses.COLOR_BLACK)  # Warning
            curses.init_pair(5, curses.COLOR_BLUE, curses.COLOR_BLACK)    # Info
            curses.init_pair(6, curses.COLOR_CYAN, curses.COLOR_BLACK)    # Accent
            curses.init_pair(7, curses.COLOR_MAGENTA, curses.COLOR_BLACK) # Special
            self.colors_initialized = True
    
    def print(self, text: str, style: str = "default"):
        """Print text with optional styling"""
        # For non-curses mode, just print normally
        if not self.stdscr:
            # Remove rich markup and print
            clean_text = self._strip_markup(text)
            print(clean_text)
            return
            
        # Curses mode printing would go here
        clean_text = self._strip_markup(text)
        print(clean_text)
    
    def input(self, prompt: str = "") -> str:
        """Get user input"""
        clean_prompt = self._strip_markup(prompt)
        return input(clean_prompt)
    
    def _strip_markup(self, text: str) -> str:
        """Remove Rich markup from text"""
        import re
        # Remove [tag] and [/tag] markup
        text = re.sub(r'\[/?[^\]]+\]', '', text)
        return text


class CursesPanel:
    """Curses-based panel replacement for Rich Panel"""
    
    @staticmethod
    def fit(content: str, title: str = "", border_style: str = "default", padding: Tuple[int, int] = (0, 1)) -> str:
        """Create a simple text panel"""
        console = CursesConsole()
        clean_content = console._strip_markup(content)
        clean_title = console._strip_markup(title)
        
        lines = clean_content.split('\n')
        max_width = max(len(line) for line in lines) if lines else 0
        
        # Add title if provided
        if clean_title:
            max_width = max(max_width, len(clean_title) + 4)
        
        # Create border
        border_char = '─'
        corner_char = '┌┐└┘'
        side_char = '│'
        
        result = []
        
        # Top border
        if clean_title:
            title_line = f"┌─ {clean_title} " + "─" * (max_width - len(clean_title) - 4) + "┐"
            result.append(title_line)
        else:
            result.append("┌" + "─" * (max_width + 2) + "┐")
        
        # Content lines
        for line in lines:
            padded_line = f"│ {line:<{max_width}} │"
            result.append(padded_line)
        
        # Bottom border
        result.append("└" + "─" * (max_width + 2) + "┘")
        
        return '\n'.join(result)


class CursesTable:
    """Curses-based table replacement for Rich Table"""
    
    def __init__(self, title: str = ""):
        self.title = title
        self.columns = []
        self.rows = []
        
    def add_column(self, header: str, style: str = "default", justify: str = "left"):
        """Add a column to the table"""
        self.columns.append({
            'header': header,
            'style': style,
            'justify': justify,
            'width': len(header)
        })
    
    def add_row(self, *values):
        """Add a row to the table"""
        row = list(values)
        self.rows.append(row)
        
        # Update column widths
        for i, value in enumerate(row):
            if i < len(self.columns):
                self.columns[i]['width'] = max(self.columns[i]['width'], len(str(value)))
    
    def __str__(self) -> str:
        """Render table as string"""
        if not self.columns:
            return ""
        
        console = CursesConsole()
        result = []
        
        # Title
        if self.title:
            clean_title = console._strip_markup(self.title)
            result.append(f"\n{clean_title}")
            result.append("=" * len(clean_title))
        
        # Calculate total width
        total_width = sum(col['width'] for col in self.columns) + len(self.columns) * 3 + 1
        
        # Header separator
        result.append("┌" + "┬".join("─" * (col['width'] + 2) for col in self.columns) + "┐")
        
        # Header row
        header_row = "│"
        for col in self.columns:
            clean_header = console._strip_markup(col['header'])
            header_row += f" {clean_header:<{col['width']}} │"
        result.append(header_row)
        
        # Header separator
        result.append("├" + "┼".join("─" * (col['width'] + 2) for col in self.columns) + "┤")
        
        # Data rows
        for row in self.rows:
            data_row = "│"
            for i, value in enumerate(row):
                if i < len(self.columns):
                    clean_value = console._strip_markup(str(value))
                    width = self.columns[i]['width']
                    data_row += f" {clean_value:<{width}} │"
            result.append(data_row)
        
        # Bottom border
        result.append("└" + "┴".join("─" * (col['width'] + 2) for col in self.columns) + "┘")
        
        return '\n'.join(result)


class CursesPrompt:
    """Curses-based prompt replacement for Rich Prompt"""
    
    @staticmethod
    def ask(question: str, default: str = None, choices: List[str] = None) -> str:
        """Ask user for input"""
        console = CursesConsole()
        clean_question = console._strip_markup(question)
        
        if choices:
            clean_question += f" ({'/'.join(choices)})"
        
        if default:
            clean_question += f" [{default}]"
        
        clean_question += ": "
        
        while True:
            response = input(clean_question).strip()
            
            if not response and default:
                return default
            
            if choices and response not in choices:
                print(f"Please choose from: {', '.join(choices)}")
                continue
                
            return response


class CursesConfirm:
    """Curses-based confirm replacement for Rich Confirm"""
    
    @staticmethod
    def ask(question: str, default: bool = False) -> bool:
        """Ask user for yes/no confirmation"""
        console = CursesConsole()
        clean_question = console._strip_markup(question)
        
        default_text = "Y/n" if default else "y/N"
        clean_question += f" ({default_text}): "
        
        while True:
            response = input(clean_question).strip().lower()
            
            if not response:
                return default
            
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please answer 'y' or 'n'")


# Create global instances for easy import
console = CursesConsole()
Panel = CursesPanel
Table = CursesTable
Prompt = CursesPrompt
Confirm = CursesConfirm


def init_curses_mode():
    """Initialize curses mode for full-screen applications"""
    stdscr = curses.initscr()
    curses.noecho()
    curses.cbreak()
    stdscr.keypad(True)
    curses.curs_set(0)
    
    console.stdscr = stdscr
    console.init_colors()
    
    return stdscr


def cleanup_curses():
    """Cleanup curses mode"""
    if console.stdscr:
        curses.nocbreak()
        console.stdscr.keypad(False)
        curses.echo()
        curses.endwin()
        console.stdscr = None
