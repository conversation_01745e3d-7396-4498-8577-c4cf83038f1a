#!/usr/bin/env python3
"""
MITM Proxy Process Manager

This module provides utilities for managing MITM proxy processes including:
- Process tracking and PID management
- Process restart functionality
- Process status monitoring
"""

import time
import psutil
import json
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from karpark.common.paths import paths
from karpark.common.config import MitmproxyConfig


# Constants
PID_FILE = paths.data_dir / 'mitm_proxy.pid'
STATUS_FILE = paths.data_dir / 'mitm_proxy_status.json'
RESTART_HISTORY_FILE = paths.data_dir / 'mitm_restart_history.json'


class ProcessManager:
    """Manages MITM proxy processes."""
    
    def __init__(self):
        self.pid_file = PID_FILE
        self.status_file = STATUS_FILE
        self._ensure_data_dir()
    
    def _ensure_data_dir(self):
        """Ensure data directory exists."""
        self.pid_file.parent.mkdir(parents=True, exist_ok=True)
    
    def save_pid(self, pid: int, command: list, start_time: datetime = None) -> None:
        """Save process PID and metadata to file.
        
        Args:
            pid: Process ID
            command: Command used to start the process
            start_time: Process start time (defaults to current time)
        """
        if start_time is None:
            start_time = datetime.now()
            
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
            
            # Save additional metadata
            import json
            metadata = {
                'pid': pid,
                'command': command,
                'start_time': start_time.isoformat(),
                'port': MitmproxyConfig.Server.port
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
            print(f"Saved MITM proxy PID: {pid}")
            
        except Exception as e:
            print(f"Failed to save PID: {e}")
    
    def get_pid(self) -> Optional[int]:
        """Get saved process PID.
        
        Returns:
            Process PID if found and valid, None otherwise
        """
        try:
            if not self.pid_file.exists():
                return None
                
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
                
            # Verify process is still running
            if self.is_process_running(pid):
                return pid
            else:
                # Clean up stale PID file
                self.cleanup_pid_files()
                return None
                
        except (ValueError, FileNotFoundError, PermissionError) as e:
            print(f"Could not read PID file: {e}")
            return None
    
    def is_process_running(self, pid: int) -> bool:
        """Check if process with given PID is running.
        
        Args:
            pid: Process ID to check
            
        Returns:
            True if process is running, False otherwise
        """
        try:
            process = psutil.Process(pid)
            return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
    
    def get_process_info(self) -> Optional[Dict[str, Any]]:
        """Get detailed process information.
        
        Returns:
            Process information dict or None if no process found
        """
        pid = self.get_pid()
        if not pid:
            return None
            
        try:
            process = psutil.Process(pid)
            
            # Load metadata if available
            metadata = {}
            if self.status_file.exists():
                import json
                with open(self.status_file, 'r') as f:
                    metadata = json.load(f)
            
            return {
                'pid': pid,
                'status': process.status(),
                'cpu_percent': process.cpu_percent(),
                'memory_info': process.memory_info()._asdict(),
                'create_time': datetime.fromtimestamp(process.create_time()),
                'command': metadata.get('command', []),
                'port': metadata.get('port', MitmproxyConfig.Server.port)
            }
            
        except (psutil.NoSuchProcess, psutil.AccessDenied, Exception) as e:
            print(f"Failed to get process info: {e}")
            return None
    
    def stop_process(self, timeout: int = 10) -> bool:
        """Stop the MITM proxy process.
        
        Args:
            timeout: Timeout in seconds for graceful shutdown
            
        Returns:
            True if process was stopped successfully, False otherwise
        """
        pid = self.get_pid()
        if not pid:
            print("No MITM proxy process found to stop")
            return True
            
        try:
            process = psutil.Process(pid)
            print(f"Stopping MITM proxy process (PID: {pid})")
            
            # Try graceful shutdown first
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=timeout)
                print("MITM proxy process stopped gracefully")
            except psutil.TimeoutExpired:
                # Force kill if graceful shutdown failed
                print("Graceful shutdown timed out, force killing process")
                process.kill()
                process.wait(timeout=5)
                print("MITM proxy process force killed")
            
            self.cleanup_pid_files()
            return True
            
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"Failed to stop process: {e}")
            self.cleanup_pid_files()
            return False
    
    def cleanup_pid_files(self) -> None:
        """Clean up PID and status files."""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
            if self.status_file.exists():
                self.status_file.unlink()
            print("Cleaned up MITM proxy PID files")
        except Exception as e:
            print(f"Failed to cleanup PID files: {e}")
    
    def get_last_restart_time(self) -> Optional[datetime]:
        """Get the timestamp of the last restart.

        Returns:
            Last restart datetime or None if no restart history
        """
        try:
            if not RESTART_HISTORY_FILE.exists():
                return None

            with open(RESTART_HISTORY_FILE, 'r') as f:
                history = json.load(f)

            if history.get('restarts'):
                last_restart = history['restarts'][-1]
                return datetime.fromisoformat(last_restart['timestamp'])

        except Exception as e:
            print(f"Failed to read restart history: {e}")

        return None

    def record_restart(self) -> None:
        """Record a restart event in the history."""
        try:
            restart_time = datetime.now()

            # Load existing history
            history = {'restarts': []}
            if RESTART_HISTORY_FILE.exists():
                try:
                    with open(RESTART_HISTORY_FILE, 'r') as f:
                        history = json.load(f)
                except:
                    pass  # Use default empty history

            # Add new restart record
            history['restarts'].append({
                'timestamp': restart_time.isoformat(),
                'type': 'email_triggered'
            })

            # Keep only last 50 restart records
            history['restarts'] = history['restarts'][-50:]

            # Save updated history
            with open(RESTART_HISTORY_FILE, 'w') as f:
                json.dump(history, f, indent=2)

            print(f"Recorded restart at {restart_time}")

        except Exception as e:
            print(f"Failed to record restart: {e}")

    def is_restart_allowed(self) -> tuple[bool, Optional[datetime], Optional[int]]:
        """Check if restart is allowed based on cooldown period.

        Returns:
            Tuple of (is_allowed, last_restart_time, remaining_minutes)
        """
        last_restart = self.get_last_restart_time()
        if not last_restart:
            return True, None, None

        cooldown_minutes = MitmproxyConfig.EmailMonitor.RestartCommand.cooldown_minutes
        cooldown_period = timedelta(minutes=cooldown_minutes)
        time_since_restart = datetime.now() - last_restart

        if time_since_restart >= cooldown_period:
            return True, last_restart, None
        else:
            remaining_time = cooldown_period - time_since_restart
            remaining_minutes = int(remaining_time.total_seconds() / 60) + 1
            return False, last_restart, remaining_minutes

    def restart_process(self, launcher_instance) -> bool:
        """Restart the MITM proxy process.

        Args:
            launcher_instance: MitmdumpLauncher instance to use for restart

        Returns:
            True if restart was successful, False otherwise
        """
        print("Restarting MITM proxy process...")

        # Record the restart
        self.record_restart()

        # Stop existing process
        if not self.stop_process():
            print("Failed to stop existing process")
            return False

        # Wait a moment for cleanup
        time.sleep(2)

        # Start new process
        try:
            success = launcher_instance.run(wait=False)
            if success:
                print("MITM proxy process restarted successfully")
                return True
            else:
                print("Failed to start new MITM proxy process")
                return False
        except Exception as e:
            print(f"Failed to restart MITM proxy: {e}")
            return False


# Global process manager instance
process_manager = ProcessManager()
