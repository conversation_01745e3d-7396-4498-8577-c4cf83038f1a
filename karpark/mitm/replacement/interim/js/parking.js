
document.addEventListener('DOMContentLoaded', function() {
    // 初始化加载提示 - 使用WeUI的loading modal
    var loading = weui.loading('Loading...', {
        className: 'custom-loading-modal'
    });

    function hideLoading() {
        setTimeout(function() {
            loading.hide();
        }, 1200);
    }

    // 使用原生fetch替代jQuery.ajax
    fetch('/mobile/parking/getEntry', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(function(response) {
        if (response.ok) {
            return response.json();
        }
        throw new Error('Network response was not ok');
    })
    .then(function(data) {
        console.log('GetEntry response:', data);
        hideLoading();
        location.reload(); // 响应成功立即刷新
    })
    .catch(function(error) {
        console.error('Error:', error);
        hideLoading();
        weui.alert('Data loading failed.', {
            className: 'custom-alert-modal'
        });
        return; // 避免在显示错误信息后继续刷新页面
    });
});