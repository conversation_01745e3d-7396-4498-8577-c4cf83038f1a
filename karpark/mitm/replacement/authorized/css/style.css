/* ===== BASE STYLES ===== */
html,
body {
    height: 100%;
    -webkit-tap-highlight-color: transparent;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #F8F8F8;
    overflow-y: hidden;
}

ul {
    list-style: none;
}

input,
input::-webkit-input-placeholder {
    font-size: 16px;
}

/* ===== PARKING COMPONENTS ===== */
.parking-cells {
    margin: 8px;
    padding: 7px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);
}

.parking-cell {
    height: 37px;
    padding: 5px 5px;
}

.parking-label {
    width: 110px;
    flex-shrink: 0;
    vertical-align: middle;
    color: #0583d2;
}

/* Email label container styling */
.email-row .weui-cell__hd {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

/* Magic plate label container styling */
.magic-plate-row .weui-cell__hd {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.province-btn-block {
    min-width: 60px;
    max-width: 80px;
    padding-left: 0 !important;
    flex-shrink: 0;
}

.province-btn-block {
    position: relative;
    padding-right: 12px;
}

.province-btn-block::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    height: 50%;
    width: 1px;
    background-color: #e5e5e5;
    transform: translateY(-50%);
}

.weui-half-screen-dialog__title {
    font-size: 18px;
}

.weui-half-screen-dialog__bd {
    padding-bottom: 0px;
}

.weui-half-screen-dialog__ft{
    padding: 10px 7px;
}

.parking-input {
    text-align: right;
}

.hint-text {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 100%;
    color: #0583d2;
    font-size: 16px;
    padding: 20px 0;
    margin: 8px 0;
}

/* ===== PROVINCE BUTTON CUSTOM STYLES ===== */
.province-custom-btn {
    position: relative;
    display: inline-block;
    min-width: 20px;
    max-width: 80px;
    width: fit-content;
    margin-left: 0px;
    padding: 12px;
    box-sizing: border-box;
    font-weight: 500;
    font-size: 17px;
    text-align: center;
    text-decoration: none;
    color: #4A90D2;
    line-height: 1.2;
    background-color: transparent;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    transition: all 0.2s ease;
}

.province-custom-btn:active {
    color: #015c92;
    background-color: rgba(74, 144, 210, 0.1);
}

.province-custom-btn:active::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.province-custom-btn.disabled {
    color: rgba(50, 135, 215, 0.9);
    cursor: default;
    opacity: 0.6;
}

.province-custom-btn.disabled:active {
    background-color: transparent;
}

.province-custom-btn.disabled:active::before {
    display: none;
}

/* ===== WEUI CELL STYLES ===== */
.weui-cell::before {
    content: ' ';
    position: absolute;
    left: 5px;
    right: 5px;
    top: 0;
    height: 1px;
    border-top: 1px solid #e5e5e5;
    z-index: 0;
}

.weui-cell__bd {
    flex: 1 1 55%;
    min-width: 0;
    padding-right: 0px;
}

.weui-cell__ft {
    text-align: right;
    padding-right: 0;
    display: flex;
    gap: 5px;
}

.weui-cell__btn {
    flex: 0 0 26px;
    min-width: 26px;
    max-width: 26px;
    display: flex;
    justify-content: flex-end;
    padding-right: 0;
}

/* ===== WEUI BUTTON STYLES ===== */
.weui-btn-area {
    margin: 20px 7px 20px;
}

.weui-btn {
    min-width: 100%;
    max-width: 100%;
}

.weui-btn_primary {
    background-color: #4A90D2;
    border-color: #2A538F;
}

.weui-btn_primary:not(.weui-btn_disabled):active {
    background-color: #2A538F;
    border-color: #2A538F;
}

.weui-btn_primary.weui-btn_disabled,
.weui-btn_primary.weui-btn_disabled:active {
    background-color: #9BC6EE !important;
    border-color: #9BC6EE !important;
    color: #fff !important;
    opacity: 1 !important;
    cursor: not-allowed;
}

/* ===== WEUI DIALOG STYLES ===== */
.weui-dialog__bd {
    word-wrap: break-word;
    word-break: keep-all;
    max-height: 250px;
    overflow-y: auto;
    white-space: pre-line;
    font-size: 16px;
    line-height: 1.6;
    margin-top: 0;
    padding: 30px 20px 30px;
    margin-bottom: 0;
}

.weui-dialog__btn_primary {
    color: #4A90D2;
    border-color: #4A90D2;
}

.weui-dialog__btn_primary:active {
    color: #2A538F;
    border-color: #2A538F;
}

/* .weui-dialog__btn[data-label="我确认"] {
    background-color: #4A90D2 !important;
    color: white !important;
    border-radius: 4px;
    padding: 8px 16px;
}

.weui-dialog__btn[data-label="我确认"]:active {
    background-color: #2A538F !important;
} */

/* Custom submit dialog styles - with border like half-screen dialog */
.custom-submit-dialog
.weui-dialog__hd {
    border-bottom: 1px solid #e5e5e5;
    padding: 24px 24px 20px;

}

/* ===== WEUI OTHER COMPONENTS ===== */
/* Toast */
.weui-toast {
    width: 200px;
    margin: 0 auto;
    left: 50%;
    transform: translateX(-50%);
}

/* Picker */
.weui-picker__action:last-child {
    color: #4A90D2;
}

.weui-picker .weui-half-screen-dialog__hd {
    border-bottom: 1px solid #e5e5e5;
    @media (prefers-color-scheme: dark){
        border-bottom: 1px solid #2d3748;
    }
}

.weui-picker .weui-half-screen-dialog__ft {
    border-top: 1px solid #e5e5e5;
    @media (prefers-color-scheme: dark){
        border-top: 1px solid #2d3748;
    }
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme="light"]) .weui-picker__mask,
    body:not([data-weui-theme="light"]) .weui-picker__mask {
        /* background-image: -webkit-linear-gradient(top, rgba(25, 25, 25, .95), rgba(25, 25, 25, .6)), -webkit-linear-gradient(bottom, rgba(25, 25, 25, .95), rgba(25, 25, 25, .6)); */
        background-image: none;
    }
}

/* Switch Button */
.weui-switch-btn {
    display: block;
    padding: 0 18px;
    border-radius: 12px;
    background-color: #ccc;
    color: white;
    font-size: 14px;
    line-height: 24px;
    transition: all 0.3s;
    float: right;
}

.weui-switch-btn.active {
    background-color: #4A90D2;
    color: white;
}

.weui-switch-btn.disabled {
    background-color: #e0e0e0;
    color: #fff;
    border-color: #d3d3d3;
}

.weui-switch-btn.disabled.active {
    background-color: #9BC6EE;
    opacity: 0.7;
}

/* Agreement Checkbox */
.weui-agree {
    display: flex;
    align-items: center;
    margin: 15px 0;
    padding: 0 15px;
}

.weui-agree__checkbox {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    vertical-align: text-bottom;
    position: relative;
    top: 0px;
}

.weui-agree__text {
    font-size: 14px;
    color: #666;
    line-height: 16px;
    vertical-align: baseline;
}

.weui-agree__checkbox:checked,
.weui-agree__checkbox-check[aria-checked="true"]+.weui-agree__checkbox {
    background-image: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0.5' width='24' height='24' rx='12' fill='%234A90D2' style='fill:%234A90D2;fill-opacity:1;'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.2712 16.2899L6.5 12.5187L7.44281 11.5759L10.7426 14.8757L18.2851 7.33325L19.2279 8.27606L11.214 16.2899C10.9537 16.5503 10.5316 16.5503 10.2712 16.2899Z' fill='white' style='fill:white;fill-opacity:1;'/%3E%3C/svg%3E%0A") !important;
    color: #4A90D2 !important;
}

/* Display mode styles for checkbox */
.weui-agree__checkbox-group.display-mode .weui-agree__checkbox {
    opacity: 0.6;
    cursor: not-allowed;
}

.weui-agree__checkbox-group.display-mode .weui-agree__checkbox:disabled {
    opacity: 0.6;
}

.weui-agree__checkbox-group.display-mode .weui-agree__text {
    opacity: 0.8;
    color: #999;
}

/* ===== CUSTOM KEYBOARD ===== */
#keyboardBox {
    display: none;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 999;
}

#keyboardBox .Box,
#keyboardBox .textBox {
    width: 100%;
    background-color: #D0D5D9;
    padding-top: 10px;
    padding-bottom: 4px;
}

#keyboardBox .Box ul,
#keyboardBox .textBox ul {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    margin-top: 10px;
}

#keyboardBox .Box ul:first-of-type,
#keyboardBox .textBox ul:first-of-type {
    margin-top: 0;
}

#keyboardBox .Box ul li,
#keyboardBox .textBox ul li {
    width: 30px;
    height: 40px;
    border-radius: 6px;
    text-align: center;
    line-height: 40px;
    background-color: #fff;
}

#keyboardBox .textBox {
    display: none;
}

#keyboardBox .Box ul .changeContentBtn,
#keyboardBox .Box ul .deleteBtn,
#keyboardBox .textBox ul .changeContentBtn,
#keyboardBox .textBox ul .deleteBtn {
    width: 40px;
    height: 40px;
    background-color: #d1d5d9;
    text-align: center;
    line-height: 40px;
}

#keyboardBox .Box ul .deleteBtn img,
#keyboardBox .textBox ul .deleteBtn img {
    width: 23px;
    height: 16px;
    margin: 0;
    margin-top: 12px;
}

/* ===== DIALOG CONTENT STYLES ===== */
.dialog-content-center {
    text-align: center;
    padding: 15px 0;
}

.content-block {
    text-align: left;
    white-space: pre-line;
    font-weight: bold;
}

.content-block ul {
    padding: 3px 0 3px 25px !important;
    margin: 0 !important;
    font-weight: normal;
}

.content-block li {
    list-style-type: disc !important;
    margin: 10px 0 10px 20px !important;
    line-height: 1.8;
    padding-left: 0;
}

.custom-dialog .weui-dialog__bd {
    padding-top: 0 !important;
    margin-top: -35px !important;
    margin-bottom: -10px !important;
}

.custom-dialog .content-block {
    line-height: normal !important;
}

.custom-dialog .content-block ul {
    padding: 0 0 0 15px !important;
    margin: 0 0 10px 0 !important;
}

.custom-dialog .content-block>ul>li {
    margin: 0 0 0 10px !important;
}

/* ===== HALF SCREEN DIALOG CONTENT ===== */
.half-screen-content {
    text-align: left;
    padding: 10px 20px;
    line-height: 1.8;
    font-size: 16px;
    /* 确保内容区域可以正常触摸滚动 */
    touch-action: pan-y;
    user-select: text;
    /* 允许正常的文本换行 */
    word-break: normal;
    overflow-wrap: break-word;
    white-space: normal;
}

.half-screen-content strong {
    display: block;
    /* margin: 15px 0 10px 0; */
    font-weight: 600;
    color: #333;
}

/* 使用语义化的列表样式 */
.half-screen-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.half-screen-list li {
    display: flex;
    align-items: flex-start;
    margin: 8px 0;
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
    /* 确保长文本可以换行 */
    width: 100%;
    /* 设置最小宽度为0，确保flex container可以收缩 */
    min-width: 0;
}

.half-screen-list li::before {
    content: "•";
    flex-shrink: 0;
    margin-right: 8px;
    line-height: inherit;
    /* 确保bullet point不会换行 */
}

.half-screen-list li > span {
    flex: 1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    /* 确保文本内容可以换行 */
    white-space: normal;
    /* 设置最小宽度为0，确保flex item可以收缩 */
    min-width: 0;
}

/* Custom half-screen dialog styles */
.custom-half-screen-dialog .weui-dialog {
    position: fixed;
    bottom: 0;
    top: auto;
    left: 0;
    right: 0;
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 16px 16px 0 0;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 70vh;
    overflow: hidden;
    /* 确保触摸事件正常工作 */
    touch-action: manipulation;
}

.custom-half-screen-dialog .weui-dialog.weui-animate-fade-in {
    transform: translateY(0);
}

.custom-half-screen-dialog .weui-dialog.weui-animate-fade-out {
    transform: translateY(100%);
}

.custom-half-screen-dialog .weui-dialog__bd {
    /* max-height: 50vh; */
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 10px 30px;
    -webkit-overflow-scrolling: touch;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 WebKit 浏览器的滚动条 */
.custom-half-screen-dialog .weui-dialog__bd::-webkit-scrollbar {
    display: none;
}

.custom-half-screen-dialog .weui-dialog__hd {
    padding: 25px 24px 25px;
    border-bottom: 1px solid #e5e5e5;
}

.custom-half-screen-dialog .weui-dialog__ft {
    /* padding: 20px 24px; */
    border-top: 1px solid #e5e5e5;
}

/* ===== UTILITY COMPONENTS ===== */
.question-icon {
    /* cursor: pointer; */
    margin-left: 12px;
    font-size: 13px;
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 19px; /* Mathematically 18px, adjusted to 19px for better visual appearance */
    border-radius: 50%;
    text-align: center;
    vertical-align: middle;
    text-decoration: none;
    background-color: #e8f4fd;
    color: #4A90D2;
    border: 1px solid #b3d9f2;
    transition: all 0.2s ease;
}

/* .question-icon:hover {
    background-color: #d1e9f8;
    border-color: #4A90D2;
}

.question-icon:active {
    background-color: #b3d9f2;
    color: #015c92;
} */

/* Magic Plate Group - initially hidden */
#magicPlateGroup {
    display: flex;
    align-items: center;
}

/* Button containers - initially hidden with margin */
#withdrawBtn,
#backBtn {
    display: none;
    margin-top: 8px;
}

.text-link {
    display: block;
    text-align: center;
    font-size: 14px;
    color: #666;
    text-decoration: none;
    padding: 8px 0;
    background: none;
    border: none;
    cursor: pointer;
}

.text-link:active {
    color: #4A90D2;
    background-color: rgba(74, 144, 210, 0.1);
}

/* Magic plate row styling */
.magic-plate-row::before {
    content: ' ';
    position: absolute;
    left: 5px;
    right: 5px;
    top: 0;
    height: 1px;
    border-top: 1px solid #e5e5e5;
    z-index: 0;
}

.magic-plate-row.weui-cell::before {
    display: block !important;
}

.magic-plate-row {
    flex-grow: 1;
}

/* ===== EMAIL COMPONENTS ===== */
.email-row::before {
    content: ' ';
    position: absolute;
    left: 5px;
    right: 5px;
    top: 0;
    height: 1px;
    border-top: 1px solid #e5e5e5;
    z-index: 0;
}

.email-row.weui-cell::before {
    display: block !important;
}

.email-row:not(:first-child) .parking-label {
    color: #a0c4e0;
}

.email-row {
    flex-grow: 1;
}

#email-container {
    overflow: visible !important;
    border: none !important;
}

.add-email-btn,
.remove-email-btn {
    font-size: 13px;
    margin-left: 12px;
    text-decoration: none;
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    vertical-align: middle;
    border-radius: 50%;
    border: 1px solid;
    transition: all 0.2s ease;
}

.add-email-btn {
    color: #4A90D2;
    background-color: #e8f4fd;
    border-color: #b3d9f2;
}

/* .add-email-btn:hover {
    background-color: #d1e9f8;
    border-color: #4A90D2;
}

.add-email-btn:active {
    background-color: #b3d9f2;
    color: #015c92;
} */

.remove-email-btn {
    color: #e74c3c;
    background-color: #fdf2f2;
    border-color: #f5b7b1;
}

.remove-email-btn:hover {
    background-color: #fadbd8;
    border-color: #e74c3c;
}

.remove-email-btn:active {
    background-color: #f5b7b1;
    color: #c0392b;
}

.email-label {
    vertical-align: middle;
    line-height: 32px;
    display: inline-block;
    margin-right: 4px;
}

.email-input {
    vertical-align: middle;
    height: 32px;
    margin-left: 0;
    padding: 6px 0;
}

/* ===== ADMIN AND DEBUG COMPONENTS ===== */
#admin-buttons {
    position: fixed;
    bottom: 10px;
    right: 10px;
    z-index: 9999;
    display: flex;
    gap: 10px;
}

#debug-toggle,
#admin-toggle {
    background: rgba(0,0,0,0.5);
    color: white;
    padding: 5px;
    border-radius: 5px;
    cursor: pointer;
}

#debug-toggle {
    display: none; /* Default hidden, will be shown via JavaScript when DEBUG_MODE = 1 */
}

#debug-panel {
    position: fixed;
    bottom: 60px;
    right: 10px;
    width: 300px;
    max-height: 200px;
    overflow-y: auto;
    background: rgba(0,0,0,0.7);
    color: white;
    font-size: 12px;
    padding: 10px;
    border-radius: 5px;
    z-index: 9999;
    display: none;
}

.debug-panel-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

#debug-close {
    cursor: pointer;
}

.debug-log-message {
    border-bottom: 1px solid #444;
    padding: 3px 0;
}

.debug-log-data {
    color: #aaa;
    padding-left: 10px;
    word-break: break-all;
}

.debug-log-data-simple {
    color: #aaa;
    padding-left: 10px;
}

/* ===== DARK MODE STYLES ===== */
@media (prefers-color-scheme: dark) {
    /* Base styles */
    html,
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    /* Parking components */
    .parking-cells {
        background: #2d2d2d;
        box-shadow: 0 4px 4px rgba(255, 255, 255, 0.1);
    }

    .parking-cells .inner-box {
        background: rgba(255, 255, 255, 0.05);
        transition: background 0.3s ease;
    }

    .parking-cell {
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .parking-label {
        color: #90cdf4;
    }

    .hint-text {
        color: #90cdf4;
    }

    .parking-input,
    .weui-input {
        background-color: #2d3748;
        color: #ffffff;
        border-color: #4a5568;
    }

    .weui-input::-webkit-input-placeholder,
    .weui-input::-moz-placeholder,
    .weui-input::placeholder {
        color: #a0aec0;
    }

    /* Province button custom styles */
    .province-custom-btn {
        color: #90cdf4;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .province-custom-btn:active {
        color: #63b3ed;
        background-color: #1a202c;
    }

    .province-custom-btn:active::before {
        background-color: rgba(144, 205, 244, 0.1);
    }

    .province-custom-btn.disabled {
        color: #9bacc9;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    /* Legacy province button */
    .province-btn {
        color: #90cdf4;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .province-btn:active {
        color: #63b3ed;
        background-color: #1a202c;
    }

    .province-btn.disabled {
        color: #e5e5e5;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    /* WeUI Cell components */
    .weui-cell {
        background-color: #2d3748;
        color: #ffffff;
    }

    .weui-cell::before {
        border-top-color: #4a5568;
    }

    /* Province button separator line */
    .province-btn-block::after {
        background-color: #4a5568;
    }

    /* WeUI Button components */
    .weui-btn_primary {
        background-color: #3182ce;
        border-color: #2c5282;
        color: #ffffff;
    }

    .weui-btn_primary:not(.weui-btn_disabled):active {
        background-color: #2c5282;
        border-color: #2a4365;
    }

    .weui-btn_primary.weui-btn_disabled,
    .weui-btn_primary.weui-btn_disabled:active {
        background-color: #4a5568 !important;
        border-color: #4a5568 !important;
        color: #718096 !important;
        opacity: 0.6 !important;
        cursor: not-allowed;
    }

    .weui-btn_disabled {
        background-color: #3c4555 !important;
        border-color: #4a5568 !important;
        color: #a0aec0 !important;
    }

    .weui-btn-area {
        background-color: #1a1a1a;
        /* margin: 20px 7px 20px; */
    }

    /* WeUI Dialog components */
    .weui-dialog {
        background-color: #2d3748;
        color: #ffffff;
    }

    .weui-dialog__hd {
        background-color: #2d3748;
        color: #ffffff;
        border-bottom-color: #4a5568;
        padding: 24px 24px 20px;
    }

    .weui-dialog__bd {
        background-color: #2d3748;
        color: #ffffff;
    }

    .weui-dialog__ft {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }

    .weui-dialog__btn {
        color: #90cdf4;
        border-color: #4a5568;
    }

    .weui-dialog__btn:active {
        background-color: #1a202c;
    }

    .weui-dialog__btn_primary {
        color: #90cdf4;
        border-color: #4a5568;
    }

    .weui-dialog__btn_primary:active {
        color: #63b3ed;
        border-color: #2c5282;
    }

    /* Custom submit dialog dark mode */
    .custom-submit-dialog .weui-dialog__hd {
        border-bottom-color: #4a5568;
    }

    /* Half screen dialog components */
    .custom-half-screen-dialog .weui-dialog {
        background-color: #2d3748;
        color: #ffffff;
    }

    .custom-half-screen-dialog .weui-dialog__hd {
        background-color: #2d3748;
        color: #ffffff;
        border-bottom-color: #4a5568;
    }

    .custom-half-screen-dialog .weui-dialog__bd {
        background-color: #2d3748;
        color: #ffffff;
    }

    .custom-half-screen-dialog .weui-dialog__ft {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }

    .half-screen-content strong {
        color: #90cdf4;
    }

    .half-screen-list li::before {
        color: #90cdf4;
    }

    /* WeUI Other components */
    .weui-toast {
        background-color: #576e96;
    }

    .weui-picker {
        background-color: #2d3748;
    }

    .weui-picker__action {
        color: #90cdf4;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .weui-picker__action:active {
        background-color: #1a202c;
    }

    .weui-picker__action:last-child {
        color: #90cdf4;
    }

    .weui-switch-btn {
        background-color: #4a5568;
        color: #ffffff;
    }

    .weui-switch-btn.active {
        background-color: #3182ce;
        color: #ffffff;
    }

    .weui-switch-btn.disabled {
        background-color: #4a5568;
        color: #718096;
        border-color: #4a5568;
    }

    .weui-switch-btn.disabled.active {
        background-color: #4a5568;
        opacity: 0.7;
    }

    /* WeUI Form components */
    .weui-cells {
        background-color: #1a1a1a;
    }

    .weui-cells::before,
    .weui-cells::after {
        border-top: none !important;
        border-bottom: none !important;
    }

    .weui-cells_form {
        background-color: #2d3748;
    }

    .weui-agree__checkbox-group {
        background-color: #1a1a1a;
    }

    .weui-agree__text {
        color: #a0aec0;
    }

    /* Display mode styles for checkbox in dark mode */
    .weui-agree__checkbox-group.display-mode .weui-agree__checkbox {
        opacity: 0.5;
    }

    .weui-agree__checkbox-group.display-mode .weui-agree__text {
        opacity: 0.7;
        color: #666;
    }

    /* Custom keyboard components */
    #keyboardBox .Box,
    #keyboardBox .textBox {
        background-color: #1a202c;
    }

    #keyboardBox .Box ul li,
    #keyboardBox .textBox ul li {
        background-color: #2d3748;
        color: #ffffff;
        border: 1px solid #4a5568;
    }

    #keyboardBox .Box ul .changeContentBtn,
    #keyboardBox .Box ul .deleteBtn,
    #keyboardBox .textBox ul .changeContentBtn,
    #keyboardBox .textBox ul .deleteBtn {
        background-color: #1a202c !important;
        color: #ffffff !important;
        border-color: #1a202c !important;
        margin: 0 2px;
    }

    /* Utility components */
    .question-icon {
        background-color: #2d3748;
        color: #90cdf4;
        border-color: #4a5568;
    }

    .question-icon:hover {
        background-color: #4a5568;
        border-color: #90cdf4;
    }

    .question-icon:active {
        background-color: #1a202c;
        color: #63b3ed;
    }

    .text-link {
        color: #a0aec0;
    }

    .text-link:active {
        color: #90cdf4;
        background-color: rgba(144, 205, 244, 0.1);
    }

    /* Email and Magic plate components */
    #email-container {
        background-color: #1a1a1a;
    }

    .email-row::before,
    .magic-plate-row::before {
        border-top-color: #4a5568;
    }

    .email-row:not(:first-child) .parking-label {
        color: #4a5568;
    }

    .email-label {
        color: #e2e8f0;
    }

    .email-input {
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .add-email-btn {
        color: #90cdf4;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .add-email-btn:hover {
        background-color: #4a5568;
        border-color: #90cdf4;
    }

    .add-email-btn:active {
        background-color: #1a202c;
        color: #63b3ed;
    }

    .remove-email-btn {
        color: #fc8181;
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .remove-email-btn:hover {
        background-color: #4a5568;
        border-color: #fc8181;
    }

    .remove-email-btn:active {
        background-color: #1a202c;
        color: #e53e3e;
    }

    /* Admin and Debug components dark mode */
    #debug-toggle,
    #admin-toggle {
        background: rgba(255,255,255,0.1);
        color: #ffffff;
    }

    #debug-toggle:hover,
    #admin-toggle:hover {
        background: rgba(255,255,255,0.2);
    }

    #debug-panel {
        background: rgba(45, 55, 72, 0.95);
        color: #ffffff;
        border: 1px solid #4a5568;
    }

    .debug-log-message {
        border-bottom-color: #4a5568;
    }

    .debug-log-data,
    .debug-log-data-simple {
        color: #a0aec0;
    }
}
