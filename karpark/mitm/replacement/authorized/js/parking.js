$(document).ready(function() {
    // UI元素定义 - 缓存jQuery选择器以提高性能
    const $carNumber = $("#car-number");
    const $parkingArea = $("#parking-area");
    const $openKeyboard = $('#openKeyboard');
    const $toggleMagicPlate = $('#toggleMagicPlate');
    const $submitBtn = $('#submitBtn');
    const $withdrawBtn = $('#withdrawBtn');
    const $backBtn = $('#backBtn');
    
    // 调试模式开关：1=开启，0=关闭
    const DEBUG_MODE = 0; // 发布时设为0，调试时设为1

    // 用户权限标识
    let isAdmin = false;

    // 初始化变量和UI元素
    var postId;
    // 只创建loading，不要立刻hide
    var loading = weui.loading('Loading...', { className: 'custom-classname' });
    hideLoading();
    
    // 邮箱相关变量
    const MAX_EMAILS = 3; // 最多允许3个邮箱地址
    let emailCount = 1; // 当前邮箱输入框数量

    // 页面加载完成后立即初始化输入框
    setTimeout(function() {
        initCarNumberInput();
        initEmailInputs();
    }, 100);
    
    // 添加管理员和调试按钮容器
    function addAdminButtons() {
        // 只有管理员才能看到调试和管理按钮
        if (!isAdmin) return;

        // 添加按钮容器
        $('body').append(`
            <div id="admin-buttons">
                <div id="debug-toggle">调试</div>
                <div id="admin-toggle">管理</div>
            </div>
        `);

        // 根据调试模式显示/隐藏调试按钮
        if (DEBUG_MODE === 1) {
            $('#debug-toggle').show();
        }

        // 添加调试面板（仅在调试模式下）
        if (DEBUG_MODE === 1) {
            $('body').append(`
                <div id="debug-panel">
                    <div class="debug-panel-header">
                        <span>调试信息</span>
                        <span id="debug-close">X</span>
                    </div>
                    <div id="debug-content"></div>
                </div>
            `);

            // 调试窗口控制
            $('#debug-toggle').click(function() {
                $('#debug-panel').show();
            });

            $('#debug-close').click(function() {
                $('#debug-panel').hide();
            });
        }

        // 管理按钮点击事件
        $('#admin-toggle').click(function() {
            // 直接在新窗口打开管理页面
            window.open('/mobile/parking/adminPage', '_blank');
        });
    }
    
    // 日志记录函数 - 将调试信息添加到调试面板，支持对象和文本格式
    function logDebug(message, data) {
        if (DEBUG_MODE !== 1) return; // 非调试模式下不记录日志

        const time = new Date().toLocaleTimeString();
        let logMsg = `<div class="debug-log-message">[${time}] ${message}</div>`;

        if (data) {
            try {
                if (typeof data === 'object') {
                    logMsg += `<div class="debug-log-data">${JSON.stringify(data)}</div>`;
                } else {
                    logMsg += `<div class="debug-log-data">${data}</div>`;
                }
            } catch (e) {
                logMsg += `<div class="debug-log-data-simple">[无法显示数据]</div>`;
            }
        }

        $('#debug-content').prepend(logMsg);

        // 限制日志数量，防止内存占用过大
        if ($('#debug-content > div').length > 50) {
            $('#debug-content > div:last-child').remove();
        }
    }

    // 表单字段启用/禁用切换函数 - 用于控制表单的编辑状态
    function toggleInputFields(disable) {
        $carNumber.add($parkingArea).prop('disabled', disable);
        $openKeyboard.toggleClass('disabled', disable);
        $toggleMagicPlate.toggleClass('disabled', disable).prop('disabled', disable);

        // 邮箱输入框控制
        $('.email-input').prop('disabled', disable);
        $('.add-email-btn, .remove-email-btn').toggle(!disable);

        // 隐私声明复选框控制 - 始终显示，但在显示模式下禁用
        $('#agreeInput').prop('disabled', disable);
        if (disable) {
            $('#weuiCheckGroup').addClass('display-mode');
        } else {
            $('#weuiCheckGroup').removeClass('display-mode');
        }

        // 根据表单状态更新提示文本
        if (disable) {
            $('.hint-text').text('Auto-registration reserved👇');
        } else {
            $('.hint-text').text('Fill out the from, and just wait👇');
        }
    }

    // 表单验证函数 - 检查所有必填字段是否有效
    // 在表单验证函数中添加隐私声明检查
    function checkInputFields(obj) {
        if (!obj.eeId || obj.eeId.trim() === '') return 'Employee ID不能为空';
        if (!obj.eeName || obj.eeName.trim() === '') return 'Full Name不能为空';
        if (!obj.registrationLocation || obj.registrationLocation.trim() === '') return '请选择停车区域';
        if (!obj.registrationNumber || obj.registrationNumber.length < 7 || obj.registrationNumber.length > 8) return '车牌号应为7-8位字符';
        
        // 验证至少有一个有效的邮箱
        if (!obj.emails || obj.emails.length === 0) {
            return '请至少输入一个有效的邮箱地址';
        }
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        for (let i = 0; i < obj.emails.length; i++) {
            if (obj.emails[i] && !emailRegex.test(obj.emails[i])) {
                return `邮箱格式不正确: ${obj.emails[i]}`;
            }
        }
        
        // 添加撤销操作不检查隐私声明的条件
        if(!obj.isWithdrawn && !$('#agreeInput')[0].checked) {
            return '十秒速读并同意Terms of Use and Privacy Stuff👀';
        }
        return true;
    }

    function hideLoading() {
        setTimeout(function() {
            loading.hide();
        }, 1200);
    }

    // 初始化数据加载 - 从服务器获取用户预注册信息
    $.get("/mobile/parking/getEntry")
        .done(function(res) {
            // 延迟 500ms 后再隐藏 loading
            hideLoading();
            logDebug('获取数据成功', res);
            
            if (!res) {
                weui.alert('获取数据失败');
                return;
            }

            // 填充基本信息 - 员工ID和姓名
            $("#parking-ee-id").val(res.eeId);
            $("#parking-ee-name").val(res.eeName);

            // 处理错误信息 - 显示服务器返回的错误
            if (res.error && res.error.errorCode) {
                weui.topTips(res.error.errorMessage, 100000);
                return;
            }

            // 处理预填充数据 - 如果用户已有注册信息则自动填充
            if (res.registrationNumber && res.registrationLocation) {
                // 填充表单
                $parkingArea.val(res.registrationLocation).trigger('input');

                if (res.registrationNumber.length > 0) {
                    $openKeyboard.text(res.registrationNumber.charAt(0));
                    $carNumber.val(res.registrationNumber.substring(1));
                }
                
                // 填充邮箱信息
                if (res.emails && res.emails.length > 0) {
                    // 清空现有邮箱输入框
                    $('#email-container').empty();
                    
                    // 添加邮箱输入框
                    res.emails.forEach((email, index) => {
                        const emailRow = $(`
                            <div class="weui-cell parking-cell email-row">
                                <div class="weui-cell__hd">
                                    <label class="weui-label parking-label">Email</label>
                                </div>
                                <div class="weui-cell__bd">
                                    <input class="weui-input parking-input email-input" type="email" value="${email}" disabled>
                                </div>
                            </div>
                        `);
                        $('#email-container').append(emailRow);
                    });
                    emailCount = res.emails.length;
                }

                // 设置UI状态 - 禁用输入字段，显示更改和撤销按钮
                // 如果用户已有注册信息，说明之前已经同意过隐私声明
                $('#agreeInput').prop('checked', true);
                toggleInputFields(true);
                $submitBtn.removeClass('weui-btn_disabled').text('更改自动注册信息');
                $withdrawBtn.show();
            } else {
                // 无预填数据时的UI状态 - 启用输入字段，隐藏撤销按钮
                toggleInputFields(false);
                $withdrawBtn.hide();
                $submitBtn.text('请填写完整信息后提交');
            }

            // 高级功能显示控制 - 根据用户权限显示多车注册选项
            $('#magicPlateGroup').toggle(res.advancedFeatureOn === true);
            // 多车注册状态同步
            if (typeof res.useMagicPlate !== 'undefined') {
                $toggleMagicPlate.toggleClass('active', res.useMagicPlate).text(res.useMagicPlate ? 'YES' : 'NO');
            }

            // 检查管理员权限并添加管理按钮
            if (res.isAdmin === true) {
                isAdmin = true;
                addAdminButtons();
            }
        })
        .fail(function() {
            hideLoading();
            logDebug('获取数据失败');
            weui.alert('获取数据失败');
        })
        .always(function() {
            hideLoading();
        });

    // 省份选择按钮点击事件 - 显示省份选择键盘
    $openKeyboard.unbind().click(function(event) {
        if (!$(this).hasClass('disabled')) {
            $("#keyboardBox").show();
        }
    });

    // 停车区域选择事件 - 显示区域选择器
    $parkingArea.unbind().click(function() {
        weui.picker([{ label: 'PVG 06', value: 'PVG06' }], {
            className: 'compact-picker',
            title: '选择停车区域',
            confirmText: '确定',
            closeText: '关闭',
            showClose: true,
            onConfirm: function() {
                $parkingArea.val('PVG06').trigger('input');
            }
        });
    });

    // Function to handle the final steps after AJAX call (show toast and reload)
    function handlePostSubmit(message, isSuccess) {
        hideLoading(); // Hide loading indicator first
        if (isSuccess) {
            weui.toast(message, 3000);
            logDebug('操作成功，准备刷新页面, 2.8秒后执行');
            setTimeout(function() {
                logDebug('执行页面刷新');
                location.reload();
            }, 2800);
        } else {
            logDebug('操作失败或服务器返回错误', message);
            weui.topTips(message, 5000); // Show error message as top tip
        }
    }

    // 表单提交处理函数 - 处理注册和撤销操作
    function handleSubmit(isWithdrawn, confirmMsg, successToastMsg) {
        // 收集所有邮箱地址
        const emails = [];
        $('.email-input').each(function() {
            const email = $(this).val().trim();
            if (email) {
                emails.push(email);
            }
        });
        
        var registObj = {
            "eeId": $("#parking-ee-id").val().trim(),
            "eeName": $("#parking-ee-name").val().trim(),
            "registrationLocation": $parkingArea.val(),
            "registrationNumber": $openKeyboard.text() + $carNumber.val().trim().toUpperCase(),
            "useMagicPlate": $toggleMagicPlate.hasClass('active'),
            "isWithdrawn": isWithdrawn,
            "emails": emails
        };

        logDebug('提交数据', registObj);

        var alertMsg = checkInputFields(registObj);
        if (alertMsg === true) {
            weui.confirm(confirmMsg, {
                title: '确认提交',
                className: 'custom-submit-dialog',
                buttons: [{
                    label: 'NO',
                    type: 'default',
                    onClick: function() {
                        logDebug('用户取消提交');
                    }
                }, {
                    label: 'YES',
                    type: 'primary',
                    onClick: function() {
                        $.ajax({
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            },
                            url: "/mobile/parking/saveEnrollment",
                            type: "POST",
                            contentType: "application/json; charset=utf-8",
                            cache: false,
                            dataType: 'json',
                            data: JSON.stringify(registObj),
                            beforeSend: function() {
                                loading = weui.loading('Loading...', { 
                                    className: 'custom-classname'
                                });
                            },
                            complete: function() {
                                loading.hide(); // 在请求完成后强制关闭loading
                            },
                            success: function(res) {
                                logDebug('服务器响应', res);
                                if (res.error && res.error.errorCode !== null) {
                                    handlePostSubmit(res.error.errorMessage, false);
                                } else {
                                    handlePostSubmit(successToastMsg, true);
                                }
                            },
                            error: function(xhr, status, error) {
                                logDebug('请求失败', {status: status, error: error, response: xhr.responseText});
                                handlePostSubmit('提交失败，请检查网络或稍后重试', false);
                            },
                        });
                    }
                }]
            });
        } else {
            logDebug('表单验证失败', alertMsg);
            weui.topTips(alertMsg, 3000);
        }
    }

    // 提交按钮点击事件 - 处理表单提交和编辑模式切换
    // 在全局变量区域添加原始数据存储
    let originalProvince = '';
    let originalCarNumber = '';
    let originalPrivacyChecked = false;
    
    // 邮箱添加/删除功能
    $(document).on('click', '.add-email-btn', function() {
        if (emailCount < MAX_EMAILS) {
            emailCount++;
            const newRow = $(`
                <div class="weui-cell parking-cell email-row">
                    <div class="weui-cell__hd">
                        <label class="weui-label parking-label">Email</label>
                        <a href="javascript:;" class="remove-email-btn">-</a>
                    </div>
                    <div class="weui-cell__bd">
                        <input class="weui-input parking-input email-input" type="email" placeholder="Input your email">
                    </div>
                </div>
            `);
            $('#email-container').append(newRow);

            // 如果达到最大数量，隐藏所有添加按钮
            if (emailCount >= MAX_EMAILS) {
                $('.add-email-btn').hide();
            }

            // 触发验证
            triggerFormValidation();
        }
    });
    
    $(document).on('click', '.remove-email-btn', function() {
        $(this).closest('.email-row').remove();
        emailCount--;
        
        // 如果删除后数量小于最大值，显示添加按钮
        if (emailCount < MAX_EMAILS) {
            $('.add-email-btn').show();
        }
        
        // 触发验证
        triggerFormValidation();
    });

    // 车牌号输入框事件监听 - 处理重复输入和空白字符
    $carNumber.on('compositionstart', function() {
        $(this).data('isComposing', true);
        logDebug('车牌号开始中文输入');
    });

    $carNumber.on('compositionend', function() {
        $(this).data('isComposing', false);
        logDebug('车牌号结束中文输入');
        // 处理最终输入值
        processCarNumberInput();
    });

    $carNumber.on('input', function() {
        if (!$(this).data('isComposing')) {
            processCarNumberInput();
        }
        triggerFormValidation();
    });

    // 邮箱输入框事件监听 - 处理重复输入和空白字符
    $(document).on('compositionstart', '.email-input', function() {
        $(this).data('isComposing', true);
        logDebug('邮箱开始中文输入');
    });

    $(document).on('compositionend', '.email-input', function() {
        $(this).data('isComposing', false);
        logDebug('邮箱结束中文输入');
        // 处理最终输入值
        processEmailInput(this);
    });

    $(document).on('input', '.email-input', function() {
        if (!$(this).data('isComposing')) {
            processEmailInput(this);
        }
        triggerFormValidation();
    });

    // 添加隐私声明复选框的变化事件监听
    $(document).on('change', '#agreeInput', function() {
        // 触发验证
        triggerFormValidation();
    });

    // 车牌号输入处理函数 - 处理重复输入和空白字符
    function processCarNumberInput() {
        if (!$carNumber || !$carNumber.length) {
            return;
        }

        const original = $carNumber.val();

        // 移除所有空白字符（包括中英文空格、制表符等）
        let processed = original.replace(/\s/g, '');

        // 只保留字母和数字，并转换为大写
        processed = processed.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();

        if (processed !== original) {
            logDebug('车牌号输入处理', {
                original: original,
                processed: processed
            });

            // 保存光标位置
            let cursorPos = $carNumber[0].selectionStart || 0;

            // 调整光标位置
            if (processed.length < original.length) {
                cursorPos = Math.min(cursorPos, processed.length);
            }

            // 设置新值
            $carNumber.val(processed);

            // 恢复光标位置
            try {
                $carNumber[0].setSelectionRange(cursorPos, cursorPos);
            } catch (e) {
                logDebug('设置光标位置失败', e);
            }
        }
    }

    // 邮箱输入处理函数 - 处理重复输入和空白字符
    function processEmailInput(input) {
        const $input = $(input);
        const original = $input.val();

        // 移除所有空白字符（包括中英文空格、制表符等）
        const processed = original.replace(/\s/g, '');

        if (processed !== original) {
            logDebug('邮箱输入处理', {
                original: original,
                processed: processed
            });

            // 保存光标位置
            let cursorPos = input.selectionStart || 0;

            // 调整光标位置
            if (processed.length < original.length) {
                cursorPos = Math.min(cursorPos, processed.length);
            }

            // 设置新值
            $input.val(processed);

            // 恢复光标位置
            try {
                input.setSelectionRange(cursorPos, cursorPos);
            } catch (e) {
                logDebug('设置光标位置失败', e);
            }
        }
    }

    // 初始化车牌号输入框
    function initCarNumberInput() {
        logDebug('车牌号输入框初始化完成');
    }

    // 初始化邮箱输入框
    function initEmailInputs() {
        logDebug('邮箱输入框初始化完成');
    }

    // 表单验证触发函数
    function triggerFormValidation() {
        // 验证所有字段是否有效
        const provinceSelected = $openKeyboard.text().trim() !== '省';
        const carNoValid = $carNumber.val().length >= 6;
        const areaValid = $parkingArea.val().trim() !== '';

        // 验证至少有一个有效的邮箱
        let hasValidEmail = false;
        $('.email-input').each(function() {
            const email = $(this).val().trim();
            if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                hasValidEmail = true;
                return false; // break the loop
            }
        });

        // 验证隐私声明复选框是否已勾选（仅在编辑模式下检查）
        const isDisplayMode = $('#agreeInput').prop('disabled');
        const privacyAgreed = isDisplayMode || $('#agreeInput').prop('checked');

        const allFieldsValid = provinceSelected && carNoValid && areaValid && hasValidEmail && privacyAgreed;

        // 更新按钮状态和文本
        $submitBtn.toggleClass('weui-btn_disabled', !allFieldsValid);

        // 根据表单状态更新按钮文本
        if (allFieldsValid &&
            $submitBtn.text() !== '更改自动注册信息' &&
            $submitBtn.text() !== '提交修改') {
            $submitBtn.text('提交自动注册信息');
            logDebug('表单验证通过，更新按钮文本为：提交自动注册信息');
        } else if (!allFieldsValid &&
                   $submitBtn.text() !== '更改自动注册信息' &&
                   $submitBtn.text() !== '提交修改') {
            $submitBtn.text('请填写完整信息后提交');
            logDebug('表单验证未通过，更新按钮文本为：请填写完整信息后提交');
        }

        // 添加调试日志
        logDebug('表单验证状态', {
            provinceSelected,
            carNoValid,
            areaValid,
            hasValidEmail,
            privacyAgreed,
            allFieldsValid
        });
    }



    // 简化的输入事件处理函数 - 移除中文输入法复杂逻辑
    $parkingArea.add($openKeyboard)
        .on('input', function() {
            // 车牌号输入处理已经在专门的事件监听器中处理，这里不再重复处理
            // 直接触发表单验证
            triggerFormValidation();
        });

    // 修改提交按钮点击事件
    $submitBtn.unbind().click(function(event) {
        event.preventDefault();
    
        if ($(this).text() === '更改自动注册信息') {
            // 保存原始数据
            originalProvince = $openKeyboard.text();
            originalCarNumber = $carNumber.val();
            originalPrivacyChecked = $('#agreeInput').prop('checked');

            // 保存原始邮箱数据
            const originalEmails = [];
            $('.email-input').each(function() {
                originalEmails.push($(this).val());
            });
            window.originalEmails = originalEmails;

            // 重置邮箱输入区域
            $('#email-container').empty();
            emailCount = 0;

            // 添加邮箱输入框
            originalEmails.forEach((email, index) => {
                emailCount++;
                const emailRow = $(`
                    <div class="weui-cell parking-cell email-row">
                        <div class="weui-cell__hd">
                            <label class="weui-label parking-label">Email</label>
                            ${index === 0 ? '<a href="javascript:;" class="add-email-btn">+</a>' : '<a href="javascript:;" class="remove-email-btn">-</a>'}
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input parking-input email-input" type="email" value="${email}">
                        </div>
                    </div>
                `);
                $('#email-container').append(emailRow);
            });

            // 如果没有邮箱，添加一个空的输入框
            if (emailCount === 0) {
                emailCount = 1;
                const emailRow = $(`
                    <div class="weui-cell parking-cell email-row">
                        <div class="weui-cell__hd">
                            <label class="weui-label parking-label">Email</label>
                            <a href="javascript:;" class="add-email-btn">+</a>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input parking-input email-input" type="email" placeholder="Input your email">
                        </div>
                    </div>
                `);
                $('#email-container').append(emailRow);
            }

            // 进入编辑模式时自动勾选隐私复选框（因为用户之前已经同意过）
            $('#agreeInput').prop('checked', true);

            toggleInputFields(false);
            $(this).text('提交修改');
            $withdrawBtn.hide();
            $backBtn.show();
            return;
        }

        if (!$(this).hasClass('weui-btn_disabled')) {
            // 确保按钮文本与预期一致
            if ($(this).text() === '请填写完整信息后提交') {
                $(this).text('提交自动注册信息');
            }
            
            handleSubmit(
                false,
                '将按提交信息自动执行本轮车位注册<br>(提交后仍可按需要更改或撤销登记)',
                '登记成功<br>会努力为你搞定注册！'
            );
        }
    });

    // 返回按钮点击事件 - 取消编辑模式，恢复原始状态
    // 修改返回按钮点击事件
    $backBtn.click(function() {
        toggleInputFields(true);
        $submitBtn.text('更改自动注册信息');
        // 确保按钮样式正确（移除disabled状态）
        $submitBtn.removeClass('weui-btn_disabled');

        // 恢复原始数据
        $openKeyboard.text(originalProvince);
        $carNumber.val(originalCarNumber);
        // 恢复隐私复选框的原始状态
        $('#agreeInput').prop('checked', originalPrivacyChecked);

        // 恢复原始邮箱数据
        $('#email-container').empty();
        if (window.originalEmails && window.originalEmails.length > 0) {
            window.originalEmails.forEach(email => {
                const emailRow = $(`
                    <div class="weui-cell parking-cell email-row">
                        <div class="weui-cell__hd">
                            <label class="weui-label parking-label">Email</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input parking-input email-input" type="email" value="${email}" disabled>
                        </div>
                    </div>
                `);
                $('#email-container').append(emailRow);
            });
            emailCount = window.originalEmails.length;
        } else {
            // 如果没有原始邮箱数据，添加一个空的输入框
            const emailRow = $(`
                <div class="weui-cell parking-cell email-row">
                    <div class="weui-cell__hd">
                        <label class="weui-label parking-label">Email</label>
                    </div>
                    <div class="weui-cell__bd">
                        <input class="weui-input parking-input email-input" type="email" placeholder="Input your email" disabled>
                    </div>
                </div>
            `);
            $('#email-container').append(emailRow);
            emailCount = 1;
        }

        // 不触发输入验证，避免按钮状态被错误更新
        // $carNumber.trigger('input');
        // $openKeyboard.trigger('input');
        $withdrawBtn.show();
        $backBtn.hide();
    });

    // 撤销注册按钮处理 - 取消自动注册功能
    // 修改撤销操作的handleSubmit调用，移除隐私声明提示
    $withdrawBtn.unbind().click(function(event) {
        event.preventDefault();
        handleSubmit(
            true,
            '确定要撤销自动注册吗？',  // 移除原提示中的<br>部分
            '撤销成功！<br>系统将不再为你执行自动注册。',      // 简化成功提示
        );
    });

    // 省份选择键盘点击处理函数 - 处理省份选择并更新UI
    function keyboard(el) {
        var province = $(el).text();
        if (province) {
            $('#openKeyboard').text(province);
            $('#keyboardBox').hide();
            // 触发input事件以便按钮状态更新
            $('#openKeyboard').trigger('input');
        }
    }

    // 省份选择键盘点击事件绑定
    $('#Box ul li:not(.other)').click(function() {
        keyboard(this);
    });



    // Function to show the information dialog (Refactored)
    function showInfoDialog(isFromToggle) {
        const buttons = isFromToggle ? [{
            label: '禁用',
            type: 'default',
            onClick: function() {
                // 撤销切换状态
                $toggleMagicPlate.toggleClass('active');
                $toggleMagicPlate.text($toggleMagicPlate.hasClass('active') ? 'YES' : 'NO');
            }
        }, {
            label: '启用',
            type: 'primary',
            onClick: function() {
                console.log('状态确认保持');
            }
        }] : [{
            label: '已了解',
            type: 'default',
            onClick: function() {
                console.log('弹窗关闭');
            }
        }];
    
        var dialog = weui.dialog({
            title: '⚡限量版暗黑功能⚡',
            content: `
<div class="half-screen-content">
<strong>功能说明 (仅对少数开放，可见即有权限)</strong>
<ul class="half-screen-list">
    <li><span>本轮用"魔法车牌"帮你搞定注册~</span></li>
    <li><span>下一轮你委托一位不需要停车位的同事帮你注册相同车牌仍然可以通过~</span></li>
    <li><span>友情提示：魔法有风险，翻车自费修~</span></li>
</ul>
</div>`,
            className: 'custom-half-screen-dialog',
            buttons: buttons
        });

        // 确保移动端滚动正常工作
        setTimeout(function() {
            var dialogBd = $('.custom-half-screen-dialog .weui-dialog__bd');
            if (dialogBd.length) {
                dialogBd.on('touchstart', function(e) {
                    e.stopPropagation();
                });
                dialogBd.on('touchmove', function(e) {
                    e.stopPropagation();
                });
            }
        }, 100);
    }

    // 修改toggle按钮点击事件
    $toggleMagicPlate.unbind().click(function(event) {
        if (!$(this).hasClass('disabled')) {
            $(this).toggleClass('active');
            $(this).text($(this).hasClass('active') ? 'YES' : 'NO');
    
            if ($(this).hasClass('active')) {
                showInfoDialog(true);  // 传递参数表示来自toggle
            }
        }
    });

    // 问号图标保持原有调用方式
    $('#questionIcon').click(function() {
        showInfoDialog(false);  // 不传参数或显式传false
    });

    // 修改隐私声明弹窗事件
    $('#userMenuContent').click(function() {
        var dialog = weui.dialog({
            title: '⚡数据收集说明⚡',
            content: `
<div class="half-screen-content">
<strong>注册所需信息</strong>
<ul class="half-screen-list">
    <li><span>员工编号（Employee ID）</span></li>
    <li><span>车牌号（Plate Number）</span></li>
    <li><span>电子邮箱（Email）</span></li>
</ul>

<strong>数据处理方式</strong>
<ul class="half-screen-list">
    <li><span>数据仅在程序运行期间临时存储，注册完成后即时删除</span></li>
</ul>
</div>`,
            className: 'custom-half-screen-dialog',
            buttons: [{
                label: '拒绝',
                type: 'default',
                onClick: function() {
                    $('#agreeInput').prop('checked', false); // 取消勾选
                    triggerFormValidation(); // 触发验证更新按钮状态
                }
            }, {
                label: '同意',
                type: 'primary',
                onClick: function() {
                    $('#agreeInput').prop('checked', true); // 强制勾选
                    triggerFormValidation(); // 触发验证更新按钮状态
                }
            }]
        });

        // 确保移动端滚动正常工作
        setTimeout(function() {
            var dialogBd = $('.custom-half-screen-dialog .weui-dialog__bd');
            if (dialogBd.length) {
                dialogBd.on('touchstart', function(e) {
                    e.stopPropagation();
                });
                dialogBd.on('touchmove', function(e) {
                    e.stopPropagation();
                });
            }
        }, 100);
    });
});