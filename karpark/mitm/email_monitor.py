#!/usr/bin/env python3
"""
Email Monitor for MITM Proxy Restart

This module monitors emails for restart commands and triggers MITM proxy restarts
when valid restart emails are received.
"""

import threading
import time
from datetime import datetime, timedelta
from html.parser import <PERSON><PERSON><PERSON>arser
from typing import Dict, Any

from karpark.colorlog import logger
from karpark.common.config import MitmproxyConfig
from karpark.mail.email_client import EmailClient
from karpark.mail.email_db_operations import (
    add_processed_id,
    get_processed_ids,
    cleanup_old_records
)


class TextExtractor(HTMLParser):
    """Extract plain text from HTML content."""
    
    def __init__(self):
        super().__init__()
        self.result = []

    def handle_data(self, data):
        self.result.append(data.strip())

    def get_text(self):
        return ' '.join(self.result)


class EmailMonitor:
    """Monitors emails for MITM proxy restart commands."""
    
    def __init__(self, restart_callback=None):
        """Initialize email monitor.
        
        Args:
            restart_callback: Function to call when restart is triggered
        """
        self.email_client = EmailClient()
        self.restart_callback = restart_callback
        self.running = False
        self.monitor_thread = None
        
        # Configuration
        self.enabled = MitmproxyConfig.EmailMonitor.enabled
        self.check_interval = MitmproxyConfig.EmailMonitor.check_interval * 60  # Convert to seconds
        self.password = MitmproxyConfig.EmailMonitor.RestartCommand.password
        
        logger.info(f"Email monitor initialized (enabled: {self.enabled}, interval: {self.check_interval}s)")
    
    def extract_text_from_html(self, html_content: str) -> str:
        """Extract plain text from HTML content.
        
        Args:
            html_content: HTML content string
            
        Returns:
            Plain text content
        """
        try:
            extractor = TextExtractor()
            extractor.feed(html_content)
            return extractor.get_text()
        except Exception as e:
            logger.error(f"Failed to extract text from HTML: {e}")
            return ""
    
    def is_valid_restart_email(self, email_data: Dict[str, Any]) -> bool:
        """Check if email contains valid restart command.

        Simplified logic: Only checks if the password appears anywhere in the email
        subject or body content.

        Args:
            email_data: Email data dictionary

        Returns:
            True if email contains valid restart command, False otherwise
        """
        try:
            subject = email_data.get('subject', '').lower()
            text_content = email_data.get('text', '').lower()
            html_content = email_data.get('html', '')
            
            # Extract text from HTML if available
            if html_content and not text_content:
                text_content = self.extract_text_from_html(html_content).lower()
            
            # Combine subject and content for analysis
            full_content = f"{subject} {text_content}"

            # Check if password is present anywhere in the email
            password_present = self.password.lower() in full_content
            if not password_present:
                logger.debug("Restart password not found in email")
                return False

            logger.info("Valid restart email detected - password found in email content")
            return True
            
        except Exception as e:
            logger.error(f"Error validating restart email: {e}")
            return False

    def send_cooldown_reply(self, original_email: Dict[str, Any], last_restart_time: datetime, remaining_minutes: int) -> bool:
        """Send a reply email about restart cooldown.

        Args:
            original_email: Original email data
            last_restart_time: Time of last restart
            remaining_minutes: Minutes remaining in cooldown

        Returns:
            True if reply was sent successfully, False otherwise
        """
        try:
            if not MitmproxyConfig.EmailMonitor.RestartCommand.ReplySettings.enabled:
                logger.debug("Reply emails are disabled in configuration")
                return False

            # Extract sender information
            sender = original_email.get('sender', '<EMAIL>')
            original_subject = original_email.get('subject', 'Restart Request')

            # Calculate next available time
            cooldown_minutes = MitmproxyConfig.EmailMonitor.RestartCommand.cooldown_minutes
            next_available_time = last_restart_time + timedelta(minutes=cooldown_minutes)

            # Format email content
            subject = MitmproxyConfig.EmailMonitor.RestartCommand.ReplySettings.subject_template.format(
                original_subject=original_subject
            )

            message = MitmproxyConfig.EmailMonitor.RestartCommand.ReplySettings.message_template.format(
                cooldown_minutes=cooldown_minutes,
                last_restart_time=last_restart_time.strftime('%Y-%m-%d %H:%M:%S'),
                next_available_time=next_available_time.strftime('%Y-%m-%d %H:%M:%S'),
                remaining_minutes=remaining_minutes
            )

            # Send reply
            success = self.email_client.send_mail(
                subject=subject,
                text=message,
                to=[sender]
            )

            if success:
                logger.info(f"Sent cooldown reply to {sender}")
                return True
            else:
                logger.error(f"Failed to send cooldown reply to {sender}")
                return False

        except Exception as e:
            logger.error(f"Error sending cooldown reply: {e}")
            return False
    
    def process_restart_emails(self) -> bool:
        """Process emails and check for restart commands.
        
        Returns:
            True if restart command was found and processed, False otherwise
        """
        try:
            logger.debug("Checking for restart emails...")
            
            # Get processed email IDs to avoid reprocessing
            processed_ids = get_processed_ids()
            
            # Fetch recent emails
            emails = []
            email_count = 0
            for sender, subject, content in self.email_client.recv_mail():
                emails.append({
                    'id': f"{sender}_{subject}_{hash(content)}",  # Create unique ID
                    'sender': sender,
                    'subject': subject,
                    'text': content,
                    'html': ''  # recv_mail doesn't separate HTML/text
                })
                email_count += 1
                if email_count >= 50:  # Limit to last 50 emails
                    break
            
            restart_triggered = False
            
            for email_data in emails:
                email_id = email_data.get('id')
                if not email_id or email_id in processed_ids:
                    continue
                
                # Check if this is a valid restart email
                if self.is_valid_restart_email(email_data):
                    logger.info(f"Processing restart command from email ID: {email_id}")

                    # Check restart cooldown
                    if self.restart_callback:
                        try:
                            success = self.restart_callback(email_data)
                            if success:
                                logger.info("MITM proxy restart triggered successfully")
                                restart_triggered = True
                            else:
                                logger.info("Restart request processed (may have been in cooldown)")
                        except Exception as e:
                            logger.error(f"Error during restart callback: {e}")
                    else:
                        logger.warning("No restart callback configured")
                
                # Mark email as processed
                add_processed_id(email_id)
            
            return restart_triggered
            
        except Exception as e:
            logger.error(f"Error processing restart emails: {e}")
            return False
    
    def cleanup_old_emails(self) -> None:
        """Clean up old processed email records."""
        try:
            retention_days = MitmproxyConfig.EmailMonitor.Retention.days
            cleanup_old_records(retention_days)
            logger.debug(f"Cleaned up email records older than {retention_days} days")
        except Exception as e:
            logger.error(f"Error cleaning up old email records: {e}")
    
    def monitor_loop(self) -> None:
        """Main monitoring loop."""
        logger.info("Email monitoring loop started")
        
        while self.running:
            try:
                # Process restart emails
                self.process_restart_emails()
                
                # Cleanup old records (do this less frequently)
                if datetime.now().minute % 30 == 0:  # Every 30 minutes
                    self.cleanup_old_emails()
                
                # Wait for next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in email monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying on error
    
    def start(self) -> bool:
        """Start email monitoring.
        
        Returns:
            True if monitoring started successfully, False otherwise
        """
        if not self.enabled:
            logger.info("Email monitoring is disabled in configuration")
            return False
        
        if self.running:
            logger.warning("Email monitoring is already running")
            return True
        
        try:
            self.running = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("Email monitoring started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start email monitoring: {e}")
            self.running = False
            return False
    
    def stop(self) -> None:
        """Stop email monitoring."""
        if not self.running:
            return
        
        logger.info("Stopping email monitoring...")
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        logger.info("Email monitoring stopped")
    
    def is_running(self) -> bool:
        """Check if email monitoring is running.
        
        Returns:
            True if monitoring is active, False otherwise
        """
        return self.running and (self.monitor_thread and self.monitor_thread.is_alive())
