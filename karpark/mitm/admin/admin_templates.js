// HTML templates for admin panel managers

function getEnrollmentManagerHTML() {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Enrollment Manager</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .btn { padding: 5px 10px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
                .btn-primary { background-color: #007bff; color: white; }
                .btn-danger { background-color: #dc3545; color: white; }
                .btn-success { background-color: #28a745; color: white; }
                .loading { text-align: center; padding: 20px; }
                .error { color: red; padding: 10px; }
                .success { color: green; padding: 10px; }
            </style>
        </head>
        <body>
            <h2>Enrollment Manager</h2>
            <div id="message"></div>
            <button class="btn btn-success" onclick="refreshData()">Refresh</button>
            <button class="btn btn-danger" onclick="cleanInvalidCookies()">Clean Invalid Cookies</button>

            <div id="loading" class="loading">Loading enrollments...</div>
            <div id="content" style="display: none;">
                <table id="enrollments-table">
                    <thead>
                        <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Plate Number</th>
                            <th>Location</th>
                            <th>Magic Plate</th>
                            <th>Withdrawn</th>
                            <th>Emails</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="enrollments-tbody">
                    </tbody>
                </table>
            </div>

            <script>
                function updateEnrollmentsTable(result) {
                    const loading = document.getElementById('loading');
                    const content = document.getElementById('content');
                    const tbody = document.getElementById('enrollments-tbody');
                    const message = document.getElementById('message');

                    loading.style.display = 'none';

                    if (result.success) {
                        content.style.display = 'block';
                        tbody.innerHTML = '';

                        result.data.forEach(enrollment => {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td>' + enrollment.ee_id + '</td>' +
                                          '<td>' + enrollment.ee_name + '</td>' +
                                          '<td>' + enrollment.plate_number + '</td>' +
                                          '<td>' + enrollment.registration_location + '</td>' +
                                          '<td>' + enrollment.use_magic_plate + '</td>' +
                                          '<td>' + enrollment.is_withdrawn + '</td>' +
                                          '<td>' + enrollment.emails + '</td>' +
                                          '<td>' + enrollment.updated_time + '</td>' +
                                          '<td>' +
                                            '<button class="btn btn-primary" onclick="editEnrollment(\\'' + enrollment.ee_id + '\\')">Edit</button>' +
                                            '<button class="btn btn-danger" onclick="deleteEnrollment(\\'' + enrollment.ee_id + '\\')">Delete</button>' +
                                          '</td>';
                            tbody.appendChild(row);
                        });

                        message.innerHTML = '<div class="success">Loaded ' + result.count + ' enrollments</div>';
                    } else {
                        message.innerHTML = '<div class="error">Error: ' + result.error + '</div>';
                    }
                }

                function refreshData() {
                    document.getElementById('loading').style.display = 'block';
                    document.getElementById('content').style.display = 'none';
                    parent.loadEnrollments();
                }

                function cleanInvalidCookies() {
                    if (confirm('Are you sure you want to clean invalid cookies?')) {
                        parent.callAdminAPI('clean_cookies').then(result => {
                            const message = document.getElementById('message');
                            if (result.success) {
                                message.innerHTML = '<div class="success">' + result.message + '</div>';
                            } else {
                                message.innerHTML = '<div class="error">Error: ' + result.error + '</div>';
                            }
                        });
                    }
                }

                function editEnrollment(eeId) {
                    alert('Edit functionality not implemented yet for: ' + eeId);
                }

                function deleteEnrollment(eeId) {
                    if (confirm('Are you sure you want to delete enrollment for ' + eeId + '?')) {
                        parent.callAdminAPI('delete_enrollment', {ee_id: eeId}).then(result => {
                            const message = document.getElementById('message');
                            if (result.success) {
                                message.innerHTML = '<div class="success">' + result.message + '</div>';
                                refreshData();
                            } else {
                                message.innerHTML = '<div class="error">Error: ' + result.error + '</div>';
                            }
                        });
                    }
                }
            </script>
        </body>
        </html>
    `;
}

function getCookieManagerHTML() {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Cookie Manager</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .btn { padding: 5px 10px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
                .btn-primary { background-color: #007bff; color: white; }
                .btn-danger { background-color: #dc3545; color: white; }
                .btn-success { background-color: #28a745; color: white; }
                .loading { text-align: center; padding: 20px; }
                .error { color: red; padding: 10px; }
                .success { color: green; padding: 10px; }
                .cookie-cell { max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
            </style>
        </head>
        <body>
            <h2>Cookie Manager</h2>
            <div id="message"></div>
            <button class="btn btn-success" onclick="refreshData()">Refresh</button>

            <div id="loading" class="loading">Loading cookies...</div>
            <div id="content" style="display: none;">
                <table id="cookies-table">
                    <thead>
                        <tr>
                            <th>Cookie (truncated)</th>
                            <th>Employee ID</th>
                            <th>Valid</th>
                            <th>Created</th>
                            <th>Updated</th>
                        </tr>
                    </thead>
                    <tbody id="cookies-tbody">
                    </tbody>
                </table>
            </div>

            <script>
                function updateCookiesTable(result) {
                    const loading = document.getElementById('loading');
                    const content = document.getElementById('content');
                    const tbody = document.getElementById('cookies-tbody');
                    const message = document.getElementById('message');

                    loading.style.display = 'none';

                    if (result.success) {
                        content.style.display = 'block';
                        tbody.innerHTML = '';

                        result.data.forEach(cookie => {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td class="cookie-cell" title="' + cookie.cookie + '">' + cookie.cookie + '</td>' +
                                          '<td>' + cookie.ee_id + '</td>' +
                                          '<td>' + cookie.is_valid + '</td>' +
                                          '<td>' + cookie.created_time + '</td>' +
                                          '<td>' + cookie.updated_time + '</td>';
                            tbody.appendChild(row);
                        });

                        message.innerHTML = '<div class="success">Loaded ' + result.count + ' cookies</div>';
                    } else {
                        message.innerHTML = '<div class="error">Error: ' + result.error + '</div>';
                    }
                }

                function refreshData() {
                    document.getElementById('loading').style.display = 'block';
                    document.getElementById('content').style.display = 'none';
                    parent.loadCookies();
                }
            </script>
        </body>
        </html>
    `;
}

function getSystemStatusHTML() {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>System Status</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
                .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px; }
                .stat-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; text-align: center; }
                .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
                .stat-label { color: #6c757d; margin-top: 5px; }
                .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
                .btn-success { background-color: #28a745; color: white; }
                .loading { text-align: center; padding: 20px; }
                .error { color: red; padding: 10px; }
                .success { color: green; padding: 10px; }
                .last-updated { text-align: center; color: #6c757d; margin-top: 20px; }
            </style>
        </head>
        <body>
            <h2>System Status</h2>
            <div id="message"></div>
            <button class="btn btn-success" onclick="refreshData()">Refresh</button>

            <div id="loading" class="loading">Loading system statistics...</div>
            <div id="content" style="display: none;">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-enrollments">-</div>
                        <div class="stat-label">Total Enrollments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="active-enrollments">-</div>
                        <div class="stat-label">Active Enrollments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="withdrawn-enrollments">-</div>
                        <div class="stat-label">Withdrawn Enrollments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-cookies">-</div>
                        <div class="stat-label">Total Cookies</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="invalid-cookies">-</div>
                        <div class="stat-label">Invalid Cookies</div>
                    </div>
                </div>
                <div class="last-updated" id="last-updated"></div>
            </div>

            <script>
                function updateSystemStats(result) {
                    const loading = document.getElementById('loading');
                    const content = document.getElementById('content');
                    const message = document.getElementById('message');

                    loading.style.display = 'none';

                    if (result.success) {
                        content.style.display = 'block';
                        const data = result.data;

                        document.getElementById('total-enrollments').textContent = data.total_enrollments;
                        document.getElementById('active-enrollments').textContent = data.active_enrollments;
                        document.getElementById('withdrawn-enrollments').textContent = data.withdrawn_enrollments;
                        document.getElementById('total-cookies').textContent = data.total_cookies;
                        document.getElementById('invalid-cookies').textContent = data.invalid_cookies;
                        document.getElementById('last-updated').textContent = 'Last updated: ' + data.last_updated;

                        message.innerHTML = '<div class="success">System statistics loaded successfully</div>';
                    } else {
                        message.innerHTML = '<div class="error">Error: ' + result.error + '</div>';
                    }
                }

                function refreshData() {
                    document.getElementById('loading').style.display = 'block';
                    document.getElementById('content').style.display = 'none';
                    parent.loadSystemStats();
                }
            </script>
        </body>
        </html>
    `;
}

function getConfigManagerHTML() {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Configuration Manager</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
                .config-section { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; }
                .config-title { font-size: 1.2em; font-weight: bold; margin-bottom: 15px; color: #495057; }
                .config-item { margin: 10px 0; }
                .config-label { font-weight: 500; color: #6c757d; }
                .config-value { margin-left: 10px; font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; }
                .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
                .btn-primary { background-color: #007bff; color: white; }
                .btn-warning { background-color: #ffc107; color: black; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; color: #856404; }
            </style>
        </head>
        <body>
            <h2>Configuration Manager</h2>

            <div class="warning">
                <strong>⚠️ Warning:</strong> Configuration management through the web interface is read-only for security reasons.
                To modify configurations, please edit the YAML files directly on the server.
            </div>

            <div class="config-section">
                <div class="config-title">🔐 Access Control</div>
                <div class="config-item">
                    <span class="config-label">Admin Users:</span>
                    <span class="config-value" id="admin-users">Loading...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Privileged Users:</span>
                    <span class="config-value" id="privileged-users">Loading...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Authorized Users:</span>
                    <span class="config-value" id="authorized-users">Loading...</span>
                </div>
            </div>

            <div class="config-section">
                <div class="config-title">📁 File Paths</div>
                <div class="config-item">
                    <span class="config-label">Access Control File:</span>
                    <span class="config-value">karpark/config/access_control.yaml</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Database File:</span>
                    <span class="config-value">karpark/data/enrollment.db</span>
                </div>
            </div>

            <button class="btn btn-primary" onclick="refreshConfig()">Refresh Configuration</button>
            <button class="btn btn-warning" onclick="reloadConfig()">Reload Configuration</button>

            <script>
                function refreshConfig() {
                    // This would load current configuration values
                    // For now, we'll show placeholder values
                    document.getElementById('admin-users').textContent = 'I072162';
                    document.getElementById('privileged-users').textContent = 'I072162';
                    document.getElementById('authorized-users').textContent = 'I072162';
                }

                function reloadConfig() {
                    alert('Configuration reload functionality would be implemented here.\\nThis would trigger a configuration reload on the server.');
                }

                // Load initial config
                refreshConfig();
            </script>
        </body>
        </html>
    `;
}
