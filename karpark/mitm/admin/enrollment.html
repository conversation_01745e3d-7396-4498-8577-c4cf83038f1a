<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Enrollment Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #0583d2;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            table-layout: fixed; /* 固定表格布局 */
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
            word-wrap: break-word; /* 允许单词换行 */
            overflow: hidden; /* 隐藏溢出内容 */
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 13px;
        }

        /* 设置列宽 */
        th:nth-child(1), td:nth-child(1) { width: 10%; } /* Employee ID */
        th:nth-child(2), td:nth-child(2) { width: 12%; } /* Employee Name */
        th:nth-child(3), td:nth-child(3) { width: 12%; } /* Plate Number */
        th:nth-child(4), td:nth-child(4) { width: 10%; } /* Location */
        th:nth-child(5), td:nth-child(5) { width: 8%; }  /* Magic Plate */
        th:nth-child(6), td:nth-child(6) { width: 8%; }  /* Withdrawn */
        th:nth-child(7), td:nth-child(7) { width: 15%; } /* Emails */
        th:nth-child(8), td:nth-child(8) { width: 12%; } /* Updated Time */
        th:nth-child(9), td:nth-child(9) { width: 13%; } /* Actions */

        /* 邮箱列特殊处理 */
        .email-cell {
            max-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 时间列特殊处理 */
        .time-cell {
            font-size: 12px;
            white-space: nowrap;
        }

        /* 按钮样式优化 */
        .btn {
            padding: 4px 8px;
            font-size: 12px;
            margin: 1px;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        /* Modal button hover effects */
        #editModal button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        /* Input focus effects */
        #editModal input:focus, #editModal select:focus, #editModal textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
    </style>
</head>
<body>
    <h2>📋 Enrollment Manager</h2>
    
    <div id="message"></div>
    
    <div class="controls">
        <button class="btn btn-success" onclick="refreshData()">🔄 Refresh</button>
    </div>
    
    <div id="loading" class="loading" style="display: none;">
        Loading enrollments...
    </div>
    
    <div id="content">
        <table>
            <thead>
                <tr>
                    <th>EE ID</th>
                    <th>Name</th>
                    <th>Plate</th>
                    <th>Location</th>
                    <th>Magic</th>
                    <th>Withdrawn</th>
                    <th>Emails</th>
                    <th>Updated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="data-tbody">
                <tr>
                    <td>I072162</td>
                    <td>Test User</td>
                    <td>沪A12345</td>
                    <td>PVG06</td>
                    <td>No</td>
                    <td>No</td>
                    <td><EMAIL></td>
                    <td>2025-06-14 10:00:00</td>
                    <td>
                        <button class="btn btn-primary" onclick="editItem('I072162')">Edit</button>
                        <button class="btn btn-danger" onclick="deleteItem('I072162')">Delete</button>
                    </td>
                </tr>
                <tr>
                    <td>I072163</td>
                    <td>Another User</td>
                    <td>沪B67890</td>
                    <td>PVG07</td>
                    <td>Yes</td>
                    <td>No</td>
                    <td><EMAIL></td>
                    <td>2025-06-14 11:00:00</td>
                    <td>
                        <button class="btn btn-primary" onclick="editItem('I072163')">Edit</button>
                        <button class="btn btn-danger" onclick="deleteItem('I072163')">Delete</button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow-y: auto;">
        <div style="position: relative; margin: 20px auto; background: white; padding: 20px; border-radius: 8px; width: 95%; max-width: 600px; min-height: calc(100vh - 40px); box-sizing: border-box;">
            <h3 style="margin-top: 0; text-align: center; color: #333;">📝 Edit Enrollment</h3>
            <form id="editForm">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">👤 Employee ID:</label>
                    <input type="text" id="edit-ee-id" readonly style="width: 100%; padding: 12px; margin-top: 5px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">📛 Employee Name:</label>
                    <input type="text" id="edit-ee-name" style="width: 100%; padding: 12px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🚗 Plate Number:</label>
                    <input type="text" id="edit-plate-number" style="width: 100%; padding: 12px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">📍 Registration Location:</label>
                    <input type="text" id="edit-location" style="width: 100%; padding: 12px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">✨ Magic Plate:</label>
                    <select id="edit-magic-plate" style="width: 100%; padding: 12px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🚫 Withdrawn:</label>
                    <select id="edit-withdrawn" style="width: 100%; padding: 12px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">📧 Emails (comma separated):</label>
                    <textarea id="edit-emails" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;"></textarea>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" onclick="closeEditModal()" style="margin-right: 15px; padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">❌ Cancel</button>
                    <button type="button" onclick="saveEdit()" style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">💾 Save</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showMessage(text, type) {
            var messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '<div class="' + type + '">' + text + '</div>';
            setTimeout(function() {
                messageDiv.innerHTML = '';
            }, 3000);
        }
        
        function refreshData() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';

            // Call real API to get enrollment data
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_enrollments'
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';

                if (data.success) {
                    updateTable(data.data);
                    showMessage('✅ Enrollment data refreshed successfully! (' + data.count + ' records)', 'success');
                } else {
                    showMessage('❌ Failed to load data: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function formatDateTime(dateTimeString) {
            // Backend now returns pre-formatted local time strings (YYYY-MM-DD HH:MM:SS)
            // Just return as-is since it's already formatted
            return dateTimeString || '';
        }

        function formatEmails(emails) {
            if (!emails) return '';
            var emailStr = Array.isArray(emails) ? emails.join(', ') : emails;
            return emailStr.length > 20 ? emailStr.substring(0, 20) + '...' : emailStr;
        }

        function updateTable(enrollments) {
            var tbody = document.getElementById('data-tbody');
            tbody.innerHTML = '';

            enrollments.forEach(function(enrollment) {
                var row = document.createElement('tr');
                var formattedEmails = formatEmails(enrollment.emails);
                var formattedTime = formatDateTime(enrollment.updated_time);

                row.innerHTML =
                    '<td>' + (enrollment.ee_id || '') + '</td>' +
                    '<td>' + (enrollment.ee_name || '') + '</td>' +
                    '<td>' + (enrollment.plate_number || '') + '</td>' +
                    '<td>' + (enrollment.registration_location || '') + '</td>' +
                    '<td>' + (enrollment.use_magic_plate ? 'Yes' : 'No') + '</td>' +
                    '<td>' + (enrollment.is_withdrawn ? 'Yes' : 'No') + '</td>' +
                    '<td class="email-cell" title="' + (Array.isArray(enrollment.emails) ? enrollment.emails.join(', ') : (enrollment.emails || '')) + '">' + formattedEmails + '</td>' +
                    '<td class="time-cell">' + formattedTime + '</td>' +
                    '<td>' +
                        '<button class="btn btn-primary" onclick="editItem(\'' + enrollment.ee_id + '\')">Edit</button> ' +
                        '<button class="btn btn-danger" onclick="deleteItem(\'' + enrollment.ee_id + '\')">Del</button>' +
                    '</td>';
                tbody.appendChild(row);
            });
        }
        

        
        function editItem(eeId) {
            // Get enrollment details
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_enrollment_details',
                    ee_id: eeId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    var enrollment = data.data;
                    document.getElementById('edit-ee-id').value = enrollment.ee_id || '';
                    document.getElementById('edit-ee-name').value = enrollment.ee_name || '';
                    document.getElementById('edit-plate-number').value = enrollment.plate_number || '';
                    document.getElementById('edit-location').value = enrollment.registration_location || '';
                    document.getElementById('edit-magic-plate').value = enrollment.use_magic_plate ? 'true' : 'false';
                    document.getElementById('edit-withdrawn').value = enrollment.is_withdrawn ? 'true' : 'false';
                    document.getElementById('edit-emails').value = Array.isArray(enrollment.emails) ? enrollment.emails.join(', ') : (enrollment.emails || '');

                    document.getElementById('editModal').style.display = 'block';
                } else {
                    showMessage('❌ Failed to load enrollment details: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        function saveEdit() {
            var eeId = document.getElementById('edit-ee-id').value;
            var emails = document.getElementById('edit-emails').value.split(',').map(e => e.trim()).filter(e => e);

            var updates = {
                ee_name: document.getElementById('edit-ee-name').value,
                plate_number: document.getElementById('edit-plate-number').value,
                registration_location: document.getElementById('edit-location').value,
                use_magic_plate: document.getElementById('edit-magic-plate').value === 'true',
                is_withdrawn: document.getElementById('edit-withdrawn').value === 'true',
                emails: emails
            };

            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_enrollment',
                    ee_id: eeId,
                    updates: updates
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeEditModal();
                    refreshData();
                    showMessage('✅ Enrollment updated successfully!', 'success');
                } else {
                    showMessage('❌ Failed to update enrollment: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function deleteItem(eeId) {
            if (confirm('Are you sure you want to delete enrollment for ' + eeId + '?')) {
                fetch('/mobile/parking/admin/api', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete_enrollment',
                        ee_id: eeId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        refreshData();
                        showMessage('✅ Enrollment for ' + eeId + ' deleted successfully!', 'success');
                    } else {
                        showMessage('❌ Failed to delete enrollment: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showMessage('❌ Network error: ' + error.message, 'error');
                });
            }
        }
        
        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });
    </script>
</body>
</html>
