<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Karpark Admin Panel</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #F8F8F8;
            margin: 0;
            padding: 20px;
        }
        
        .admin-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #0583d2;
            margin-bottom: 10px;
        }
        
        .admin-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .admin-menu {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .menu-item {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
            cursor: pointer;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background-color: #f8f8f8;
        }
        
        .menu-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .menu-desc {
            font-size: 12px;
            color: #999;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0583d2;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-btn:hover {
            background: #0470b8;
        }
    </style>
</head>
<body>
    <a href="javascript:history.back()" class="back-btn">← Back</a>
    
    <div class="admin-header">
        <div class="admin-title">🚗 Karpark Admin Panel</div>
        <div class="admin-subtitle">Enrollment Management System</div>
    </div>
    
    <div class="admin-menu">
        <div class="menu-item" onclick="openPage('enrollment')">
            <div class="menu-title">📋 Enrollment Manager</div>
            <div class="menu-desc">Review, add, edit, and delete parking enrollments</div>
        </div>
        
        <div class="menu-item" onclick="openPage('cookie')">
            <div class="menu-title">🍪 Cookie Manager</div>
            <div class="menu-desc">Manage user cookies and authentication</div>
        </div>
        
        <div class="menu-item" onclick="openPage('status')">
            <div class="menu-title">📊 System Status</div>
            <div class="menu-desc">View system health and statistics</div>
        </div>
        
        <div class="menu-item" onclick="openPage('config')">
            <div class="menu-title">⚙️ Configuration</div>
            <div class="menu-desc">Manage system configuration and settings</div>
        </div>
    </div>
    
    <script>
        function openPage(type) {
            var url = '';
            
            if (type === 'enrollment') {
                url = '/mobile/parking/admin/enrollment.html';
            } else if (type === 'cookie') {
                url = '/mobile/parking/admin/cookie.html';
            } else if (type === 'status') {
                url = '/mobile/parking/admin/status.html';
            } else if (type === 'config') {
                url = '/mobile/parking/admin/config.html';
            }
            
            // Open in new window instead of iframe
            if (url) {
                window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
            }
        }
    </script>
</body>
</html>
