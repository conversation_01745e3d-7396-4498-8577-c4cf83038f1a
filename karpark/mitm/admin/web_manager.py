#!/usr/bin/env python3
"""
Web-based Enrollment Manager for Admin Panel

This module provides a web interface for the enrollment management functionality,
similar to the CLI version but accessible through the admin panel.
"""

import json
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from karpark.registrar.db_operations import (
    get_all_enrollments_detailed, get_enrollment_by_ee_id,
    update_enrollment, delete_enrollment,
    get_all_cookie_owners, save_cookie_owner, db_save_enrollment
)
from karpark.common.config import AccessControl
from karpark.common.paths import paths


def format_time_for_display(time_str: str) -> str:
    """
    Convert UTC time string to local time and format for display.

    Args:
        time_str: UTC time string in ISO format

    Returns:
        Formatted local time string (YYYY-MM-DD HH:MM:SS)
    """
    if not time_str:
        return ''

    try:
        # Parse UTC time
        if time_str.endswith('Z'):
            utc_time = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        elif '+' in time_str or time_str.endswith('00:00'):
            utc_time = datetime.fromisoformat(time_str)
        else:
            # Assume UTC if no timezone info
            utc_time = datetime.fromisoformat(time_str).replace(tzinfo=timezone.utc)

        # Convert to local time (UTC+8 for China)
        local_time = utc_time.astimezone(timezone(timedelta(hours=8)))

        # Format as readable string
        return local_time.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        # If parsing fails, return original string
        return time_str


class WebEnrollmentManager:
    """Web-based enrollment management class."""
    
    def __init__(self):
        self.table_headers = [
            'ee_id', 'ee_name', 'plate_number', 'registration_location',
            'use_magic_plate', 'is_withdrawn', 'emails', 'updated_time'
        ]
        self.cookie_headers = [
            'cookie', 'ee_id', 'is_valid', 'created_time', 'updated_time'
        ]
    
    def get_enrollments_data(self) -> Dict[str, Any]:
        """Get all enrollments data for web display."""
        try:
            enrollments = get_all_enrollments_detailed()
            
            # Format data for web display
            formatted_enrollments = []
            for enrollment in enrollments:
                formatted_enrollment = {
                    'ee_id': enrollment.get('ee_id', ''),
                    'ee_name': enrollment.get('ee_name', ''),
                    'plate_number': enrollment.get('plate_number', ''),
                    'registration_location': enrollment.get('registration_location', ''),
                    'use_magic_plate': 'Yes' if enrollment.get('use_magic_plate') else 'No',
                    'is_withdrawn': 'Yes' if enrollment.get('is_withdrawn') else 'No',
                    'emails': ', '.join(enrollment.get('emails', [])) if enrollment.get('emails') else '',
                    'created_time': format_time_for_display(enrollment.get('created_time', '')),
                    'updated_time': format_time_for_display(enrollment.get('updated_time', ''))
                }
                formatted_enrollments.append(formatted_enrollment)
            
            return {
                'success': True,
                'data': formatted_enrollments,
                'count': len(formatted_enrollments)
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'count': 0
            }
    
    def get_cookie_owners_data(self) -> Dict[str, Any]:
        """Get all cookie owners data for web display."""
        try:
            cookie_owners = get_all_cookie_owners()

            # Format data for web display
            formatted_owners = []
            for owner in cookie_owners:
                formatted_owner = {
                    'cookie': owner.get('cookie', '')[:50] + '...' if len(owner.get('cookie', '')) > 50 else owner.get('cookie', ''),
                    'ee_id': owner.get('ee_id', ''),
                    'is_valid': owner.get('is_valid', False),  # Keep as boolean for JavaScript
                    'order': owner.get('order', 0),
                    'total': owner.get('total', 0),
                    'register_time': format_time_for_display(owner.get('register_time', '')),
                    'created_time': format_time_for_display(owner.get('created_time', '')),
                    'updated_time': format_time_for_display(owner.get('updated_time', ''))
                }
                formatted_owners.append(formatted_owner)
            
            return {
                'success': True,
                'data': formatted_owners,
                'count': len(formatted_owners)
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'count': 0
            }
    
    def get_enrollment_details(self, ee_id: str) -> Dict[str, Any]:
        """Get detailed enrollment information for a specific employee."""
        try:
            enrollment = get_enrollment_by_ee_id(ee_id)
            if enrollment:
                return {
                    'success': True,
                    'data': enrollment
                }
            else:
                return {
                    'success': False,
                    'error': f'No enrollment found for employee ID: {ee_id}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_enrollment_data(self, ee_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update enrollment data for a specific employee."""
        try:
            success = update_enrollment(ee_id, updates)
            if success:
                return {
                    'success': True,
                    'message': f'Enrollment updated successfully for {ee_id}'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to update enrollment'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_enrollment_data(self, ee_id: str) -> Dict[str, Any]:
        """Delete enrollment data for a specific employee."""
        try:
            success = delete_enrollment(ee_id)
            if success:
                return {
                    'success': True,
                    'message': f'Enrollment deleted successfully for {ee_id}'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to delete enrollment'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def add_new_enrollment(self, enrollment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add a new enrollment."""
        try:
            success = db_save_enrollment(
                cookie=enrollment_data.get('cookie', ''),
                ee_id=enrollment_data.get('ee_id', ''),
                ee_name=enrollment_data.get('ee_name', ''),
                plate_number=enrollment_data.get('plate_number', ''),
                registration_location=enrollment_data.get('registration_location', ''),
                use_magic_plate=int(enrollment_data.get('use_magic_plate', 0)),
                is_withdrawn=int(enrollment_data.get('is_withdrawn', 0)),
                emails=enrollment_data.get('emails', [])
            )
            
            if success:
                # Also save cookie owner information if cookie is provided
                if enrollment_data.get('cookie'):
                    save_cookie_owner(
                        enrollment_data['cookie'],
                        enrollment_data['ee_id']
                    )
                
                return {
                    'success': True,
                    'message': f'Enrollment added successfully for {enrollment_data.get("ee_id", "")}'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to add enrollment'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    

    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        try:
            enrollments = get_all_enrollments_detailed()
            cookie_owners = get_all_cookie_owners()

            active_enrollments = [e for e in enrollments if not e.get('is_withdrawn')]
            withdrawn_enrollments = [e for e in enrollments if e.get('is_withdrawn')]

            # Calculate valid cookies (all cookies are considered valid now)
            total_cookies = len(cookie_owners)
            valid_cookies = total_cookies

            # Check database status
            database_status = 'online'  # If we can query, database is online

            return {
                'success': True,
                'data': {
                    'total_enrollments': len(enrollments),
                    'active_enrollments': len(active_enrollments),
                    'withdrawn_enrollments': len(withdrawn_enrollments),
                    'total_cookies': total_cookies,
                    'invalid_cookies': 0,  # No invalid cookies tracking anymore
                    'valid_cookies': valid_cookies,
                    'database_status': database_status,
                    'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'data': {
                    'total_enrollments': 0,
                    'active_enrollments': 0,
                    'withdrawn_enrollments': 0,
                    'total_cookies': 0,
                    'invalid_cookies': 0,
                    'valid_cookies': 0,
                    'database_status': 'offline',
                    'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

    def get_configuration(self) -> Dict[str, Any]:
        """Get current configuration."""
        try:
            # Load access control configuration
            AccessControl.load_from_config()

            # Load base.yaml configuration
            base_config = {}
            base_config_path = paths.base_config_file
            if base_config_path.exists():
                with open(base_config_path, 'r', encoding='utf-8') as f:
                    base_config = yaml.safe_load(f) or {}

            # Load email configuration
            email_config = {}
            email_config_path = paths.email_address_file
            if email_config_path.exists():
                with open(email_config_path, 'r', encoding='utf-8') as f:
                    email_config = yaml.safe_load(f) or {}

            config_data = {
                'access_control': {
                    'admin_users': AccessControl.Users.admin_users,
                    'privileged_users': AccessControl.Users.privileged_users,
                    'authorized_users': AccessControl.Users.authorized_users
                },
                'base_config': base_config,
                'email_config': email_config
            }

            return {
                'success': True,
                'data': config_data
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}



    def update_access_control(self, updates: Dict[str, List[str]]) -> Dict[str, Any]:
        """Update access control configuration with enforced hierarchy."""
        try:
            access_control_file = paths.access_control_file

            # Read current configuration
            if access_control_file.exists():
                with open(access_control_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
            else:
                config = {}

            # Get user lists and convert to uppercase
            admin_users = [user.upper().strip() for user in updates.get('admin_users', []) if user.strip()]
            privileged_users = [user.upper().strip() for user in updates.get('privileged_users', []) if user.strip()]
            authorized_users = [user.upper().strip() for user in updates.get('authorized_users', []) if user.strip()]

            # Enforce hierarchy: admin_users must be in privileged_users and authorized_users
            # privileged_users must be in authorized_users
            for admin_user in admin_users:
                if admin_user not in privileged_users:
                    privileged_users.append(admin_user)
                if admin_user not in authorized_users:
                    authorized_users.append(admin_user)

            for privileged_user in privileged_users:
                if privileged_user not in authorized_users:
                    authorized_users.append(privileged_user)

            # Update configuration with enforced hierarchy
            config['admin_users'] = admin_users
            config['privileged_users'] = privileged_users
            config['authorized_users'] = authorized_users

            # Write updated configuration
            with open(access_control_file, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True, default_flow_style=False)

            # Reload configuration in memory
            AccessControl.load_from_config()

            return {
                'success': True,
                'message': 'Access control configuration updated successfully (hierarchy enforced)'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_base_config(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update base.yaml configuration."""
        try:
            base_config_path = paths.base_config_file

            # Read current configuration
            if base_config_path.exists():
                with open(base_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
            else:
                config = {}

            # Update configuration with provided updates
            config.update(updates)

            # Write updated configuration with consistent formatting
            yaml_content = yaml.safe_dump(config, allow_unicode=True, default_flow_style=False, sort_keys=False)

            # Post-process to ensure time values are properly quoted to prevent YAML parsing issues
            import re
            yaml_content = re.sub(r"(\s+work_time_(?:start|end):\s*)(\d{2}:\d{2}(?::\d{2})?)\s*$", r"\1'\2'", yaml_content, flags=re.MULTILINE)

            with open(base_config_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)

            return {
                'success': True,
                'message': 'Base configuration updated successfully'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_base_config_partial(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update base.yaml configuration with partial updates (merge with existing)."""
        try:
            base_config_path = paths.base_config_file

            # Read current configuration
            if base_config_path.exists():
                with open(base_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
            else:
                config = {}

            # Deep merge the updates
            def deep_merge(target, source):
                for key, value in source.items():
                    if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                        deep_merge(target[key], value)
                    else:
                        target[key] = value

            deep_merge(config, updates)

            # Ensure consistent time format (without quotes)
            if 'task_scheduler' in config:
                task_scheduler = config['task_scheduler']
                for time_key in ['work_time_start', 'work_time_end']:
                    if time_key in task_scheduler:
                        # Ensure time format is consistent (HH:MM:SS without quotes)
                        time_value = str(task_scheduler[time_key])
                        if len(time_value) == 5:  # HH:MM format
                            time_value += ':00'  # Add seconds
                        task_scheduler[time_key] = time_value

            # Write updated configuration with custom formatting for time values
            yaml_content = yaml.safe_dump(config, allow_unicode=True, default_flow_style=False, sort_keys=False)

            # Post-process to ensure time values are properly quoted to prevent YAML parsing issues
            import re
            # Ensure time values are quoted to prevent YAML from parsing them as time objects
            yaml_content = re.sub(r"(\s+work_time_(?:start|end):\s*)(\d{2}:\d{2}(?::\d{2})?)\s*$", r"\1'\2'", yaml_content, flags=re.MULTILINE)

            with open(base_config_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)

            return {
                'success': True,
                'message': 'Base configuration updated successfully'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_email_config(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update _email_address.yaml configuration."""
        try:
            email_config_path = paths.email_address_file

            # Read current configuration
            if email_config_path.exists():
                with open(email_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
            else:
                config = {}

            # Update configuration with provided updates
            config.update(updates)

            # Write updated configuration
            with open(email_config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True, default_flow_style=False)

            return {
                'success': True,
                'message': 'Email configuration updated successfully'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_network_info(self) -> Dict[str, Any]:
        """Get network information including local IP address."""
        try:
            import socket

            # Get local IP address
            def get_local_ip():
                try:
                    # Connect to a remote address to determine local IP
                    with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                        s.connect(("*******", 80))
                        return s.getsockname()[0]
                except Exception:
                    try:
                        # Fallback method
                        hostname = socket.gethostname()
                        return socket.gethostbyname(hostname)
                    except Exception:
                        return "127.0.0.1"

            local_ip = get_local_ip()

            # Get admin port from current server (if available)
            admin_port = "8080"  # Default port

            return {
                'success': True,
                'data': {
                    'local_ip': local_ip,
                    'admin_port': admin_port,
                    'hostname': socket.gethostname()
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}


# Global instance for use in web handlers
web_manager = WebEnrollmentManager()


def handle_web_request(action: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Handle web requests for enrollment management."""
    if data is None:
        data = {}
    
    try:
        if action == 'get_enrollments':
            return web_manager.get_enrollments_data()
        elif action == 'get_cookies':
            return web_manager.get_cookie_owners_data()
        elif action == 'get_enrollment_details':
            ee_id = data.get('ee_id')
            if not ee_id:
                return {'success': False, 'error': 'Employee ID is required'}
            return web_manager.get_enrollment_details(ee_id)
        elif action == 'update_enrollment':
            ee_id = data.get('ee_id')
            updates = data.get('updates', {})
            if not ee_id:
                return {'success': False, 'error': 'Employee ID is required'}
            return web_manager.update_enrollment_data(ee_id, updates)
        elif action == 'delete_enrollment':
            ee_id = data.get('ee_id')
            if not ee_id:
                return {'success': False, 'error': 'Employee ID is required'}
            return web_manager.delete_enrollment_data(ee_id)
        elif action == 'add_enrollment':
            return web_manager.add_new_enrollment(data)

        elif action == 'get_stats':
            return web_manager.get_system_stats()
        elif action == 'get_config':
            return web_manager.get_configuration()
        elif action == 'get_network_info':
            return web_manager.get_network_info()

        elif action == 'update_access_control':
            updates = data.get('updates', {})
            return web_manager.update_access_control(updates)
        elif action == 'update_base_config':
            updates = data.get('updates', {})
            return web_manager.update_base_config(updates)
        elif action == 'update_base_config_partial':
            updates = data.get('updates', {})
            return web_manager.update_base_config_partial(updates)
        elif action == 'update_email_config':
            updates = data.get('updates', {})
            return web_manager.update_email_config(updates)
        else:
            return {'success': False, 'error': f'Unknown action: {action}'}
    except Exception as e:
        return {'success': False, 'error': str(e)}


if __name__ == '__main__':
    # Test the web manager
    print("Testing Web Enrollment Manager...")
    result = handle_web_request('get_stats')
    print(json.dumps(result, indent=2))
