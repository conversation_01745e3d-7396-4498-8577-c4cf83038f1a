<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON> Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #0583d2;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .cookie-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h2>🍪 Cookie Manager</h2>
    
    <div id="message"></div>
    
    <div class="controls">
        <button class="btn btn-success" onclick="refreshData()">🔄 Refresh</button>
    </div>
    
    <div id="loading" class="loading" style="display: none;">
        Loading cookies...
    </div>
    
    <div id="content">
        <table>
            <thead>
                <tr>
                    <th>Cookie (truncated)</th>
                    <th>Employee ID</th>
                    <th>Valid</th>
                    <th>Order</th>
                    <th>Total</th>
                    <th>Register Time</th>
                    <th>Created</th>
                    <th>Updated</th>
                </tr>
            </thead>
            <tbody id="cookie-tbody">
                <!-- Cookie data will be loaded dynamically -->
            </tbody>
        </table>
    </div>
    
    <script>
        function showMessage(text, type) {
            var messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '<div class="' + type + '">' + text + '</div>';
            setTimeout(function() {
                messageDiv.innerHTML = '';
            }, 3000);
        }
        
        function refreshData() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';

            // Call real API to get cookie data
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_cookies'
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';

                if (data.success) {
                    updateTable(data.data);
                    showMessage('✅ Cookie data refreshed successfully! (' + data.count + ' records)', 'success');
                } else {
                    showMessage('❌ Failed to load data: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function updateTable(cookies) {
            var tbody = document.querySelector('#content tbody');
            tbody.innerHTML = '';

            cookies.forEach(function(cookie) {
                var row = document.createElement('tr');
                var truncatedCookie = cookie.cookie.length > 50 ? cookie.cookie.substring(0, 50) + '...' : cookie.cookie;
                var validIcon = cookie.is_valid ? '✅ Yes' : '❌ No';

                row.innerHTML =
                    '<td class="cookie-cell" title="' + cookie.cookie + '">' + truncatedCookie + '</td>' +
                    '<td>' + (cookie.ee_id || '') + '</td>' +
                    '<td>' + validIcon + '</td>' +
                    '<td>' + (cookie.order || 0) + '</td>' +
                    '<td>' + (cookie.total || 0) + '</td>' +
                    '<td>' + (cookie.register_time || '') + '</td>' +
                    '<td>' + (cookie.created_time || '') + '</td>' +
                    '<td>' + (cookie.updated_time || '') + '</td>';
                tbody.appendChild(row);
            });
        }



        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });
    </script>
</body>
</html>
