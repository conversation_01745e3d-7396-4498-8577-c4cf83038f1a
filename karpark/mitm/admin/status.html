<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>System Status</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #0583d2;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #28a745;
        }
        
        .status-warning {
            background-color: #ffc107;
        }
        
        .last-updated {
            text-align: center;
            color: #6c757d;
            margin-top: 30px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h2>📊 System Status</h2>
    
    <div id="message"></div>
    
    <div class="controls">
        <button class="btn btn-success" onclick="refreshData()">🔄 Refresh</button>
    </div>
    
    <div id="loading" class="loading" style="display: none;">
        Loading system statistics...
    </div>
    
    <div id="content">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-enrollments">5</div>
                <div class="stat-label">
                    <span class="status-indicator status-online"></span>
                    Total Enrollments
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" id="active-enrollments">4</div>
                <div class="stat-label">
                    <span class="status-indicator status-online"></span>
                    Active Enrollments
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" id="withdrawn-enrollments">1</div>
                <div class="stat-label">
                    <span class="status-indicator status-warning"></span>
                    Withdrawn Enrollments
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" id="total-cookies">8</div>
                <div class="stat-label">
                    <span class="status-indicator status-online"></span>
                    Total Cookies
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" id="invalid-cookies">2</div>
                <div class="stat-label">
                    <span class="status-indicator status-warning"></span>
                    Invalid Cookies
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" id="valid-cookies">1</div>
                <div class="stat-label">
                    <span class="status-indicator status-online"></span>
                    Valid Cookies
                </div>
            </div>
        </div>
        
        <div class="last-updated" id="last-updated">
            Last updated: 2025-06-14 10:45:00
        </div>
    </div>
    
    <script>
        function showMessage(text, type) {
            var messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '<div class="' + type + '">' + text + '</div>';
            setTimeout(function() {
                messageDiv.innerHTML = '';
            }, 3000);
        }
        
        function refreshData() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';

            // Call real API to get system stats
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_stats'
                })
            })
            .then(response => {
                console.log('API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API response data:', data);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';

                if (data.success) {
                    console.log('API success, calling updateStats with:', data.data);
                    updateStats(data.data);
                    showMessage('✅ System statistics refreshed successfully!', 'success');
                } else {
                    console.log('API failed:', data.error);
                    showMessage('❌ Failed to load system stats: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function updateStats(stats) {
            console.log('updateStats called with:', stats);

            // Safely update enrollment stats
            var totalEnrollments = document.getElementById('total-enrollments');
            var activeEnrollments = document.getElementById('active-enrollments');
            var withdrawnEnrollments = document.getElementById('withdrawn-enrollments');

            if (totalEnrollments) totalEnrollments.textContent = stats.total_enrollments || 0;
            if (activeEnrollments) activeEnrollments.textContent = stats.active_enrollments || 0;
            if (withdrawnEnrollments) withdrawnEnrollments.textContent = stats.withdrawn_enrollments || 0;

            // Safely update cookie stats
            var totalCookies = document.getElementById('total-cookies');
            var invalidCookies = document.getElementById('invalid-cookies');
            var validCookies = document.getElementById('valid-cookies');

            console.log('Cookie elements found:', {
                totalCookies: !!totalCookies,
                invalidCookies: !!invalidCookies,
                validCookies: !!validCookies
            });

            console.log('Cookie values from API:', {
                total_cookies: stats.total_cookies,
                invalid_cookies: stats.invalid_cookies,
                valid_cookies: stats.valid_cookies
            });

            if (totalCookies) {
                totalCookies.textContent = stats.total_cookies || 0;
                console.log('Updated total cookies to:', stats.total_cookies || 0);
            }
            if (invalidCookies) {
                invalidCookies.textContent = stats.invalid_cookies || 0;
                console.log('Updated invalid cookies to:', stats.invalid_cookies || 0);
            }
            if (validCookies) {
                validCookies.textContent = stats.valid_cookies || 0;
                console.log('Updated valid cookies to:', stats.valid_cookies || 0);
            }



            // Update last updated time
            var lastUpdatedElement = document.getElementById('last-updated');
            if (lastUpdatedElement) {
                var now = new Date();
                var timestamp = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');
                lastUpdatedElement.textContent = 'Last updated: ' + timestamp;
            }
        }
        
        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });
    </script>
</body>
</html>
