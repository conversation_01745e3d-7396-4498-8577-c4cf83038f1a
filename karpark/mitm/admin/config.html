<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Configuration Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #0583d2;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Modal button hover effects */
        #editModal button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        /* Input focus effects */
        #editModal textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            color: #856404;
        }
        
        .config-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .config-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 16px;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        
        .config-item {
            margin: 12px 0;
            display: flex;
            align-items: center;
        }
        
        .config-label {
            font-weight: 500;
            color: #6c757d;
            min-width: 150px;
        }
        
        .config-value {
            margin-left: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h2>⚙️ Configuration Manager</h2>
    
    <div id="message"></div>
    
    <div class="warning">
        <strong>⚠️ Security Notice:</strong> Only critical configurations can be edited through the web interface.
        Other configurations require direct file editing for security reasons.
    </div>
    
    <div class="controls">
        <button class="btn btn-primary" onclick="refreshConfig()">🔄 Refresh Display</button>
        <button class="btn btn-success" onclick="editAccessControl()">✏️ Edit Access Control</button>
        <button class="btn btn-success" onclick="editTaskSchedulerConfig()">⏰ Edit Task Scheduler Config</button>
        <button class="btn btn-success" onclick="editEmailConfig()">📧 Edit Email Config</button>
    </div>
    
    <div class="config-section">
        <div class="config-title">🔐 Access Control</div>
        <div class="config-item">
            <span class="config-label">Admin Users:</span>
            <span class="config-value" id="admin-users">I072162</span>
        </div>
        <div class="config-item">
            <span class="config-label">Privileged Users:</span>
            <span class="config-value" id="privileged-users">I072162</span>
        </div>
        <div class="config-item">
            <span class="config-label">Authorized Users:</span>
            <span class="config-value" id="authorized-users">I072162</span>
        </div>
    </div>
    
    <div class="config-section">
        <div class="config-title">📁 File Paths</div>
        <div class="config-item">
            <span class="config-label">Access Control File:</span>
            <span class="config-value">karpark/config/access_control.yaml</span>
        </div>
        <div class="config-item">
            <span class="config-label">Database File:</span>
            <span class="config-value">karpark/data/enrollment.db</span>
        </div>
        <div class="config-item">
            <span class="config-label">Log Directory:</span>
            <span class="config-value">karpark/logs/</span>
        </div>
    </div>
    
    <div class="config-section">
        <div class="config-title">🌐 Network Settings</div>
        <div id="network-settings-content">
            <!-- Network settings will be loaded dynamically -->
        </div>
    </div>

    <div class="config-section">
        <div class="config-title">⏰ Task Scheduler Configuration</div>
        <div id="task-scheduler-config-content">
            <!-- Task scheduler configuration will be loaded dynamically -->
        </div>
    </div>

    <div class="config-section">
        <div class="config-title">📧 Email Configuration (_email_address.yaml)</div>
        <div id="email-config-content">
            <!-- Email configuration will be loaded dynamically -->
        </div>
    </div>

    <!-- Edit Access Control Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow-y: auto;">
        <div style="position: relative; margin: 10px auto; background: white; padding: 20px; border-radius: 8px; width: 98%; max-width: 800px; min-height: calc(100vh - 20px); box-sizing: border-box;">
            <h3 style="margin-top: 0; text-align: center; color: #333;">🔐 Edit Access Control</h3>
            <form id="editForm">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">👑 Admin Users (comma separated):</label>
                    <textarea id="edit-admin-users" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;" placeholder="I072162, I072163"></textarea>
                    <small style="color: #666;">Admin users have full access to all admin features. Supports: , ; ， ；</small>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⭐ Privileged Users (comma separated):</label>
                    <textarea id="edit-privileged-users" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;" placeholder="I072162, I072163"></textarea>
                    <small style="color: #666;">Privileged users can use magic plate and advanced features. Supports: , ; ， ；</small>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">✅ Authorized Users (comma separated):</label>
                    <textarea id="edit-authorized-users" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;" placeholder="I072162, I072163, I072164"></textarea>
                    <small style="color: #666;">Authorized users can access the parking registration system. Supports: , ; ， ；</small>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" onclick="closeEditModal()" style="margin-right: 15px; padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">❌ Cancel</button>
                    <button type="button" onclick="saveAccessControl()" style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">💾 Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Task Scheduler Configuration Modal -->
    <div id="editTaskSchedulerModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow-y: auto;">
        <div style="position: relative; margin: 10px auto; background: white; padding: 20px; border-radius: 8px; width: 98%; max-width: 900px; min-height: calc(100vh - 20px); box-sizing: border-box;">
            <h3 style="margin-top: 0; text-align: center; color: #333;">⏰ Edit Task Scheduler Configuration</h3>
            <form id="editTaskSchedulerForm">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🔢 Exit on Total:</label>
                        <input type="number" id="edit-exit-total" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="600" min="1">
                        <small style="color: #666;">Program exits when waiting list reaches this number</small>
                    </div>
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⏱️ Token Expiration (minutes):</label>
                        <input type="number" id="edit-token-expiration" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="29" min="1" max="60">
                        <small style="color: #666;">Token expiration time in minutes</small>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⏰ Work Hours:</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="time" id="edit-work-start" style="flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                        <span style="color: #666;">to</span>
                        <input type="time" id="edit-work-end" style="flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🌅 Work Hours Break (minutes):</label>
                        <input type="number" id="edit-work-hours-break" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="3" min="1">
                        <small style="color: #666;">Break duration during work hours</small>
                    </div>
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🌙 Off Hours Break (minutes):</label>
                        <input type="number" id="edit-off-hours-break" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="15" min="1">
                        <small style="color: #666;">Break duration during non-work hours</small>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⏳ Min Break (seconds):</label>
                        <input type="number" id="edit-min-break" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="1" min="1">
                        <small style="color: #666;">Minimum break time</small>
                    </div>
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⏰ Max Break (minutes):</label>
                        <input type="number" id="edit-max-break" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="25" min="1">
                        <small style="color: #666;">Maximum break time</small>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">⏸️ Pause Before Register (seconds):</label>
                        <input type="number" id="edit-pause-before-register" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="1" min="0">
                        <small style="color: #666;">Pause before registering new parking</small>
                    </div>
                    <div>
                        <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">📋 Wait Enrollment Break (seconds):</label>
                        <input type="number" id="edit-wait-enrollment-break" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box;" placeholder="30" min="1">
                        <small style="color: #666;">Time to wait before checking for new enrollments</small>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; font-weight: bold; color: #555;">
                        <input type="checkbox" id="edit-priority-sorting" style="margin-right: 8px; transform: scale(1.2);">
                        🎯 Enable Priority Sorting
                    </label>
                    <small style="color: #666; margin-left: 24px;">Sort tasks by user priority (admin > privileged > authorized)</small>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" onclick="closeEditTaskSchedulerModal()" style="margin-right: 15px; padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">❌ Cancel</button>
                    <button type="button" onclick="saveTaskSchedulerConfig()" style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">💾 Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Email Configuration Modal -->
    <div id="editEmailModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow-y: auto;">
        <div style="position: relative; margin: 10px auto; background: white; padding: 20px; border-radius: 8px; width: 98%; max-width: 800px; min-height: calc(100vh - 20px); box-sizing: border-box;">
            <h3 style="margin-top: 0; text-align: center; color: #333;">📧 Edit Email Configuration</h3>
            <form id="editEmailForm">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">👑 Admin Emails (comma separated):</label>
                    <textarea id="edit-admin-emails" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;" placeholder="<EMAIL>, <EMAIL>"></textarea>
                    <small style="color: #666;">Admin notification email addresses. Supports: , ; ， ；</small>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #555;">🌐 Global Emails (comma separated):</label>
                    <textarea id="edit-global-emails" style="width: 100%; padding: 12px; margin-top: 5px; height: 80px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; resize: vertical;" placeholder="<EMAIL>, <EMAIL>"></textarea>
                    <small style="color: #666;">Global notification email addresses. Supports: , ; ， ；</small>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" onclick="closeEditEmailModal()" style="margin-right: 15px; padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">❌ Cancel</button>
                    <button type="button" onclick="saveEmailConfig()" style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">💾 Save</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showMessage(text, type) {
            var messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '<div class="' + type + '">' + text + '</div>';
            setTimeout(function() {
                messageDiv.innerHTML = '';
            }, 3000);
        }
        
        function refreshConfig() {
            // Call real API to get configuration
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_config'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateConfigDisplay(data.data);
                    showMessage('✅ Display refreshed with current configuration!', 'success');
                } else {
                    showMessage('❌ Failed to load configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function updateConfigDisplay(config) {
            // Update access control display
            if (config.access_control) {
                document.getElementById('admin-users').textContent = config.access_control.admin_users.map(u => u.toUpperCase()).join(', ') || 'None';
                document.getElementById('privileged-users').textContent = config.access_control.privileged_users.map(u => u.toUpperCase()).join(', ') || 'None';
                document.getElementById('authorized-users').textContent = config.access_control.authorized_users.map(u => u.toUpperCase()).join(', ') || 'None';
            }

            // Update task scheduler configuration display
            if (config.base_config && config.base_config.task_scheduler) {
                updateTaskSchedulerConfigDisplay(config.base_config.task_scheduler);
            }

            // Update email configuration display
            if (config.email_config) {
                updateEmailConfigDisplay(config.email_config);
            }

            // Update network settings display
            updateNetworkSettingsDisplay();
        }

        function updateTaskSchedulerConfigDisplay(taskSchedulerConfig) {
            var container = document.getElementById('task-scheduler-config-content');
            container.innerHTML = '';

            // Display task scheduler configuration items
            var configItems = [
                { key: 'enable_priority_sorting', label: 'Priority Sorting' },
                { key: 'exit_on_total', label: 'Exit on Total' },
                { key: 'work_time_start', label: 'Work Start Time' },
                { key: 'work_time_end', label: 'Work End Time' },
                { key: 'work_hours_break', label: 'Work Hours Break (min)' },
                { key: 'off_hours_break', label: 'Off Hours Break (min)' },
                { key: 'token_expiration', label: 'Token Expiration (min)' },
                { key: 'min_break', label: 'Min Break (sec)' },
                { key: 'max_break', label: 'Max Break (min)' }
            ];

            configItems.forEach(function(item) {
                var value = taskSchedulerConfig[item.key];
                if (value !== undefined) {
                    var configItem = document.createElement('div');
                    configItem.className = 'config-item';
                    configItem.innerHTML =
                        '<span class="config-label">' + item.label + ':</span>' +
                        '<span class="config-value">' + value + '</span>';
                    container.appendChild(configItem);
                }
            });
        }

        function updateEmailConfigDisplay(emailConfig) {
            var container = document.getElementById('email-config-content');
            container.innerHTML = '';

            // Display admin emails
            if (emailConfig.admin_emails) {
                var configItem = document.createElement('div');
                configItem.className = 'config-item';
                configItem.innerHTML =
                    '<span class="config-label">Admin Emails:</span>' +
                    '<span class="config-value">' + emailConfig.admin_emails.join(', ') + '</span>';
                container.appendChild(configItem);
            }

            // Display global emails
            if (emailConfig.global_emails) {
                var configItem = document.createElement('div');
                configItem.className = 'config-item';
                configItem.innerHTML =
                    '<span class="config-label">Global Emails:</span>' +
                    '<span class="config-value">' + emailConfig.global_emails.join(', ') + '</span>';
                container.appendChild(configItem);
            }
        }

        function updateNetworkSettingsDisplay() {
            var container = document.getElementById('network-settings-content');
            container.innerHTML = '';

            // Get current machine IP
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_network_info'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    var networkInfo = data.data;

                    // Display network information
                    var networkItems = [
                        { label: 'Local IP Address', value: networkInfo.local_ip || 'Unknown' },
                        { label: 'Target Domain', value: 'parking.labs.sap.cn' },
                        { label: 'Admin Port', value: networkInfo.admin_port || '8080' },
                        { label: 'SSL Verification', value: 'Enabled' }
                    ];

                    networkItems.forEach(function(item) {
                        var configItem = document.createElement('div');
                        configItem.className = 'config-item';
                        configItem.innerHTML =
                            '<span class="config-label">' + item.label + ':</span>' +
                            '<span class="config-value">' + item.value + '</span>';
                        container.appendChild(configItem);
                    });
                } else {
                    // Fallback display
                    var fallbackItems = [
                        { label: 'Local IP Address', value: 'Unable to detect' },
                        { label: 'Target Domain', value: 'parking.labs.sap.cn' },
                        { label: 'Admin Port', value: '8080' },
                        { label: 'SSL Verification', value: 'Enabled' }
                    ];

                    fallbackItems.forEach(function(item) {
                        var configItem = document.createElement('div');
                        configItem.className = 'config-item';
                        configItem.innerHTML =
                            '<span class="config-label">' + item.label + ':</span>' +
                            '<span class="config-value">' + item.value + '</span>';
                        container.appendChild(configItem);
                    });
                }
            })
            .catch(error => {
                console.error('Failed to get network info:', error);
                // Fallback display
                container.innerHTML = '<div class="config-item"><span class="config-label">Network Info:</span><span class="config-value">Unable to load</span></div>';
            });
        }

        function getNestedValue(obj, path) {
            return path.split('.').reduce(function(current, key) {
                return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
        }



        function editAccessControl() {
            // Get current access control configuration
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_config'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.access_control) {
                    var config = data.data.access_control;
                    // Populate form fields with uppercase user IDs
                    document.getElementById('edit-admin-users').value = config.admin_users.map(u => u.toUpperCase()).join(', ');
                    document.getElementById('edit-privileged-users').value = config.privileged_users.map(u => u.toUpperCase()).join(', ');
                    document.getElementById('edit-authorized-users').value = config.authorized_users.map(u => u.toUpperCase()).join(', ');

                    document.getElementById('editModal').style.display = 'block';
                } else {
                    showMessage('❌ Failed to load access control configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        function parseUserList(input) {
            // Handle multiple separator types: comma, semicolon, Chinese comma, Chinese semicolon
            var separators = /[,，;；]/;
            return input.split(separators)
                       .map(u => u.trim().toUpperCase())  // Convert to uppercase and trim
                       .filter(u => u);  // Remove empty strings
        }

        function saveAccessControl() {
            var adminUsers = parseUserList(document.getElementById('edit-admin-users').value);
            var privilegedUsers = parseUserList(document.getElementById('edit-privileged-users').value);
            var authorizedUsers = parseUserList(document.getElementById('edit-authorized-users').value);

            var updates = {
                admin_users: adminUsers,
                privileged_users: privilegedUsers,
                authorized_users: authorizedUsers
            };

            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_access_control',
                    updates: updates
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeEditModal();
                    refreshConfig();
                    showMessage('✅ Access control configuration updated successfully!', 'success');
                } else {
                    showMessage('❌ Failed to update configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function editTaskSchedulerConfig() {
            // Get current task scheduler configuration
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_config'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.base_config && data.data.base_config.task_scheduler) {
                    var config = data.data.base_config.task_scheduler;

                    // Populate form fields
                    document.getElementById('edit-exit-total').value = config.exit_on_total || 600;
                    document.getElementById('edit-token-expiration').value = config.token_expiration || 29;
                    document.getElementById('edit-work-start').value = config.work_time_start || '08:30:00';
                    document.getElementById('edit-work-end').value = config.work_time_end || '18:30:00';
                    document.getElementById('edit-work-hours-break').value = config.work_hours_break || 3;
                    document.getElementById('edit-off-hours-break').value = config.off_hours_break || 15;
                    document.getElementById('edit-min-break').value = config.min_break || 1;
                    document.getElementById('edit-max-break').value = config.max_break || 25;
                    document.getElementById('edit-pause-before-register').value = config.pause_before_register || 1;
                    document.getElementById('edit-wait-enrollment-break').value = config.wait_enrollment_break || 30;
                    document.getElementById('edit-priority-sorting').checked = config.enable_priority_sorting || false;

                    document.getElementById('editTaskSchedulerModal').style.display = 'block';
                } else {
                    showMessage('❌ Failed to load task scheduler configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function closeEditTaskSchedulerModal() {
            document.getElementById('editTaskSchedulerModal').style.display = 'none';
        }

        function saveTaskSchedulerConfig() {
            // Collect form data
            var updates = {
                task_scheduler: {
                    exit_on_total: parseInt(document.getElementById('edit-exit-total').value) || 600,
                    token_expiration: parseInt(document.getElementById('edit-token-expiration').value) || 29,
                    work_time_start: document.getElementById('edit-work-start').value,
                    work_time_end: document.getElementById('edit-work-end').value,
                    work_hours_break: parseInt(document.getElementById('edit-work-hours-break').value) || 3,
                    off_hours_break: parseInt(document.getElementById('edit-off-hours-break').value) || 15,
                    min_break: parseInt(document.getElementById('edit-min-break').value) || 1,
                    max_break: parseInt(document.getElementById('edit-max-break').value) || 25,
                    pause_before_register: parseInt(document.getElementById('edit-pause-before-register').value) || 1,
                    wait_enrollment_break: parseInt(document.getElementById('edit-wait-enrollment-break').value) || 30,
                    enable_priority_sorting: document.getElementById('edit-priority-sorting').checked
                }
            };

            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_base_config_partial',
                    updates: updates
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeEditTaskSchedulerModal();
                    refreshConfig();
                    showMessage('✅ Task scheduler configuration updated successfully!', 'success');
                } else {
                    showMessage('❌ Failed to update configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function editEmailConfig() {
            // Get current email configuration
            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_config'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.email_config) {
                    var config = data.data.email_config;

                    // Populate form fields
                    document.getElementById('edit-admin-emails').value = (config.admin_emails || []).join(', ');
                    document.getElementById('edit-global-emails').value = (config.global_emails || []).join(', ');

                    document.getElementById('editEmailModal').style.display = 'block';
                } else {
                    showMessage('❌ Failed to load email configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        function closeEditEmailModal() {
            document.getElementById('editEmailModal').style.display = 'none';
        }

        function parseEmailList(input) {
            // Handle multiple separator types: comma, semicolon, Chinese comma, Chinese semicolon
            var separators = /[,，;；]/;
            return input.split(separators)
                       .map(e => e.trim().toLowerCase())  // Convert to lowercase and trim for emails
                       .filter(e => e);  // Remove empty strings
        }

        function saveEmailConfig() {
            // Collect form data
            var adminEmails = parseEmailList(document.getElementById('edit-admin-emails').value);
            var globalEmails = parseEmailList(document.getElementById('edit-global-emails').value);

            var updates = {
                admin_emails: adminEmails,
                global_emails: globalEmails
            };

            fetch('/mobile/parking/admin/api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_email_config',
                    updates: updates
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeEditEmailModal();
                    refreshConfig();
                    showMessage('✅ Email configuration updated successfully!', 'success');
                } else {
                    showMessage('❌ Failed to update configuration: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showMessage('❌ Network error: ' + error.message, 'error');
            });
        }

        // Load configuration on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshConfig();
        });
    </script>
</body>
</html>
