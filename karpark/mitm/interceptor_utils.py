"""
Utility functions and classes for the MITM interceptor.

This module contains helper functions, constants, and data models used by the main interceptor.
"""

import json
from pathlib import Path
from typing import Optional, NamedTuple, Dict, Any, List
from mitmproxy import http

from karpark.common.config import AccessControl
from karpark.registrar.db_operations import db_save_enrollment, save_cookie_owner, get_cookie_owner, \
    get_latest_enrollment
from karpark.utils import normalize_cookie

# Load access control configuration on module import
AccessControl.load_from_config()

# ============= Constants =============

# Content type mappings
CONTENT_TYPES = {
    "html": "text/html",
    "css": "text/css",
    "js": "application/javascript",
    "json": "application/json",
    "plain": "text/plain"
}

# Replacement file paths
REPLACEMENT_PATHS = {
    "authorized": "replacement/authorized",
    "interim": "replacement/interim",
    "unregistered": "replacement/unregistered"
}

# Target domain for interception
TARGET_DOMAIN = "parking.labs.sap.cn"

class RequestPaths:
    """API endpoint paths for the application"""
    regist = "/mobile/parking/regist"
    get_entry = "/mobile/parking/getEntry"
    style_css = "/mobile/parking/css/style.css"
    parking_js = "/mobile/parking/js/parking.js?v3"
    save_enrollment = "/mobile/parking/saveEnrollment"
    weui_css = "/mobile/parking/css/weui.min.css"
    weui_js = "/mobile/parking/js/weui.min.js"

# ============= Data Models =============

class RequestData(NamedTuple):
    """Data model for parking registration requests"""
    cookie: str
    ee_id: str
    ee_name: str
    plate_number: str
    registration_location: str
    use_magic_plate: bool
    is_withdrawn: bool
    emails: List[str] = []

class MissingFieldError(Exception):
    """Exception raised when required fields are missing from request data"""
    pass

# ============= Authorization Functions =============

def is_admin_user(user_id: str) -> bool:
    """Check if a user has admin access."""
    # Reload access control configuration to get latest changes
    AccessControl.load_from_config()
    return user_id in AccessControl.Users.admin_users

def is_privileged_user(user_id: str) -> bool:
    """Check if a user has privileged access."""
    # Reload access control configuration to get latest changes
    AccessControl.load_from_config()
    return user_id in AccessControl.Users.privileged_users

def is_authorized_user(user_id: str) -> bool:
    """Check if a user has authorized access."""
    # Reload access control configuration to get latest changes
    AccessControl.load_from_config()
    return user_id in AccessControl.Users.authorized_users

def determine_auth_type(flow: http.HTTPFlow) -> str:
    """Determine the authorization type based on flow information."""
    # Reload access control configuration to get latest changes
    AccessControl.load_from_config()

    cookie = flow.request.headers.get("Cookie", "")
    normalized_cookie = normalize_cookie(cookie)

    _, stored_ee_id, order, total, register_time = get_cookie_owner(normalized_cookie)

    if not stored_ee_id:
        return "interim"

    # First check authorization status
    is_authorized = is_authorized_user(stored_ee_id) if stored_ee_id else False

    # If user is not authorized, they should use unregistered interface regardless of registration status
    if not is_authorized:
        return "unregistered"

    # For authorized users, check if they are registered
    # If they have registration data, they use authorized interface
    # If they don't have registration data, they use unregistered interface for initial registration
    if order == 0 and total == 0 and not register_time:
        return "unregistered"

    return "authorized" # todo debug
    # return "unregistered" ##todo debug, important

def update_cookie_owner_info(flow: http.HTTPFlow) -> bool:
    """Update cookie owner information in the database."""
    cookie = normalize_cookie(flow.request.headers.get("Cookie", ""))
    ee_id = extract_ee_id_from_response(flow)

    if not ee_id:
        print(f"[WARN] Failed to extract ee_id from flow")
        return False

    # Extract order, total, and registerTime from response
    order = 0
    total = 0
    register_time = None

    try:
        response_data = parse_response_data(flow)
        if response_data:
            order = response_data.get("order", 0) or 0
            total = response_data.get("total", 0) or 0
            register_time = response_data.get("registerTime")
    except Exception as e:
        print(f"[WARN] Failed to extract queue info from response: {e}")

    try:
        save_cookie_owner(cookie, ee_id, order, total, register_time)
        print(f"[INFO] User auth info saved successfully with queue data: order={order}, total={total}")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to save user auth info: {e}")
        return False

# ============= Utility Functions =============

def get_replacement_path(auth_type: str, file_name: str) -> str:
    """Get the full path to a replacement file based on authorization type."""
    base_dir = Path(__file__).parent
    path = base_dir / REPLACEMENT_PATHS[auth_type] / file_name
    return str(path.resolve())

def create_error(code: str, message: str) -> Dict[str, str]:
    """Create a standardized error response object."""
    return {
        "errorCode": code,
        "errorMessage": message
    }

def replace_response_content(flow: http.HTTPFlow, file_path: str, content_type: str) -> bool:
    """Replace response content with content from a file."""
    print(f"[INFO] Replacing response content with file: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            flow.response.content = content.encode('utf-8')
            flow.response.headers["Content-Type"] = content_type
            flow.response.headers["Content-Length"] = str(len(flow.response.content))
        print(f"[INFO] Response content replaced successfully.")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to replace response content: {e}")
        return False

def update_flow_response(flow: http.HTTPFlow, response_data: Dict[str, Any]) -> None:
    """Update the flow response with new content."""
    try:
        json_str = json.dumps(response_data)
        encoded_content = json_str.encode()
        
        flow.response.content = encoded_content
        flow.response.headers["Content-Length"] = str(len(encoded_content))
        
        print(f"[DEBUG] Response updated successfully")
    except json.JSONDecodeError as e:
        print(f"[ERROR] Failed to encode response data: {e}")

# ============= Response Building Functions =============

def build_interim_response(response_data: dict) -> dict:
    """Build interim user response structure."""
    return response_data

def build_unauthorized_response(response_data: dict) -> dict:
    """Build unauthorized user response structure."""
    return response_data

def build_authorized_response(response_data: dict) -> dict:
    """Build authorized user response structure with registration status validation."""
    if not response_data.get("carNo") and not response_data.get("location"):
        response_data["error"] = create_error("381642", "You seemed NOT registered yet...")
    
    status = response_data.get("status", "").upper()
    if status not in ["WAITING", "PENDING", "ACCEPTED", "REJECTED"]:
        response_data["error"] = create_error("381627", "Invalid status value detected!")

    ee_id = response_data.get('eeId', '')
    try:
        latest_ee_id, db_ee_name, reg_number, reg_location, use_magic_plate, emails = get_latest_enrollment(ee_id)
        
        privileged = is_privileged_user(response_data.get('eeId', ''))
        admin = is_admin_user(response_data.get('eeId', ''))

        response = {
            "eeName": db_ee_name or response_data.get("eeName", ""),
            "eeId": latest_ee_id or response_data.get('eeId', ''),
            "registrationNumber": reg_number,
            "registrationLocation": reg_location,
            "useMagicPlate": privileged and use_magic_plate,
            "advancedFeatureOn": privileged,
            "isAdmin": admin,
            "isAuthorized": True,
            "emails": emails or []
        }
        return response
    except Exception as e:
        print(f"[ERROR] 数据库查询或响应构建失败: {e}")
        return response_data

# ============= Helper Functions =============

def extract_ee_id_from_response(flow: http.HTTPFlow) -> Optional[str]:
    """Extract eeId from response JSON data."""
    print(f"[DEBUG] Extracting eeId from response...")
    if not flow.response or not flow.response.content:
        print(f"[DEBUG] No response or empty content.")
        return None

    if flow.request.path not in [RequestPaths.get_entry, RequestPaths.save_enrollment]:
        print(f"[DEBUG] Request path not matched.")
        return None

    try:
        print(f"[DEBUG] Cookie: {flow.request.headers.get('Cookie', '')}")
        print(f"[DEBUG] Response content size: {len(flow.response.content)} bytes")

        content_type = flow.response.headers.get("Content-Type", "")
        if "application/json" not in content_type:
            print(f"[WARN] Unexpected Content-Type: {content_type}")
            return None

        try:
            response_data = json.loads(flow.response.content)
        except json.JSONDecodeError as e:
            print(f"[ERROR] Invalid JSON: {flow.response.content[:200]}...")
            print(f"{e.__str__()}")
            raise

        if "eeId" not in response_data:
            print(f"[WARN] eeId field missing in response JSON")

        ee_id = response_data.get("eeId")
        print(f"[DEBUG] Extracted eeId: {ee_id}")
        return ee_id
    except Exception as e:
        print(f"[ERROR] Error parsing response: {e}")
        return None

def parse_response_data(flow: http.HTTPFlow) -> dict:
    """Parse and clone response data."""
    original_response = json.loads(flow.response.content)
    return original_response.copy()

def save_enrollment_data(flow: http.HTTPFlow, data: RequestData) -> None:
    """Save enrollment data to database."""
    _ = flow # trick
    print(f"[DEBUG] Received data: {data._asdict()}")
    data = data._replace(registration_location=data.registration_location.replace(" ", ""))
    db_save_enrollment(
        data.cookie,
        data.ee_id,
        data.ee_name,
        data.plate_number,
        data.registration_location,
        int(data.use_magic_plate),
        int(data.is_withdrawn),
        data.emails
    )
    print(f"[INFO] Data saved: {data.cookie} | {data.ee_id} | {data.ee_name}")

def _parse_request_content(flow: http.HTTPFlow) -> dict:
    """Parse request content and return dictionary."""
    try:
        return json.loads(flow.request.content)
    except (json.JSONDecodeError, UnicodeDecodeError) as e:
        raise e

def _validate_required_fields(data: dict) -> None:
    """Validate required fields in the data for save enrollment request."""
    required_fields = ["eeId", "eeName", "registrationNumber", "registrationLocation"]
    if missing := [f for f in required_fields if f not in data]:
        raise MissingFieldError(f"Missing required fields: {', '.join(missing)}")

def _extract_request_data(flow: http.HTTPFlow, post_data: dict) -> RequestData:
    """Extract and encapsulate request data."""
    cookie = normalize_cookie(flow.request.headers.get("Cookie", ""))
    
    return RequestData(
        cookie=cookie,
        ee_id=post_data["eeId"],
        ee_name=post_data["eeName"],
        plate_number=post_data["registrationNumber"],
        registration_location=post_data["registrationLocation"],
        use_magic_plate=post_data.get("useMagicPlate", False),
        is_withdrawn=post_data.get("isWithdrawn", False),
        emails=post_data.get("emails", [])
    )

def _create_success_response_for_save(flow: http.HTTPFlow) -> None:
    """Create a success response."""
    flow.response = http.Response.make(
        200, 
        b"", 
        {"Content-Type": CONTENT_TYPES["plain"]}
    )

def _create_error_response_for_save(flow: http.HTTPFlow, status_code: int, message: str) -> None:
    """Create an error response."""
    flow.response = http.Response.make(
        status_code,
        message.encode("utf-8"),
        {"Content-Type": CONTENT_TYPES["plain"]}
    )
    print(f"[ERROR] Error occurred: {message}") 