#!/usr/bin/env python3
"""
MITM Proxy Interceptor for Karpark Application

This module implements a MITM proxy interceptor that handles requests and responses
for the Karpark parking application. It provides functionality for:
- Request/response interception and modification
- User authorization handling
- Static resource replacement
- Cookie and user data management
"""

import json
from pathlib import Path

from mitmproxy import http
from typing import Dict

from karpark.mitm.interceptor_utils import (
    TARGET_DOMAIN, CONTENT_TYPES, RequestPaths,
    MissingFieldError,
    determine_auth_type, update_cookie_owner_info,
    replace_response_content, update_flow_response,
    build_interim_response, build_authorized_response,
    parse_response_data, save_enrollment_data,
    _parse_request_content, _validate_required_fields, _extract_request_data,
    _create_success_response_for_save, _create_error_response_for_save,
    get_replacement_path, is_admin_user, normalize_cookie, get_cookie_owner
)
from karpark.mitm.admin.web_manager import handle_web_request

def request(flow: http.HTTPFlow) -> None:
    """
    Handle incoming requests.
    
    Args:
        flow: HTTP flow object
    """
    if flow.request.host != TARGET_DOMAIN:
        return

    print(f"[INFO] Handling request: {flow.request.url}")

    if flow.request.method == "POST" and flow.request.path == RequestPaths.save_enrollment:
        handle_save_enrollment_request(flow)
    elif flow.request.method == "POST" and flow.request.path == "/mobile/parking/saveMagic":
        handle_save_magic_request(flow)
    elif flow.request.method == "POST" and flow.request.path == "/mobile/parking/adminPage":
        handle_admin_page_request(flow)
    elif flow.request.method == "POST" and flow.request.path.startswith("/mobile/parking/admin/"):
        handle_admin_api_request(flow)

def response(flow: http.HTTPFlow) -> None:
    """
    Handle HTTP responses.
    
    Args:
        flow: HTTP flow object
    """
    if flow.request.host != TARGET_DOMAIN:
        return

    auth_type = determine_auth_type(flow)
    print(f"[DEBUG] Auth type determined: {auth_type}")

    if flow.request.method == "GET" and flow.response:
        _handle_get_request(flow, auth_type)
    elif flow.request.method == "POST" and flow.request.path == RequestPaths.save_enrollment and flow.response:
        _handle_post_request(flow)

def handle_save_enrollment_request(flow: http.HTTPFlow) -> None:
    """
    Handle parking registration entry requests.
    
    Args:
        flow: HTTP flow object
    """
    print(f"[INFO] Handling capture entry request...")
    try:
        post_data = _parse_request_content(flow)
        _validate_required_fields(post_data)
        request_data = _extract_request_data(flow, post_data)
        
        save_enrollment_data(flow, request_data)
        _create_success_response_for_save(flow)
        
    except (json.JSONDecodeError, UnicodeDecodeError) as e:
        print(f"[ERROR] Data format error: {e}")
        _create_error_response_for_save(flow, 400, f"Invalid data format: {e}")
    except MissingFieldError as e:
        print(f"[ERROR] Missing field error: {e}")
        _create_error_response_for_save(flow, 400, str(e))
    except Exception as e:
        print(f"[ERROR] Internal server error: {e}")
        _create_error_response_for_save(flow, 500, f"Internal Server Error: {e}")

def handle_save_magic_request(flow: http.HTTPFlow) -> None:
    """
    Handle magic parking registration entry requests.
    Transforms carNo using magic plate variants before processing.

    Args:
        flow: HTTP flow object
    """
    print(f"[INFO] Handling magic entry request...")
    try:
        # Import magic plate function
        from karpark.registrar.magic_plate import create_plate_variants
        import random

        post_data = _parse_request_content(flow)
        _validate_required_fields(post_data)

        # Get original carNo
        original_car_no = post_data.get('carNo', '')
        print(f"[DEBUG] Original carNo: {original_car_no}")

        # Generate magic plate variants
        if original_car_no:
            variants = create_plate_variants(original_car_no)
            if variants:
                # Select a random variant
                magic_car_no = random.choice(variants)
                print(f"[DEBUG] Selected magic carNo: {magic_car_no} (from {len(variants)} variants)")

                # Replace carNo in post_data
                post_data['carNo'] = magic_car_no
            else:
                print(f"[WARN] No variants generated for {original_car_no}, using original")

        # Continue with normal processing using modified data
        request_data = _extract_request_data(flow, post_data)
        save_enrollment_data(flow, request_data)
        _create_success_response_for_save(flow)

    except (json.JSONDecodeError, UnicodeDecodeError) as e:
        print(f"[ERROR] Data format error: {e}")
        _create_error_response_for_save(flow, 400, f"Invalid data format: {e}")
    except MissingFieldError as e:
        print(f"[ERROR] Missing field error: {e}")
        _create_error_response_for_save(flow, 400, str(e))
    except Exception as e:
        print(f"[ERROR] Internal server error: {e}")
        _create_error_response_for_save(flow, 500, f"Internal Server Error: {e}")

def handle_admin_page_request(flow: http.HTTPFlow) -> None:
    """
    Handle admin page requests.

    Args:
        flow: HTTP flow object
    """
    print(f"[INFO] Handling admin page request...")
    try:
        # Check if user is admin
        cookie = flow.request.headers.get("Cookie", "")

        normalized_cookie = normalize_cookie(cookie)
        _, stored_ee_id, _, _, _ = get_cookie_owner(normalized_cookie)

        if not stored_ee_id or not is_admin_user(stored_ee_id):
            print(f"[WARN] Access denied for admin page request. User: {stored_ee_id}")
            flow.response = http.Response.make(
                403,
                b"<html><body><h1>403 Access Denied</h1><p>Admin privileges required to access this page.</p><p><a href='/mobile/parking/regist'>Return to Main Page</a></p></body></html>",
                {"Content-Type": CONTENT_TYPES["html"]}
            )
            return

        print(f"[DEBUG] Admin access granted for user: {stored_ee_id}")

        # Serve admin page

        base_dir = Path(__file__).parent
        admin_page_path = str((base_dir / "admin" / "admin.html").resolve())
        print(f"[DEBUG] Admin page path: {admin_page_path}")

        if not replace_response_content(flow, admin_page_path, CONTENT_TYPES["html"]):
            print(f"[ERROR] Failed to serve admin page")
            flow.response = http.Response.make(
                404,
                b"<html><body><h1>404 Not Found</h1><p>Admin page not found.</p></body></html>",
                {"Content-Type": CONTENT_TYPES["html"]}
            )
        else:
            print(f"[SUCCESS] Successfully served admin page to admin user {stored_ee_id}")

    except Exception as e:
        print(f"[ERROR] Error handling admin page request: {e}")
        import traceback
        traceback.print_exc()
        flow.response = http.Response.make(
            500,
            b"<html><body><h1>500 Internal Server Error</h1><p>Server error occurred.</p></body></html>",
            {"Content-Type": CONTENT_TYPES["html"]}
        )

def handle_admin_api_request(flow: http.HTTPFlow) -> None:
    """
    Handle admin API requests.

    Args:
        flow: HTTP flow object
    """
    print(f"[INFO] Handling admin API request: {flow.request.path}")
    try:
        # Check if user is admin
        cookie = flow.request.headers.get("Cookie", "")
        print(f"[DEBUG] Request cookie: {cookie[:100]}..." if cookie else "[DEBUG] No cookie in request")

        normalized_cookie = normalize_cookie(cookie)
        print(f"[DEBUG] Normalized cookie: {normalized_cookie[:100]}..." if normalized_cookie else "[DEBUG] No normalized cookie")

        _, stored_ee_id, _, _, _ = get_cookie_owner(normalized_cookie)
        print(f"[DEBUG] Cookie lookup result: stored_ee_id={stored_ee_id}")

        if stored_ee_id:
            is_admin = is_admin_user(stored_ee_id)
            print(f"[DEBUG] Admin check for {stored_ee_id}: {is_admin}")

        if not stored_ee_id or not is_admin_user(stored_ee_id):
            print(f"[WARN] Access denied for admin API request. User: {stored_ee_id}")
            flow.response = http.Response.make(
                403,
                json.dumps({"success": False, "error": "Access denied. Admin privileges required."}).encode('utf-8'),
                {"Content-Type": CONTENT_TYPES["json"]}
            )
            return

        # Parse request data
        try:
            request_data = json.loads(flow.request.content.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            request_data = {}

        # Extract action from request data, not URL path
        action = request_data.get('action', '')
        if not action:
            flow.response = http.Response.make(
                400,
                json.dumps({"success": False, "error": "Missing action parameter"}).encode('utf-8'),
                {"Content-Type": CONTENT_TYPES["json"]}
            )
            return

        # Handle different admin actions

        result = handle_web_request(action, request_data)

        flow.response = http.Response.make(
            200,
            json.dumps(result).encode('utf-8'),
            {"Content-Type": CONTENT_TYPES["json"]}
        )

    except Exception as e:
        print(f"[ERROR] Error handling admin API request: {e}")
        flow.response = http.Response.make(
            500,
            json.dumps({"success": False, "error": f"Internal Server Error: {e}"}).encode('utf-8'),
            {"Content-Type": CONTENT_TYPES["json"]}
        )

def handle_admin_html_request(flow: http.HTTPFlow) -> None:
    """
    Handle admin HTML file requests.

    Args:
        flow: HTTP flow object
    """
    print(f"[INFO] Handling admin HTML request: {flow.request.path}")
    try:
        # Check if user is admin
        cookie = flow.request.headers.get("Cookie", "")

        normalized_cookie = normalize_cookie(cookie)
        _, stored_ee_id, _, _, _ = get_cookie_owner(normalized_cookie)
        if not stored_ee_id or not is_admin_user(stored_ee_id):
            print(f"[WARN] Access denied for admin HTML request. User: {stored_ee_id}")
            flow.response = http.Response.make(
                403,
                b"<html><body><h1>403 Access Denied</h1><p>Admin privileges required to access this page.</p><p><a href='/mobile/parking/regist'>Return to Main Page</a></p></body></html>",
                {"Content-Type": CONTENT_TYPES["html"]}
            )
            return

        # Extract filename from path
        filename = flow.request.path.split('/')[-1]
        print(f"[DEBUG] Extracted filename: {filename}")

        # Serve the HTML file
        base_dir = Path(__file__).parent
        html_file_path = str((base_dir / "admin" / filename).resolve())
        print(f"[DEBUG] Resolved file path: {html_file_path}")

        # Check if file exists
        if not Path(html_file_path).exists():
            print(f"[ERROR] File not found: {html_file_path}")
            flow.response = http.Response.make(
                404,
                b"<html><body><h1>404 Not Found</h1><p>Admin page not found.</p></body></html>",
                {"Content-Type": CONTENT_TYPES["html"]}
            )
            return

        print(f"[DEBUG] Admin access granted for user: {stored_ee_id}")
        print(f"[DEBUG] Attempting to serve file: {html_file_path}")

        if not replace_response_content(flow, html_file_path, CONTENT_TYPES["html"]):
            print(f"[ERROR] Failed to replace response content")
            flow.response = http.Response.make(
                500,
                b"<html><body><h1>500 Internal Server Error</h1><p>Failed to load page content.</p></body></html>",
                {"Content-Type": CONTENT_TYPES["html"]}
            )
        else:
            print(f"[SUCCESS] Successfully served {filename} to admin user {stored_ee_id}")

    except Exception as e:
        print(f"[ERROR] Error handling admin HTML request: {e}")
        import traceback
        traceback.print_exc()
        flow.response = http.Response.make(
            500,
            b"<html><body><h1>500 Internal Server Error</h1><p>Server error occurred.</p></body></html>",
            {"Content-Type": CONTENT_TYPES["html"]}
        )

def _handle_get_request(flow: http.HTTPFlow, auth_type: str) -> None:
    """
    Handle GET requests.
    
    Args:
        flow: HTTP flow object
        auth_type: Authorization type
    """
    # If user is unauthorized, keep original resources
    if auth_type == "unauthorized":
        return

    static_resource_mapping = {
        RequestPaths.regist: {"file_name": "register.html", "content_type": "html"},
        RequestPaths.parking_js: {"file_name": "js/parking.js", "content_type": "js"},
        RequestPaths.style_css: {"file_name": "css/style.css", "content_type": "css"},
        RequestPaths.weui_css: {"file_name": "css/weui.min.css", "content_type": "css"},
        RequestPaths.weui_js: {"file_name": "js/weui.min.js", "content_type": "js"}
    }
    
    if flow.request.path in static_resource_mapping:
        _handle_static_resource(flow, auth_type, static_resource_mapping[flow.request.path])
    elif flow.request.path == RequestPaths.get_entry:
        _handle_get_entry_request(flow, auth_type)
    elif flow.request.path == "/mobile/parking/adminPage":
        handle_admin_page_request(flow)
    elif flow.request.path.startswith("/mobile/parking/admin/") and flow.request.path.endswith(".html"):
        handle_admin_html_request(flow)

def _handle_static_resource(flow: http.HTTPFlow, auth_type: str, resource_info: Dict[str, str]) -> None:
    """
    Handle static resource requests.
    
    Args:
        flow: HTTP flow object
        auth_type: Authorization type
        resource_info: Resource information dictionary
    """
    file_path = get_replacement_path(auth_type, resource_info["file_name"])
    file_type = resource_info["content_type"].upper()
    print(f"[INFO] Replacing {file_type} resource ({auth_type}): {file_path}")
    
    if not replace_response_content(flow, file_path, CONTENT_TYPES[resource_info["content_type"]]):
        print(f"[ERROR] Failed to replace {file_type} resource.")

def _handle_get_entry_request(flow: http.HTTPFlow, auth_type: str) -> None:
    """
    Handle get entry requests.
    
    Args:
        flow: HTTP flow object
        auth_type: Authorization type
    """
    response_data = parse_response_data(flow)
    update_cookie_owner_info(flow)
    
    if auth_type == "interim":
        response_data = build_interim_response(response_data)
    elif auth_type == "authorized":
        response_data = build_authorized_response(response_data)
    elif auth_type == "unregistered":
        # Keep original response data for unregistered users (like unauthorized)
        pass
    elif auth_type == "unauthorized":
        # Keep original response data for unauthorized users
        pass

    update_flow_response(flow, response_data)

def _handle_post_request(flow: http.HTTPFlow) -> None:
    """
    Handle POST requests.
    
    Args:
        flow: HTTP flow object
    """
    try:
        post_data = json.loads(flow.request.content)
        flow.response = http.Response.make(
            200,
            json.dumps(post_data).encode('utf-8'),
            {"Content-Type": CONTENT_TYPES["json"]}
        )
        print(f"[DEBUG] saveEnrollment response handled successfully.")
    except Exception as e:
        print(f"[ERROR] Error handling saveEnrollment response: {e}")
