#!/usr/bin/env python3
"""
Karpark MITM 代理启动脚本

优化版本 - 专注于核心功能：
1. 基于配置文件的Python环境管理
2. 启动mitmdump代理
3. 支持多种运行模式
4. 简化的配置管理
"""

import sys
import platform
import subprocess
import argparse
import os
import time
from pathlib import Path
from datetime import datetime

from karpark.common.paths import paths
from karpark.common.config import MitmproxyConfig
from karpark.mitm.process_manager import process_manager
from karpark.mitm.email_monitor import EmailMonitor

class MitmdumpLauncher:
    """MITM代理启动器"""

    def __init__(self):
        self.project_root = paths.project_root
        self.intercept_script = paths.mitm_dir / "intercept.py"
        self.email_monitor = None

    def set_system_python(self, use_system=True):
        """临时设置系统Python使用偏好

        Args:
            use_system: 是否使用系统Python
        """
        MitmproxyConfig.Python.use_system_python = use_system
        if use_system:
            print("🐍 Temporarily enabled system Python usage")

    def find_python_executable(self):
        """智能查找合适的Python解释器"""
        # Check configuration for system Python preference
        if MitmproxyConfig.Python.use_system_python:
            print("🐍 Using system Python (configured in _mitmproxy.yaml)")
            return sys.executable

        # 当前是否在虚拟环境中？
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
        if in_venv:
            print(f"🐍 Using current virtual environment: {sys.executable}")
            return sys.executable

        # 查找项目虚拟环境
        venv_paths = [
            self.project_root / ".venv" / "Scripts" / "python.exe",  # Windows
            self.project_root / ".venv" / "bin" / "python",          # Linux/Mac
        ]

        for venv_python in venv_paths:
            if venv_python.exists():
                print(f"🐍 Found project virtual environment: {venv_python}")
                return str(venv_python)

        # 兜底方案
        print(f"⚠️  Using current Python: {sys.executable}")
        return sys.executable

    def find_mitmdump_executable(self, python_exe):
        """查找mitmdump可执行文件"""
        python_dir = Path(python_exe).parent

        if platform.system() == "Windows":
            mitmdump_exe = python_dir / "mitmdump.exe"
        else:
            mitmdump_exe = python_dir / "mitmdump"

        if mitmdump_exe.exists():
            return str(mitmdump_exe), True  # 使用可执行文件
        else:
            return python_exe, False  # 使用Python模块

    def check_dependencies(self, python_exe):
        """检查依赖是否安装"""
        try:
            result = subprocess.run([
                python_exe, "-c",
                "import mitmproxy; import coloredlogs; print('OK')"
            ], capture_output=True, text=True, timeout=5, errors='replace')

            return result.returncode == 0
        except Exception:
            return False

    def build_command(self, executable, use_exe, test_mode=False):
        """构建mitmdump命令"""
        # Get port from configuration
        port = MitmproxyConfig.Server.port
        mode_arg = f"regular@{port}"

        # Build base command
        if use_exe:
            # 使用mitmdump可执行文件
            if test_mode:
                command = [executable, "--mode", mode_arg]
            else:
                command = [executable, "-s", str(self.intercept_script), "--mode", mode_arg]
        else:
            # 使用Python模块
            if test_mode:
                command = [executable, "-m", "mitmproxy.tools.dump", "--mode", mode_arg]
            else:
                command = [executable, "-m", "mitmproxy.tools.dump", "-s", str(self.intercept_script), "--mode", mode_arg]

        # Add password authentication if configured
        if MitmproxyConfig.Server.password:
            command.extend(["--proxyauth", MitmproxyConfig.Server.password])

        return command

    def run(self, wait=True, test_mode=False):
        """启动mitmdump代理"""
        print("🚀 Starting Karpark MITM Proxy...")

        # 1. 查找Python解释器
        python_exe = self.find_python_executable()

        # 2. 检查依赖
        if not self.check_dependencies(python_exe):
            print("❌ Required dependencies not found!")
            print(f"💡 Install with: {python_exe} -m pip install mitmproxy coloredlogs")
            return False

        # 3. 查找mitmdump可执行文件
        executable, use_exe = self.find_mitmdump_executable(python_exe)

        # 4. 构建命令
        command = self.build_command(executable, use_exe, test_mode)

        # 5. 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.project_root)

        # 6. 显示信息
        if test_mode:
            print("🧪 Running in test mode (no script loaded)")
        print(f"🌐 Port: {MitmproxyConfig.Server.port}")
        if MitmproxyConfig.Server.password:
            print("🔐 Password authentication enabled")
        print(f"📝 Command: {' '.join(command)}")

        # 7. 启动进程
        try:
            if wait:
                return self._run_with_output(command, env)
            else:
                return self._run_background(command, env)
        except FileNotFoundError as e:
            print(f"❌ Executable not found: {e}")
            return False

    def _run_with_output(self, command, env):
        """等待模式：显示实时输出"""
        process = subprocess.Popen(
            command, env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        print("✅ mitmdump started. Press Ctrl+C to stop.")
        try:
            # 实时显示输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())

            # 检查退出码
            return_code = process.poll()
            if return_code != 0:
                print(f"❌ mitmdump exited with code: {return_code}")
                return False

        except KeyboardInterrupt:
            print("\n🛑 Stopping mitmdump...")
            process.terminate()
            process.wait()

        return True

    def _run_background(self, command, env):
        """后台模式：启动后立即返回"""
        process = subprocess.Popen(command, env=env)
        print(f"✅ mitmdump started in background (PID: {process.pid})")

        # Save process information for management
        process_manager.save_pid(process.pid, command, datetime.now())

        # 检查进程是否立即退出
        time.sleep(2)
        if process.poll() is not None:
            print(f"⚠️  Process exited with code: {process.poll()}")
            process_manager.cleanup_pid_files()
            return False

        return True

    def restart(self) -> bool:
        """Restart the MITM proxy process.

        Returns:
            True if restart was successful, False otherwise
        """
        print("🔄 Restarting MITM proxy...")
        return process_manager.restart_process(self)

    def restart_with_cooldown_check(self, email_data=None) -> bool:
        """Restart with cooldown check and email reply if needed.

        Args:
            email_data: Email data for reply if restart is in cooldown

        Returns:
            True if restart was successful or cooldown reply sent, False otherwise
        """
        # Check if restart is allowed
        is_allowed, last_restart_time, remaining_minutes = process_manager.is_restart_allowed()

        if is_allowed:
            # Restart is allowed
            print("🔄 Restarting MITM proxy...")
            return process_manager.restart_process(self)
        else:
            # Restart is in cooldown
            print(f"⏳ Restart cooldown active. {remaining_minutes} minutes remaining.")

            # Send reply email if email data is provided
            if email_data and self.email_monitor:
                try:
                    reply_sent = self.email_monitor.send_cooldown_reply(
                        email_data, last_restart_time, remaining_minutes
                    )
                    if reply_sent:
                        print("📧 Cooldown notification email sent")
                    else:
                        print("❌ Failed to send cooldown notification email")
                except Exception as e:
                    print(f"❌ Error sending cooldown notification: {e}")

            return True  # Return True as the request was processed (even if not restarted)

    def stop(self) -> bool:
        """Stop the MITM proxy process.

        Returns:
            True if stop was successful, False otherwise
        """
        print("🛑 Stopping MITM proxy...")
        success = process_manager.stop_process()

        # Stop email monitoring if running
        if self.email_monitor:
            self.email_monitor.stop()
            self.email_monitor = None

        return success

    def status(self) -> dict:
        """Get MITM proxy process status.

        Returns:
            Process status information dict
        """
        info = process_manager.get_process_info()
        if info:
            return {
                'status': 'running',
                'pid': info['pid'],
                'port': info['port'],
                'cpu_percent': info['cpu_percent'],
                'memory_mb': round(info['memory_info']['rss'] / 1024 / 1024, 2),
                'start_time': info['create_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'email_monitor': self.email_monitor.is_running() if self.email_monitor else False
            }
        else:
            return {
                'status': 'stopped',
                'pid': None,
                'email_monitor': False
            }

    def start_email_monitoring(self) -> bool:
        """Start email monitoring for restart commands.

        Returns:
            True if email monitoring started successfully, False otherwise
        """
        if self.email_monitor and self.email_monitor.is_running():
            print("📧 Email monitoring is already running")
            return True

        def restart_callback(email_data=None):
            """Callback function for email-triggered restart."""
            return self.restart_with_cooldown_check(email_data)

        self.email_monitor = EmailMonitor(restart_callback=restart_callback)
        success = self.email_monitor.start()

        if success:
            print("📧 Email monitoring started for restart commands")
        else:
            print("❌ Failed to start email monitoring")

        return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='🚀 Karpark MITM Proxy Launcher',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_mitm_server.py                    # 启动代理（等待模式）
  python launch_mitm_server.py --no-wait          # 后台启动
  python launch_mitm_server.py --system-python    # 临时使用系统Python
  python launch_mitm_server.py --test-mode        # 测试模式（不加载脚本）
  python launch_mitm_server.py --restart          # 重启代理
  python launch_mitm_server.py --stop             # 停止代理
  python launch_mitm_server.py --status           # 查看状态
  python launch_mitm_server.py --email-monitor    # 启动邮件监控
        """
    )

    parser.add_argument('--no-wait', action='store_true',
                       help='后台运行，立即退出')
    parser.add_argument('--system-python', action='store_true',
                       help='使用系统Python（临时覆盖配置文件设置）')
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式（不加载拦截脚本）')
    parser.add_argument('--install-deps', action='store_true',
                       help='安装必要依赖')
    parser.add_argument('--restart', action='store_true',
                       help='重启MITM代理')
    parser.add_argument('--stop', action='store_true',
                       help='停止MITM代理')
    parser.add_argument('--status', action='store_true',
                       help='查看MITM代理状态')
    parser.add_argument('--email-monitor', action='store_true',
                       help='启动邮件监控（用于远程重启）')

    args = parser.parse_args()
    launcher = MitmdumpLauncher()

    # 临时覆盖系统Python设置
    if args.system_python:
        launcher.set_system_python(True)

    # 安装依赖
    if args.install_deps:
        python_exe = launcher.find_python_executable()
        print(f"📦 Installing dependencies using {python_exe}...")
        result = subprocess.run([python_exe, "-m", "pip", "install", "mitmproxy", "coloredlogs", "psutil"])
        if result.returncode == 0:
            print("✅ Dependencies installed!")
        else:
            print("❌ Failed to install dependencies!")
            sys.exit(1)
        return

    # 重启代理
    if args.restart:
        success = launcher.restart()
        sys.exit(0 if success else 1)

    # 停止代理
    if args.stop:
        success = launcher.stop()
        sys.exit(0 if success else 1)

    # 查看状态
    if args.status:
        status_info = launcher.status()
        print("📊 MITM Proxy Status:")
        print(f"   Status: {status_info['status']}")
        if status_info['status'] == 'running':
            print(f"   PID: {status_info['pid']}")
            print(f"   Port: {status_info['port']}")
            print(f"   CPU: {status_info['cpu_percent']}%")
            print(f"   Memory: {status_info['memory_mb']} MB")
            print(f"   Started: {status_info['start_time']}")
            print(f"   Email Monitor: {'Running' if status_info['email_monitor'] else 'Stopped'}")
        return

    # 启动邮件监控
    if args.email_monitor:
        success = launcher.start_email_monitoring()
        if success:
            print("📧 Email monitoring is running. Press Ctrl+C to stop.")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Stopping email monitoring...")
                launcher.email_monitor.stop()
        sys.exit(0 if success else 1)

    # 启动代理
    success = launcher.run(
        wait=not args.no_wait,
        test_mode=args.test_mode
    )

    # 如果是后台模式且启动成功，自动启动邮件监控
    if success and args.no_wait and MitmproxyConfig.EmailMonitor.enabled:
        launcher.start_email_monitoring()

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
