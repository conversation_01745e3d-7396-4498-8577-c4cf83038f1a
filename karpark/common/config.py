#!/usr/bin/env python3
# coding: utf-8
"""
Configuration management module for the parking registration system.

This module provides classes and utilities for managing
various configurations used throughout the application.

Key features:
- Configuration loading from YAML files
- Caching and reloading of configuration files
- Static access to configuration settings
- Support for environment variables and defaults
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional

from karpark.common.paths import paths
from karpark.utils import TimeInterval, TimeUnit


def load_yaml_file(file_path: Path) -> dict:
    """Load a YAML file
    
    Args:
        file_path: Path to the YAML file
        
    Returns:
        dict: Configuration dictionary
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


class ConfigFile:
    """Represents a configuration file with its metadata."""
    
    def __init__(self, name: str, path: Path):
        self.name = name
        self.path = path
        self.last_modified: Optional[float] = None
        
    def check_modified(self) -> bool:
        """Check if the file has been modified since last check.
        
        Returns:
            bool: True if file has been modified, False otherwise
        """
        try:
            current_mtime = os.path.getmtime(self.path)
            if self.last_modified is None or current_mtime > self.last_modified:
                self.last_modified = current_mtime
                return True
            return False
        except OSError as e:
            print(f"Error checking file {self.path}: {e}")
            return False

# Service URL Configuration
class ServiceURL:
    """Service URL configuration and utilities."""
    domain: str = ""
    base_path: str = ""
    
    @classmethod
    def full_url(cls, path: str) -> str:
        """Generate full URL for a given path."""
        return f"https://{cls.domain}{cls.base_path}{path}"
    
    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load service URL configuration from config dict."""
        service_config = config['service_url']['service']
        cls.domain = service_config['domain']
        cls.base_path = service_config['base_path']

# Parker Configuration
class ParkerConfig:
    """Configuration settings for the parking system."""

    class ApiEndpoints:
        """API Endpoints for parking service"""
        base_url: str = ""
        entry_url: str = ""
        post_url: str = ""

    class ServiceThresholds:
        """Time settings for parking service"""
        moving_benchmark: int = 20
        new_registration_grace_hours: int = 24
        request_timeout: int = 0

    # Status Configuration
    valid_status = frozenset({"WAITING", "PENDING", "ACCEPTED", "REJECTED"})

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load parker configuration from config dict."""
        # Update API endpoints
        cls.ApiEndpoints.base_url = ServiceURL.full_url('')
        cls.ApiEndpoints.entry_url = ServiceURL.full_url(config['service_url']['service']['urls']['entry'])
        cls.ApiEndpoints.post_url = ServiceURL.full_url(config['service_url']['service']['urls']['post'])

        # Update service thresholds
        threshold_config = config['service_threshold']
        cls.ServiceThresholds.moving_benchmark = threshold_config['move_benchmark']
        cls.ServiceThresholds.new_registration_grace_hours = threshold_config['registration_valid_hours']
        cls.ServiceThresholds.request_timeout = threshold_config['request_timeout']

# Notification Configuration
class NotificationConfig:
    """Configuration settings for the notification system."""

    class Threshold:
        """Notification threshold settings."""
        notification_cooldown: int = 0

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load notification configuration from config dict."""
        notification_config = config['notification_threshold']
        cls.Threshold.notification_cooldown = notification_config['notification_cooldown']

# Task Scheduler Configuration
class TaskSchedulerConfig:
    """Configuration settings for the task scheduler system."""

    class Control:
        """Control settings for task scheduler."""
        exit_on_total: int = 0
        enable_priority_sorting: bool = True

    class Intervals:
        """Time interval settings."""
        wait_enrollment: TimeInterval
        poll_interval_work: TimeInterval
        poll_interval_off: TimeInterval
        poll_interval_minimum: TimeInterval
        poll_interval_maximum: TimeInterval
        poll_interval_after_reopen: TimeInterval = TimeInterval(1, TimeUnit.SECOND)
        pause_before_register: TimeInterval = TimeInterval(1, TimeUnit.SECOND)

    class WorkTime:
        """Work time settings."""
        work_time_start: str = "08:30:00"
        work_time_end: str = "18:30:00"

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load task scheduler configuration from config dict."""
        scheduler_config = config['task_scheduler']

        # Load control settings
        cls.Control.exit_on_total = scheduler_config['exit_on_total']
        cls.Control.enable_priority_sorting = scheduler_config.get('enable_priority_sorting', True)

        # Load time interval settings
        cls.Intervals.wait_enrollment = TimeInterval(scheduler_config['wait_enrollment_break'], TimeUnit.SECOND)
        cls.Intervals.poll_interval_work = TimeInterval(scheduler_config['work_hours_break'], TimeUnit.MINUTE)
        cls.Intervals.poll_interval_off = TimeInterval(scheduler_config['off_hours_break'], TimeUnit.MINUTE)
        cls.Intervals.poll_interval_minimum = TimeInterval(scheduler_config['min_break'], TimeUnit.SECOND)
        cls.Intervals.poll_interval_maximum = TimeInterval(scheduler_config['max_break'], TimeUnit.MINUTE)
        cls.Control.pause_before_register = TimeInterval(scheduler_config['pause_before_register'], TimeUnit.SECOND)

        # Load work time settings
        cls.WorkTime.work_time_start = scheduler_config['work_time_start']
        cls.WorkTime.work_time_end = scheduler_config['work_time_end']


# Magic Plate Configuration
class MagicPlateConfig:
    """Configuration for magic plate system."""

    class Mapping:
        """Character mapping configuration."""
        # Note: char_mapping is now hardcoded in magic_plate.py and no longer configurable
        magic_invisible: str = "‎"

    class Priority:
        """Priority configuration for magic plate variants."""
        priority_config: List[Dict[str, Any]] = []

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load magic plate configuration from config dict."""
        magic_plate_config = config['magic_plate']
        # char_mapping is now hardcoded in magic_plate.py, so we don't load it from config
        cls.Mapping.magic_invisible = magic_plate_config['magic_invisible']
        cls.Priority.priority_config = magic_plate_config['priority_config']

# Mail Configuration
class MailConfig:
    """Configuration settings for the mail system."""
    
    class Settings:
        """Mail server and account settings."""
        smtp_host: str = ""
        smtp_port: int = 0
        pop3_host: str = ""
        pop3_port: int = 0
        account: str = ""
        token: str = ""
        from_address: str = ""
        from_name: str = ""
    
    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load mail configuration from config dict."""
        mail_config = config['mail_account']
        cls.Settings.smtp_host = mail_config['smtp_host'].strip()
        cls.Settings.smtp_port = mail_config['smtp_port']
        cls.Settings.pop3_host = mail_config['pop3_host'].strip()
        cls.Settings.pop3_port = mail_config['pop3_port']
        cls.Settings.account = mail_config['account'].strip()
        cls.Settings.token = mail_config['token'].strip()
        cls.Settings.from_address = mail_config['from'].strip()
        cls.Settings.from_name = mail_config['from_name'].strip()

# Logger Configuration
class LoggerConfig:
    """Configuration settings for the colorlog system."""

    class Settings:
        """Logger settings."""
        logger_name: str = "karpark"
        level: str = "DEBUG"
        console_format: str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        file_format: str = "{time:YYYY-MM-DD HH:mm:ss.SSS} {file:>18} line:{line:<4} {level:>8} {message}"
        max_bytes: int = ********  # 10MB
        backup_count: int = 2
        # Loguru specific settings
        colorize: bool = True
        backtrace: bool = True
        diagnose: bool = True

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load logger configuration from config dict."""
        logger_config = config['logger']
        cls.Settings.logger_name = logger_config['logger_name']
        cls.Settings.level = logger_config['level']
        cls.Settings.console_format = logger_config['console_format']
        cls.Settings.file_format = logger_config['file_format']
        cls.Settings.max_bytes = logger_config['max_bytes']
        cls.Settings.backup_count = logger_config['backup_count']
        # Load Loguru specific settings with defaults
        cls.Settings.colorize = logger_config.get('colorize', True)
        cls.Settings.backtrace = logger_config.get('backtrace', True)
        cls.Settings.diagnose = logger_config.get('diagnose', True)


# Mitmproxy Configuration
class MitmproxyConfig:
    """Configuration for mitmproxy server."""

    class Python:
        """Python environment settings."""
        use_system_python: bool = False

    class Server:
        """Server configuration settings."""
        port: int = 8080
        password: Optional[str] = None

    class EmailMonitor:
        """Email monitoring settings for remote restart functionality."""
        enabled: bool = True
        check_interval: int = 5  # minutes

        class RestartCommand:
            """Restart command configuration."""
            password: str = "karpark_restart_2024"
            cooldown_minutes: int = 10

            class ReplySettings:
                """Email reply settings for cooldown notifications."""
                enabled: bool = True
                subject_template: str = "Re: {original_subject} - Restart Cooldown Active"
                message_template: str = """Hello,

Your restart request has been received, but the MITM proxy was recently restarted.
To prevent system instability, restarts are limited to once every {cooldown_minutes} minutes.

Last restart: {last_restart_time}
Next available restart: {next_available_time}

Please try again after {remaining_minutes} minutes.

Best regards,
Karpark MITM Monitor"""

        class Retention:
            """Email retention settings."""
            days: int = 7

    @classmethod
    def load_from_config(cls, config: dict) -> None:
        """Load mitmproxy configuration from config dict."""
        try:
            mitm_config = config.get('mitmproxy', {})

            # Update Python settings
            python_config = mitm_config.get('python', {})
            cls.Python.use_system_python = python_config.get('use_system_python', False)

            # Update Server settings
            server_config = mitm_config.get('server', {})
            cls.Server.port = server_config.get('port', 8080)
            cls.Server.password = server_config.get('password')

            # Update Email Monitor settings
            email_monitor_config = mitm_config.get('email_monitor', {})
            cls.EmailMonitor.enabled = email_monitor_config.get('enabled', True)
            cls.EmailMonitor.check_interval = email_monitor_config.get('check_interval', 5)

            # Update Restart Command settings
            restart_config = email_monitor_config.get('restart_command', {})
            cls.EmailMonitor.RestartCommand.password = restart_config.get('password', 'karpark_restart_2024')
            cls.EmailMonitor.RestartCommand.cooldown_minutes = restart_config.get('cooldown_minutes', 10)

            # Update Reply Settings
            reply_config = restart_config.get('reply_settings', {})
            cls.EmailMonitor.RestartCommand.ReplySettings.enabled = reply_config.get('enabled', True)
            cls.EmailMonitor.RestartCommand.ReplySettings.subject_template = reply_config.get('subject_template',
                'Re: {original_subject} - Restart Cooldown Active')
            cls.EmailMonitor.RestartCommand.ReplySettings.message_template = reply_config.get('message_template',
                cls.EmailMonitor.RestartCommand.ReplySettings.message_template)

            # Update Retention settings
            retention_config = email_monitor_config.get('retention', {})
            cls.EmailMonitor.Retention.days = retention_config.get('days', 7)

        except Exception as e:
            print(f"Error loading mitmproxy configuration: {e}")
            # Set default values on error
            cls.Python.use_system_python = False
            cls.Server.port = 8080
            cls.Server.password = None
            cls.EmailMonitor.enabled = True
            cls.EmailMonitor.check_interval = 5


# Access Control Configuration
class AccessControl:
    """Configuration for access control system."""
    
    class Users:
        """User access control settings."""
        admin_users: List[str] = []
        privileged_users: List[str] = []
        authorized_users: List[str] = []
    
    @classmethod
    def load_from_config(cls) -> None:
        """Load access control data from configuration file."""
        try:
            access_control_file = paths.access_control_file
            
            # Check if file exists
            if not access_control_file.exists():
                # Create an empty authorization file if not exists
                default_data = {
                    "admin_users": [],
                    "privileged_users": [],
                    "authorized_users": []
                }
                with open(access_control_file, 'w', encoding='utf-8') as f:
                    yaml.safe_dump(default_data, f, allow_unicode=True)
                cls.Users.admin_users = []
                cls.Users.privileged_users = []
                cls.Users.authorized_users = []
                return
            
            # Read and parse YAML file
            with open(access_control_file, 'r', encoding='utf-8') as f:
                auth_data = yaml.safe_load(f)
            
            # Validate data format
            if not isinstance(auth_data, dict):
                print("Warning: _access_control.yaml has incorrect format, expected dictionary")
                cls.Users.admin_users = []
                cls.Users.privileged_users = []
                cls.Users.authorized_users = []
                return

            admin_users = auth_data.get("admin_users", [])
            privileged_users = auth_data.get("privileged_users", [])
            authorized_users = auth_data.get("authorized_users", [])
            
            # Ensure list format
            if not isinstance(admin_users, list):
                print("Warning: admin_users has incorrect format, expected list")
                admin_users = []

            if not isinstance(privileged_users, list):
                print("Warning: privileged_users has incorrect format, expected list")
                privileged_users = []

            if not isinstance(authorized_users, list):
                print("Warning: authorized_users has incorrect format, expected list")
                authorized_users = []

            # Enforce hierarchy: admin_users must be in privileged_users and authorized_users
            # privileged_users must be in authorized_users
            for admin_user in admin_users:
                if admin_user not in privileged_users:
                    privileged_users.append(admin_user)
                if admin_user not in authorized_users:
                    authorized_users.append(admin_user)

            for privileged_user in privileged_users:
                if privileged_user not in authorized_users:
                    authorized_users.append(privileged_user)

            cls.Users.admin_users = admin_users
            cls.Users.privileged_users = privileged_users
            cls.Users.authorized_users = authorized_users

        except Exception as e:
            print(f"Error loading authorization data (_access_control.yaml): {e}")
            cls.Users.admin_users = []
            cls.Users.privileged_users = []
            cls.Users.authorized_users = []

class ConfigManager:
    """Manages configuration files and their reloading."""
    
    def __init__(self):
        self.config_files = {
            'main': ConfigFile('main', paths.base_config_file),
            'email': ConfigFile('email', paths.email_address_file),
            'mitm': ConfigFile('mitm', paths.mitmproxy_config_file),
            'access': ConfigFile('access', paths.access_control_file),
            'magic': ConfigFile('magic', paths.magic_plate_file),
            'service': ConfigFile('service', paths.service_url_file),
            'logger': ConfigFile('logger', paths.logger_config_file)
        }
        self.config: Dict[str, Any] = {}
        self._initial_load()
    
    def _initial_load(self) -> None:
        """Perform initial configuration load."""
        self._load_all_configs()
        self._update_global_configs()
    
    def _load_all_configs(self) -> None:
        """Load all configuration files."""
        self.config = load_yaml_file(paths.base_config_file)
        self.config['email_address'] = load_yaml_file(paths.email_address_file)
        self.config['mitmproxy'] = load_yaml_file(paths.mitmproxy_config_file)
        self.config['mitm_access'] = load_yaml_file(paths.access_control_file)
        self.config['magic_plate'] = load_yaml_file(paths.magic_plate_file)
        self.config['service_url'] = load_yaml_file(paths.service_url_file)
        self.config['logger'] = load_yaml_file(paths.logger_config_file)['logger']
    
    def _update_global_configs(self) -> None:
        """Update all global configuration objects."""
        # Update ServiceURL first as it's used by ParkerConfig
        ServiceURL.load_from_config(self.config)

        # Update all other configs
        TaskSchedulerConfig.load_from_config(self.config)
        MailConfig.load_from_config(self.config)
        ParkerConfig.load_from_config(self.config)
        NotificationConfig.load_from_config(self.config)
        MagicPlateConfig.load_from_config(self.config)
        LoggerConfig.load_from_config(self.config)
        MitmproxyConfig.load_from_config(self.config)
        AccessControl.load_from_config()
    
    def check_and_reload(self) -> bool:
        """Check if any config files have been modified and reload if necessary.
        
        Returns:
            bool: True if config was reloaded, False otherwise
        """
        # Check if any file has been modified
        if not any(file.check_modified() for file in self.config_files.values()):
            return False
            
        # Reload all configurations
        self._load_all_configs()
        self._update_global_configs()
        # print("Configuration reloaded successfully")
        return True

# Initialize configuration manager
config_manager = ConfigManager()


if __name__ == '__main__':
    from pprint import pprint
    pprint(config_manager.config)
    print("Config loaded.")
