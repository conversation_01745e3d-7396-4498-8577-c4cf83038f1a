#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# refer to register.js
from enum import Enum, auto
from karpark.utils import CamelCaseEnum


class JsonFieldKeys(str, CamelCaseEnum):
    # Don't change key name, key determines value, see CamelCaseEnum
    ID = auto()
    EE_ID = auto()
    EE_NAME = auto()
    LOCATION = auto()
    CAR_NO = auto()
    CAR_PLATE_NUMBER = auto()
    COMMENT = auto()
    REGISTER_TIME = auto()
    STATUS = auto()
    ORDER = auto()
    TOTAL = auto()
    TIME_STAMP = auto()
    ERROR = auto()
    ERROR_CODE = auto()
    ERROR_MESSAGE = auto()

class ErrorCode(str, Enum):
    CLOSED = '40003'
    DUPLICATE_CAR = '40004' ## todo mid-term document里面写程序运行前要检查的几个点

class RegObject(str, Enum):
    LOCATION = "PVG 06"
    STATUS = "WAITING"

if __name__ == "__main__":
    pass
