#!/usr/bin/env python3
"""
Centralized path management for the project

Implements singleton pattern using decorator to ensure single instance
"""
from pathlib import Path
from karpark.utils import Singleton


@Singleton
class Paths:
    """
    Centralized path management for the project
    
    Implements singleton pattern using decorator to ensure single instance
    """
    
    def __init__(self):
        """Initialize all paths"""
        # Project root directory
        self.project_root = Path(__file__).resolve().parent.parent.parent

        # Data directory
        self.data_dir = self.project_root / "data"

        # Log directory
        self.log_dir = self.project_root / "logs"

        # Source main directory
        self._karpark_dir = self.project_root / 'karpark'

        # Config directories
        self._config_dir = self._karpark_dir / "config"
        self.format_dir = self._karpark_dir / "mail" / "format"

        # directory
        self.mitm_dir = self._karpark_dir / "mitm"
        self.registrar_dir = self._karpark_dir / "registrar"

        # Config files
        self.base_config_file = self._config_dir / "base.yaml"

        # static configs
        self.magic_plate_file = self._config_dir / "static" / "_magic_plate.yaml"
        self.service_url_file = self._config_dir / "static" / "_service_url.yaml"
        self.logger_config_file = self._config_dir / "static" / "_logger.yaml"

        # dynamic configs
        self.access_control_file = self._config_dir / "_access_control.yaml"
        self.email_address_file = self._config_dir / "_email_address.yaml"
        self.mitmproxy_config_file = self._config_dir / "_mitmproxy.yaml"

# 创建全局单例实例
paths = Paths()

if __name__ == "__main__":
    # Print all paths
    paths = Paths()
    print("Log directory:", paths.log_dir)
    print("Data directory:", paths.data_dir)
    print("Email format directory:", paths.format_dir)
    print("Config file:", paths.base_config_file)
