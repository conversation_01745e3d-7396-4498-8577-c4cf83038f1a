#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import poplib
from pathlib import Path
import smtplib
from email import encoders
from email.parser import Parser
from email.header import Header, decode_header
from email.utils import parseaddr, formataddr
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from typing import List, Optional, Tuple, Generator, Set, Union
from html.parser import HTMLParser

from karpark.common.config import MailConfig
from karpark.common.paths import paths
from karpark.colorlog import logger
from karpark.mail.email_db_operations import (
    get_processed_ids as db_get_processed_ids,
    add_processed_id as db_add_processed_id,
    cleanup_old_records as db_cleanup_old_records
)
from tenacity import retry, stop_after_attempt, wait_exponential

# Mail Constants
SMTP_TIMEOUT = 10
MAX_POOL_SIZE = 5
SMTP_CONNECTION_POOL: list = []

# ===== Utility Classes =====
class TextExtractor(HTMLParser):
    """Extracts text from HTML content."""
    def __init__(self):
        super().__init__()
        self.result = []

    def handle_data(self, data):
        self.result.append(data.strip())

    def get_text(self):
        return ' '.join(self.result)


class EmailClient:
    """Unified mail client for sending and receiving emails."""

    def __init__(self):
        """Initialize mail client with configuration."""
        self.mail_config = MailConfig.Settings

    # ===== Sending Email Methods =====
    def _create_msg(
            self,
            subject: str,
            text: str,
            html: str,
            to: List[str],
            cc: Optional[List[str]] = None,
            attachments: Optional[List[str]] = None
    ) -> MIMEMultipart:
        """Create email message with text, HTML content and attachments."""
        if html:
            _msg = MIMEMultipart("alternative")
            part1 = MIMEText(text, "plain", _charset="utf-8")
            part2 = MIMEText(html, "html", _charset="utf-8")
            _msg.attach(part1)
            _msg.attach(part2)
        else:
            _msg = MIMEText(text, "plain", "utf-8")

        _msg["Subject"] = Header(subject, "utf-8")
        _msg["From"] = formataddr((Header(self.mail_config.from_name, 'utf-8').encode(), self.mail_config.from_address))
        _msg["To"] = ", ".join(to) if to else ""
        _msg["CC"] = ", ".join(cc) if cc else ""

        # Process attachments
        if attachments:
            for attachment in attachments:
                with open(attachment, "rb") as attachment_file:
                    part = MIMEBase("application", "octet-stream")
                    part.set_payload(attachment_file.read())
                    encoders.encode_base64(part)
                    filename = Path(attachment).name
                    part.add_header(
                        "Content-Disposition",
                        f"attachment; filename= {filename}",
                    )
                    _msg.attach(part)

        return _msg

    @staticmethod
    def _get_adders_lst(addresses: str) -> List[str]:
        """Extract email addresses from a string."""
        pattern = re.compile(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+")
        _addresses = pattern.findall(addresses)
        return _addresses

    @staticmethod
    def get_recipients_list(to: List[str], cc: Optional[List[str]] = None, bcc: Optional[List[str]] = None) -> List[str]:
        """Combine and deduplicate recipient lists."""
        to = to if to is not None else []
        cc = cc if cc is not None else []
        bcc = bcc if bcc is not None else []
        combined_list = to + cc + bcc
        seen = set()
        unique_list = []
        for item in combined_list:
            if item not in seen:
                seen.add(item)
                unique_list.append(item)
        return unique_list

    @staticmethod
    def _normalize_recipients(recipients: Optional[Union[List[str], str]]) -> List[str]:
        """Normalize recipient parameters to list format.
        
        Args:
            recipients: Can be a string (comma or semicolon separated) or a list of strings
            
        Returns:
            Normalized list of strings
        """
        if recipients is None:
            return []
        if isinstance(recipients, str):
            # Split by semicolon first, then by comma, and flatten the list
            return [addr.strip() for part in recipients.split(';') for addr in part.split(',') if addr.strip()]
        if isinstance(recipients, list):
            # Process each item in the list
            result = []
            for item in recipients:
                if isinstance(item, str):
                    # If the item is a string, process it as a comma/semicolon separated list
                    result.extend([addr.strip() for part in item.split(';') for addr in part.split(',') if addr.strip()])
                else:
                    # If the item is not a string, convert it to string and add it
                    result.append(str(item).strip())
            return result
        # If it's neither string nor list, convert to string and return as single item
        return [str(recipients).strip()]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def send_mail(
            self,
            subject: str,
            text: str,
            html: str = "",
            to: Optional[Union[List[str], str]] = None,
            cc: Optional[Union[List[str], str]] = None,
            bcc: Optional[Union[List[str], str]] = None,
            attachments: Optional[List[str]] = None
    ) -> bool:
        """Send an email.
        
        Args:
            subject: Email subject
            text: Email body (plain text)
            html: Email body (HTML format)
            to: List of recipients or comma/semicolon separated recipient string
            cc: List of CC recipients or comma/semicolon separated CC recipient string
            bcc: List of BCC recipients or comma/semicolon separated BCC recipient string
            attachments: List of attachment file paths
            
        Returns:
            bool: Whether the mail was sent successfully
        """
        server = None
        try:
            logger.info(f"Sending a reminder mail via {self.mail_config.smtp_host} ...")
            # Normalize recipient lists
            to_list = self._normalize_recipients(to)
            cc_list = self._normalize_recipients(cc)
            bcc_list = self._normalize_recipients(bcc)
            combined_recipients: List[str] = self.get_recipients_list(to_list, cc_list, bcc_list)
            logger.info(f"To: {', '.join(combined_recipients)}")
            _message = self._create_msg(
                subject=subject,
                text=text,
                html=html,
                to=to_list,
                cc=cc_list,
                attachments=attachments
            )
            server = self._get_smtp_connection()
            server.sendmail(
                from_addr=self.mail_config.from_address,
                to_addrs=combined_recipients,
                msg=_message.as_string(),
                mail_options=[]
            )
            logger.info("Mail sent successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to send mail: {e}")
            if server:
                self._handle_smtp_error(server)
            raise
        finally:
            if server:
                self._return_connection(server)

    def _get_smtp_connection(self):
        """Get an available SMTP connection from the pool or create a new one."""
        while SMTP_CONNECTION_POOL:
            server = SMTP_CONNECTION_POOL.pop()
            try:
                server.noop()
                return server
            except Exception:
                continue
        server = smtplib.SMTP_SSL(self.mail_config.smtp_host, self.mail_config.smtp_port, timeout=SMTP_TIMEOUT)
        server.login(self.mail_config.account, self.mail_config.token)
        return server

    @staticmethod
    def _return_connection(server):
        """Return the connection to the pool or close it if the pool is full."""
        try:
            if len(SMTP_CONNECTION_POOL) < MAX_POOL_SIZE:
                server.noop()
                SMTP_CONNECTION_POOL.append(server)
            else:
                server.quit()
        except Exception:
            server.quit()

    @staticmethod
    def _handle_smtp_error(server):
        """Handle SMTP connection errors."""
        try:
            server.quit()
        except Exception:
            pass

    # ===== Receiving Email Methods =====
    @staticmethod
    def get_processed_ids() -> Set[str]:
        """Get set of already processed email IDs."""
        return db_get_processed_ids()

    @staticmethod
    def add_processed_id(message_id: str) -> None:
        """Add email ID to processed list."""
        db_add_processed_id(message_id)

    @staticmethod
    def cleanup_old_records(days: int = 30) -> None:
        """Clean up old processed email records."""
        db_cleanup_old_records(days)

    def recv_mail(self) -> Generator[Tuple[str, str, str], None, None]:
        """Receive and process emails."""
        email_server = None
        try:
            logger.info(f"Connecting to mail server {self.mail_config.pop3_host}:{self.mail_config.pop3_port}")
            email_server = poplib.POP3_SSL(host=self.mail_config.pop3_host, port=self.mail_config.pop3_port, timeout=10)
            logger.info(f"Logging in with account {self.mail_config.account}")
            email_server.user(self.mail_config.account)
            email_server.pass_(self.mail_config.token)
            logger.info("Successfully connected to mail server")
            processed_ids = self.get_processed_ids()
            resp, list_items, octets = email_server.list()
            logger.info(f"Found {len(list_items)} messages in mailbox")
            for item in list_items:
                msg_no = int(item.split(b' ')[0])
                try:
                    resp, lines, octets = email_server.retr(msg_no)
                    email_content = b'\r\n'.join(lines)
                    try:
                        email_content = email_content.decode('utf-8')
                        msg = Parser().parsestr(email_content)
                        msg_id = msg.get('Message-ID', '')
                        if msg_id and msg_id not in processed_ids:
                            yield self.parse_email(msg)
                            self.add_processed_id(msg_id)
                    except Exception as e:
                        logger.error(f"Error processing message {msg_no}: {e}")
                        continue
                except Exception as e:
                    logger.error(f"Error retrieving message {msg_no}: {e}")
                    continue
        except poplib.error_proto as e:
            error_msg = e
            if isinstance(e.args[0], bytes):
                try:
                    error_msg = e.args[0].decode('gbk')
                except Exception:
                    error_msg = e
            logger.error(f"Mail server protocol error: {error_msg}")
        except Exception as e:
            logger.error(f"Failed to connect to mail server: {e}")
        finally:
            if email_server:
                try:
                    email_server.close()
                    logger.info("Mail server connection closed")
                except Exception:
                    pass

    def parse_email(self, msg, level: int = 0) -> Tuple[str, str, str]:
        """Parse email message to extract sender, subject and content."""
        _from, _subject, _content = None, None, None
        if level == 0:
            _from = msg.get('From', '')
            _, _from = self.parse_addr(_from)
            _subject = msg.get('Subject', '')
            _subject = self.decode_str(_subject)
        if msg.is_multipart():
            if msg.get_content_type() == 'multipart/alternative':
                for part in msg.walk():
                    if part.get_content_type() == 'text/plain':
                        _content = self.get_plain_text(part)
                        break
            else:
                parts = msg.get_payload()
                for n, part in enumerate(parts):
                    content_disposition = part.get('Content-Disposition')
                    if content_disposition and "attachment" in content_disposition:
                        continue
                    _, _, _content_new = self.parse_email(part, level + 1)
                    _content = _content if _content else _content_new
        else:
            content_type = msg.get_content_type()
            if content_type == 'text/plain' or content_type == 'text/html':
                _content = self.get_plain_text(msg)
        return _from, _subject, _content

    def get_plain_text(self, msg) -> str:
        _text = msg.get_payload(decode=True)
        _charset = self.guess_charset(msg)
        if _charset:
            _text = _text.decode(_charset)
        if _text:
            extractor = TextExtractor()
            extractor.feed(_text if isinstance(_text, str) else _text.decode(_charset or 'utf-8'))
            return extractor.get_text()
        return ''

    @staticmethod
    def decode_str(s: str) -> str:
        """Decode email header string."""
        decoded_parts = decode_header(s)
        if decoded_parts:
            value, charset = decoded_parts[0]
        else:
            value, charset = s, None
        if charset:
            value = value.decode(charset)
        return value

    @staticmethod
    def guess_charset(msg) -> Optional[str]:
        """Guess character encoding from an email message."""
        charset = msg.get_charset()
        if charset is None:
            content_type = msg.get('Content-Type', '').lower()
            for item in content_type.split(';'):
                item = item.strip()
                if item.startswith('charset'):
                    charset = item.split('=')[1]
                    break
        return charset

    @staticmethod
    def parse_addr(value: str) -> Tuple[str, str]:
        """Parse email address."""
        hdr, addr = parseaddr(value)
        hdr = EmailClient.decode_str(hdr)
        return hdr, addr

if __name__ == "__main__":
    # 创建单例实例
    email_client = EmailClient()
    # 清理旧记录
    email_client.cleanup_old_records()

    # 发送测试邮件
    with open(paths.format_dir / "reminder.html", encoding="utf-8") as f:
        demo_html = f.read()

    with open(paths.format_dir / "reminder.txt", encoding="utf-8") as f:
        demo_text = f.read()

    demo_text = demo_text.replace("__PH_FIRSTNAME__", "John, Marx")
    demo_html = demo_html.replace("__PH_FIRSTNAME__", "John, Marx")

    email_client.send_mail(
        subject="Was mich nicht umbringt, macht mich stärker.",
        text=demo_text,
        html=demo_html,
        bcc=["<EMAIL>", "<EMAIL>"],
        attachments=[paths.format_dir / 'reminder.html']
    )

    # 处理接收邮件
    for sender, subject, content in email_client.recv_mail():
        print(f'-------------------------------------\n\n')
        print(f'From: {sender}')
        print(f'Subject: {subject}')
        print(f'Content: {content}')