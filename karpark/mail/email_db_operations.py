import sqlite3
from pathlib import Path
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone

from karpark.common.paths import paths

# Constants
DB_PATH = Path(paths.data_dir) / 'sqlite' / 'karpark.db'
DEFAULT_RETENTION_DAYS = 30

# Database connection management
@contextmanager
def get_db_connection():
    """
    Context manager for database connections.
    
    Yields:
        sqlite3.Connection: Database connection object
    """
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        yield conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            conn.close()

def _ensure_db_path_exists() -> None:
    """Ensure the database directory and file exist."""
    try:
        DB_PATH.parent.mkdir(parents=True, exist_ok=True)
        if not DB_PATH.exists():
            DB_PATH.touch()
    except (OSError, IOError) as e:
        print(f"Error creating database path: {e}")

def _create_processed_emails_table() -> None:
    """Create processed emails table if it doesn't exist."""
    try:
        _ensure_db_path_exists()
        with get_db_connection() as conn:
            c = conn.cursor()
            # Processed emails table
            c.execute('''CREATE TABLE IF NOT EXISTS processed_emails (
                          message_id TEXT PRIMARY KEY,
                          processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )''')
            conn.commit()
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")

# Initialize processed emails table on module import
_create_processed_emails_table()

# Email processing operations
def get_processed_ids() -> set:
    """
    Retrieve all processed email message IDs from database.
    
    Returns:
        set: Set of processed message IDs
    """
    try:
        with get_db_connection() as conn:
            return {row[0] for row in conn.execute('SELECT message_id FROM processed_emails')}
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return set()

def add_processed_id(message_id: str) -> None:
    """
    Add new processed email ID to database.
    
    Args:
        message_id: Email message ID to add
    """
    try:
        with get_db_connection() as conn:
            conn.execute('INSERT INTO processed_emails (message_id) VALUES (?)', (message_id,))
            conn.commit()
    except sqlite3.Error as e:
        print(f"Database error: {e}")

def cleanup_old_records(days: int = DEFAULT_RETENTION_DAYS) -> None:
    """
    Cleanup records older than specified days.
    
    Args:
        days: Retention days (default 30)
    """
    try:
        cutoff_date = (datetime.now(timezone.utc) - timedelta(days=days)).isoformat()
        with get_db_connection() as conn:
            conn.execute('DELETE FROM processed_emails WHERE processed_at < ?', (cutoff_date,))
            conn.commit()
    except sqlite3.Error as e:
        print(f"Database cleanup error: {e}")
