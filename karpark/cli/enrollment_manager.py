#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified Enrollment Manager CLI using curses instead of rich
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

import click
from karpark.registrar.db_operations import (
    get_all_enrollments_detailed, get_enrollment_by_ee_id,
    update_enrollment, delete_enrollment, fetch_employee_info,
    get_all_cookie_owners, save_cookie_owner, db_save_enrollment
)
from karpark.utils.curses_ui import console, Table, Panel, Prompt, Confirm


def create_enrollments_table(enrollments):
    """Create a table for displaying enrollments"""
    table = Table(title="Enrollments")
    table.add_column("ID", justify="right")
    table.add_column("Employee ID", justify="center")
    table.add_column("Name", justify="left")
    table.add_column("Email", justify="left")
    table.add_column("Status", justify="center")
    table.add_column("Queue Position", justify="right")
    table.add_column("Created", justify="center")
    
    for enrollment in enrollments:
        table.add_row(
            str(enrollment.get('id', 'N/A')),
            str(enrollment.get('employee_id', 'N/A')),
            enrollment.get('name', 'N/A'),
            enrollment.get('email', 'N/A'),
            enrollment.get('status', 'N/A'),
            str(enrollment.get('queue_position', 'N/A')),
            enrollment.get('created_at', 'N/A')[:10] if enrollment.get('created_at') else 'N/A'
        )
    
    return table


def show_interactive_menu():
    """Show interactive menu"""
    menu_options = [
        "1. List all enrollments",
        "2. Search enrollment by Employee ID", 
        "3. Update enrollment",
        "4. Delete enrollment",
        "5. Add new enrollment",
        "6. Manage cookie owners",
        "7. Exit"
    ]

    while True:
        # Show welcome banner
        welcome_panel = Panel.fit(
            "Karpark Enrollment Manager\n\n"
            "Select an option from the menu below",
            title="Main Menu"
        )
        console.print(welcome_panel)
        console.print("")

        # Show menu options
        for option in menu_options:
            console.print(option)
        console.print("")

        # Get user choice
        try:
            choice = Prompt.ask("Select an option", default="7")
            
            if choice == "1":
                run_list_command()
            elif choice == "2":
                run_search_command()
            elif choice == "3":
                run_update_command()
            elif choice == "4":
                run_delete_command()
            elif choice == "5":
                run_add_command()
            elif choice == "6":
                run_cookie_management()
            elif choice == "7":
                console.print("Goodbye!")
                break
            else:
                console.print("Invalid choice. Please try again.")

        except (ValueError, KeyboardInterrupt):
            console.print("\nGoodbye!")
            break
        except Exception as e:
            console.print(f"Error: {e}")
            console.input("\nPress Enter to continue...")


def run_list_command():
    """List all enrollments"""
    try:
        enrollments = get_all_enrollments_detailed()
        if not enrollments:
            console.print("No enrollments found.")
            return
            
        table = create_enrollments_table(enrollments)
        console.print(table)
        
    except Exception as e:
        console.print(f"Error listing enrollments: {e}")
    
    console.input("\nPress Enter to continue...")


def run_search_command():
    """Search enrollment by Employee ID"""
    try:
        employee_id = Prompt.ask("Enter Employee ID")
        enrollment = get_enrollment_by_ee_id(employee_id)
        
        if not enrollment:
            console.print(f"No enrollment found for Employee ID: {employee_id}")
            return
            
        table = create_enrollments_table([enrollment])
        console.print(table)
        
    except Exception as e:
        console.print(f"Error searching enrollment: {e}")
    
    console.input("\nPress Enter to continue...")


def run_update_command():
    """Update enrollment"""
    try:
        employee_id = Prompt.ask("Enter Employee ID to update")
        enrollment = get_enrollment_by_ee_id(employee_id)
        
        if not enrollment:
            console.print(f"No enrollment found for Employee ID: {employee_id}")
            return
            
        console.print("Current enrollment:")
        table = create_enrollments_table([enrollment])
        console.print(table)
        
        # Get new values
        new_name = Prompt.ask("New name", default=enrollment.get('name', ''))
        new_email = Prompt.ask("New email", default=enrollment.get('email', ''))
        new_status = Prompt.ask("New status", default=enrollment.get('status', ''))
        
        # Update enrollment
        update_data = {
            'name': new_name,
            'email': new_email,
            'status': new_status
        }
        
        if update_enrollment(enrollment['id'], update_data):
            console.print("Enrollment updated successfully!")
        else:
            console.print("Failed to update enrollment.")
            
    except Exception as e:
        console.print(f"Error updating enrollment: {e}")
    
    console.input("\nPress Enter to continue...")


def run_delete_command():
    """Delete enrollment"""
    try:
        employee_id = Prompt.ask("Enter Employee ID to delete")
        enrollment = get_enrollment_by_ee_id(employee_id)
        
        if not enrollment:
            console.print(f"No enrollment found for Employee ID: {employee_id}")
            return
            
        console.print("Enrollment to delete:")
        table = create_enrollments_table([enrollment])
        console.print(table)
        
        if Confirm.ask("Are you sure you want to delete this enrollment?"):
            if delete_enrollment(enrollment['id']):
                console.print("Enrollment deleted successfully!")
            else:
                console.print("Failed to delete enrollment.")
        else:
            console.print("Deletion cancelled.")
            
    except Exception as e:
        console.print(f"Error deleting enrollment: {e}")
    
    console.input("\nPress Enter to continue...")


def run_add_command():
    """Add new enrollment"""
    try:
        employee_id = Prompt.ask("Enter Employee ID")
        name = Prompt.ask("Enter Name")
        email = Prompt.ask("Enter Email")
        
        # Fetch employee info if available
        try:
            emp_info = fetch_employee_info(employee_id)
            if emp_info:
                console.print(f"Found employee info: {emp_info}")
        except Exception:
            pass
        
        enrollment_data = {
            'employee_id': employee_id,
            'name': name,
            'email': email,
            'status': 'active'
        }
        
        if db_save_enrollment(enrollment_data):
            console.print("Enrollment added successfully!")
        else:
            console.print("Failed to add enrollment.")
            
    except Exception as e:
        console.print(f"Error adding enrollment: {e}")
    
    console.input("\nPress Enter to continue...")


def run_cookie_management():
    """Manage cookie owners"""
    try:
        owners = get_all_cookie_owners()
        
        if owners:
            console.print("Current cookie owners:")
            for owner in owners:
                console.print(f"- {owner}")
        else:
            console.print("No cookie owners found.")
        
        if Confirm.ask("Add a new cookie owner?"):
            owner_name = Prompt.ask("Enter owner name")
            cookie_value = Prompt.ask("Enter cookie value")
            
            if save_cookie_owner(owner_name, cookie_value):
                console.print("Cookie owner added successfully!")
            else:
                console.print("Failed to add cookie owner.")
                
    except Exception as e:
        console.print(f"Error managing cookies: {e}")
    
    console.input("\nPress Enter to continue...")


@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """Karpark Enrollment Manager CLI"""
    if ctx.invoked_subcommand is None:
        show_interactive_menu()


@cli.command()
def list():
    """List all enrollments"""
    run_list_command()


@cli.command()
@click.argument('employee_id')
def search(employee_id):
    """Search enrollment by Employee ID"""
    try:
        enrollment = get_enrollment_by_ee_id(employee_id)
        if enrollment:
            table = create_enrollments_table([enrollment])
            console.print(table)
        else:
            console.print(f"No enrollment found for Employee ID: {employee_id}")
    except Exception as e:
        console.print(f"Error: {e}")


@cli.command()
def interactive():
    """Start interactive mode"""
    show_interactive_menu()


def main():
    """Main entry point"""
    cli()


if __name__ == "__main__":
    main()
