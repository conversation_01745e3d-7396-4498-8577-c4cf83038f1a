#!/usr/bin/env python3
"""
CLI utility functions for enrollment management.

This module contains legacy utility functions that are kept for backward compatibility.
The new CLI uses Click and Rich for enhanced user experience.
"""

import os
import sys
from typing import List, Dict, Any, Optional

# Legacy functions - kept for backward compatibility
def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')


def print_header(title: str):
    """Print a formatted header."""
    print("=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_table(data: List[Dict[str, Any]], headers: List[str], selected_index: int = -1):
    """
    Print data in a formatted table.
    
    Args:
        data: List of dictionaries containing row data
        headers: List of column headers
        selected_index: Index of selected row (-1 for no selection)
    """
    if not data:
        print("No data to display.")
        return
    
    # Calculate column widths
    col_widths = {}
    for header in headers:
        col_widths[header] = len(header)
        for row in data:
            value = str(row.get(header, ''))
            col_widths[header] = max(col_widths[header], len(value))
    
    # Print header
    header_line = " | ".join(header.ljust(col_widths[header]) for header in headers)
    print(header_line)
    print("-" * len(header_line))
    
    # Print rows
    for i, row in enumerate(data):
        row_values = []
        for header in headers:
            value = str(row.get(header, ''))
            row_values.append(value.ljust(col_widths[header]))
        
        row_line = " | ".join(row_values)
        
        # Highlight selected row
        if i == selected_index:
            print(f"> {row_line}")
        else:
            print(f"  {row_line}")


def get_user_input(prompt: str, default: str = None, required: bool = True) -> str:
    """
    Get user input with optional default value.
    
    Args:
        prompt: Input prompt
        default: Default value if user presses Enter
        required: Whether input is required
        
    Returns:
        str: User input
    """
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        user_input = input(full_prompt).strip()
        
        if user_input:
            return user_input
        elif default:
            return default
        elif not required:
            return ""
        else:
            print("This field is required. Please enter a value.")


def get_yes_no_input(prompt: str, default: bool = None) -> bool:
    """
    Get yes/no input from user.
    
    Args:
        prompt: Input prompt
        default: Default value (True for yes, False for no, None for no default)
        
    Returns:
        bool: True for yes, False for no
    """
    if default:
        full_prompt = f"{prompt} [Y/n]: "
    elif default is False:
        full_prompt = f"{prompt} [y/N]: "
    else:
        full_prompt = f"{prompt} [y/n]: "
    
    while True:
        user_input = input(full_prompt).strip().lower()
        
        if user_input in ['y', 'yes']:
            return True
        elif user_input in ['n', 'no']:
            return False
        elif user_input == "" and default is not None:
            return default
        else:
            print("Please enter 'y' for yes or 'n' for no.")


def show_menu(title: str, options: List[str]) -> int:
    """
    Show a menu and get user selection.
    
    Args:
        title: Menu title
        options: List of menu options
        
    Returns:
        int: Selected option index (0-based)
    """
    clear_screen()
    print_header(title)
    
    for i, option in enumerate(options, 1):
        print(f"{i}. {option}")
    
    print(f"{len(options) + 1}. Exit")
    print()
    
    while True:
        try:
            choice = int(input("Please select an option: "))
            if 1 <= choice <= len(options):
                return choice - 1
            elif choice == len(options) + 1:
                return -1  # Exit
            else:
                print(f"Please enter a number between 1 and {len(options) + 1}")
        except ValueError:
            print("Please enter a valid number.")


def wait_for_key(message: str = "Press Enter to continue..."):
    """Wait for user to press a key."""
    input(message)


def format_boolean(value: bool) -> str:
    """Format boolean value for display."""
    return "Yes" if value else "No"


def format_list(items: List[str]) -> str:
    """Format list for display."""
    if not items:
        return "None"
    return ", ".join(items)


def truncate_string(text: str, max_length: int = 30) -> str:
    """Truncate string if too long."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def navigate_table(data: List[Dict[str, Any]], headers: List[str]) -> Optional[int]:
    """
    Navigate through table with arrow keys and return selected index.

    Args:
        data: Table data
        headers: Table headers

    Returns:
        int: Selected index or None if cancelled
    """
    if not data:
        print("No data to navigate.")
        return None

    selected_index = 0

    # Try to detect if we can use arrow key navigation
    arrow_keys_supported = _test_arrow_key_support()

    if not arrow_keys_supported:
        return _fallback_navigation(data, headers)

    while True:
        clear_screen()
        print_header("Navigate with ↑/↓ arrows, Enter to select, 'q' to quit")
        print_table(data, headers, selected_index)
        print()
        print("Commands: ↑/↓ to navigate, Enter to select, 'q' to quit")

        # Get user input
        try:
            if os.name == 'nt':  # Windows
                import msvcrt
                key = msvcrt.getch()
                if key == b'\xe0':  # Arrow key prefix on Windows
                    key = msvcrt.getch()
                    if key == b'H':  # Up arrow
                        selected_index = max(0, selected_index - 1)
                    elif key == b'P':  # Down arrow
                        selected_index = min(len(data) - 1, selected_index + 1)
                elif key == b'\r':  # Enter
                    return selected_index
                elif key.lower() == b'q':
                    return None
            else:  # Unix/Linux/Mac
                import termios, tty
                fd = sys.stdin.fileno()
                old_settings = termios.tcgetattr(fd)
                try:
                    tty.setraw(sys.stdin.fileno())
                    key = sys.stdin.read(1)
                    if key == '\x1b':  # ESC sequence
                        key += sys.stdin.read(2)
                        if key == '\x1b[A':  # Up arrow
                            selected_index = max(0, selected_index - 1)
                        elif key == '\x1b[B':  # Down arrow
                            selected_index = min(len(data) - 1, selected_index + 1)
                    elif key == '\r' or key == '\n':  # Enter
                        return selected_index
                    elif key.lower() == 'q':
                        return None
                finally:
                    termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        except (ImportError, OSError, Exception) as e:
            # If arrow key navigation fails, fall back to numbered selection
            print(f"Arrow key navigation failed: {e}")
            return _fallback_navigation(data, headers)


def _test_arrow_key_support() -> bool:
    """Test if arrow key navigation is supported in current environment."""
    try:
        if os.name == 'nt':  # Windows
            import msvcrt
            return True
        else:  # Unix/Linux/Mac
            import termios, tty
            return True
    except ImportError:
        return False
    except Exception:
        return False


def _fallback_navigation(data: List[Dict[str, Any]], headers: List[str]) -> Optional[int]:
    """Fallback navigation using numbered selection."""
    clear_screen()
    print_header("Select Enrollment")
    print("Arrow key navigation not available. Using numbered selection.")
    print()

    # Display numbered list
    for i, row in enumerate(data, 1):
        ee_id = row.get('ee_id', 'N/A')
        ee_name = row.get('ee_name', 'N/A')
        plate_number = row.get('plate_number', 'N/A')
        print(f"{i:2d}. {ee_id} - {ee_name} ({plate_number})")

    print(f"{len(data) + 1:2d}. Cancel")
    print()

    while True:
        try:
            choice = input("Enter number to select: ").strip()
            if not choice:
                continue

            choice_num = int(choice)
            if 1 <= choice_num <= len(data):
                return choice_num - 1
            elif choice_num == len(data) + 1:
                return None
            else:
                print(f"Please enter a number between 1 and {len(data) + 1}")
        except ValueError:
            print("Please enter a valid number.")
        except KeyboardInterrupt:
            return None
