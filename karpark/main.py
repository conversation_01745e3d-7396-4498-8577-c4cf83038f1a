#!/usr/bin/env python3
"""
Main entry point for the parking registration system.

This module initializes and runs the task scheduler, which manages
parking registrations and queue positions.
"""

import argparse

from karpark.colorlog import logger
from karpark.registrar.task_scheduler import TaskScheduler


# ===== Main Program Entry =====
def main(argv=None) -> int:
    """Run the main program

    Args:
        argv: Optional list of arguments to parse. If None, uses sys.argv.
              This allows tests to pass custom arguments or empty list.

    Returns:
        int: Exit code
    """
    parser = argparse.ArgumentParser(
        description="KarPark - SAP Labs Digital Parking Auto-Registration System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  karpark                    # Run the main program
  karpark --help             # Show this help message

For more information, see README.md
        """
    )

    parser.add_argument(
        "--version",
        action="version",
        version="KarPark 0.5.1"
    )

    # Parse arguments (for validation only)
    parser.parse_args(argv)

    try:
        return TaskScheduler().run()  # singleton

    except KeyboardInterrupt:
        logger.info("Program interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Program error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
