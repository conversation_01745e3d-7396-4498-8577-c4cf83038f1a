#!/usr/bin/env python3
"""
KarPark Tools Unified Entry Point

This script provides a unified entry point to run various KarPark tools:
- enrollment: Registration Manager
- mitm: MITM Proxy Server
- validate: Server Resource Validation Tool

Usage:
    karpark-tools validate   # Start resource validation tool
    karpark-tools enrollment          # Start registration manager
    karpark-tools mitm       # Start MITM proxy server
    karpark-tools --help              # Show help information
"""

import sys
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from karpark.utils.curses_ui import console, Panel, Table


def show_main_menu():
    """Show main menu"""
    console.print()

    # Create title panel
    title_panel = Panel.fit(
        "🛠️  KarPark Tools\n"
        "Unified tool management entry point",
        title="🎯 Welcome",
        border_style="blue",
        padding=(1, 2)
    )
    console.print(title_panel)

    # Create tools table
    table = Table(title="📋 Available Tools", show_header=True, header_style="bold magenta")
    table.add_column("Tool Name", style="cyan", width=15)
    table.add_column("Command", style="green", width=25)
    table.add_column("Description", style="white")

    table.add_row(
        "Registration Manager",
        "karpark-tools enrollment",
        "Interactive parking registration management tool"
    )
    table.add_row(
        "MITM Proxy",
        "karpark-tools mitm ",
        "MITM proxy server launcher"
    )
    table.add_row(
        "Resource Validation",
        "karpark-tools validate ",
        "Server resource change validation tool"
    )

    console.print(table)

    # Show usage examples
    examples_panel = Panel(
        "📝 Usage Examples:\n\n"
        "# Start registration manager\n"
        "karpark-tools enrollment\n\n"
        "# Start MITM proxy (foreground mode)\n"
        "karpark-tools mitm\n\n"
        "# Start MITM proxy (background mode)\n"
        "karpark-tools mitm --no-wait\n\n"
        "# Check server resource changes\n"
        "karpark-tools validate check\n\n"
        "# Show help for specific tools\n"
        "karpark-tools mitm --help\n"
        "karpark-tools validate --help",
        title="💡 Quick Start",
        border_style="yellow"
    )
    console.print(examples_panel)


def run_enrollment_manager(args):
    """Run registration manager"""
    try:
        from karpark.cli import cli

        # Show stylish welcome banner
        welcome_text = """🚗 Karpark Registration Manager

Interactive CLI tool for managing parking registrations
Powered by Click & Rich for the best user experience"""

        banner = Panel.fit(
            welcome_text,
            title="🎯 Welcome",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(banner)
        console.print()

        # Start interactive menu
        cli()

    except ImportError as e:
        console.print(f"❌ Import error: {e}")
        console.print("Please make sure you're running this script from the project root directory.")
        sys.exit(1)
    except Exception as e:
        console.print(f"❌ Error: {e}")
        sys.exit(1)


def run_mitm_server(args):
    """Run MITM proxy server"""
    try:
        from karpark.mitm.launch_mitm_server import MitmdumpLauncher

        console.print("🚀 Karpark MITM Proxy Server Launcher")
        console.print("=" * 40)

        # Create launcher instance
        launcher = MitmdumpLauncher()

        # Handle dependency installation
        if args.install_deps:
            python_exe = launcher.find_python_executable()
            console.print(f"📦 Installing dependencies using {python_exe}...")
            import subprocess
            result = subprocess.run()
            if result.returncode == 0:
                console.print("✅ Dependencies installed successfully!")
            else:
                console.print("❌ Failed to install dependencies!")
                sys.exit(1)
            return

        # Handle restart command
        if args.restart:
            success = launcher.restart()
            sys.exit(0 if success else 1)

        # Handle stop command
        if args.stop:
            success = launcher.stop()
            sys.exit(0 if success else 1)

        # Handle status command
        if args.status:
            launcher.show_status()
            return

        # Handle email monitor only
        if args.email_monitor:
            launcher.start_email_monitoring()
            return

        # Set system python if requested
        if args.system_python:
            launcher.set_system_python()

        # Launch the server
        try:
            success = launcher.run(
                wait=not args.no_wait,
                test_mode=args.test_mode
            )

            # Auto-start email monitoring if successful and enabled
            if success:
                from karpark.common.config import MitmproxyConfig
                if MitmproxyConfig.EmailMonitor.enabled:
                    launcher.start_email_monitoring()
                    if not args.no_wait:
                        console.print("📧 Email monitoring started automatically")

            if not success:
                console.print("❌ Failed to start mitmproxy server!")
                sys.exit(1)

        except KeyboardInterrupt:
            console.print("\n🛑 Server launch interrupted by user")
            sys.exit(0)
        except Exception as e:
            console.print(f"❌ Unexpected error: {e}")
            sys.exit(1)

    except ImportError as e:
        console.print(f"❌ Import error: {e}")
        sys.exit(1)
    except Exception as e:
        console.print(f"❌ Error: {e}")
        sys.exit(1)


def run_validate_resources(args):
    """Run resource validation tool"""
    try:
        # Import resource validation tool CLI
        from karpark.scripts.run_change_monitor import cli

        # Build argument list to pass to click CLI
        click_args = []

        # Add global options
        if args.interactive:
            click_args.append('--interactive')
        if args.url:
            click_args.extend()
        if args.cookie:
            click_args.extend()

        # Add subcommand
        if hasattr(args, 'validate_command') and args.validate_command:
            click_args.append(args.validate_command)

        # Call click CLI
        cli(click_args, standalone_mode=False)

    except ImportError as e:
        console.print(f"❌ Import error: {e}")
        sys.exit(1)
    except Exception as e:
        console.print(f"❌ Error: {e}")
        sys.exit(1)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='🛠️ KarPark Tools Unified Entry Point',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Tool descriptions:
  enrollment    Start registration manager
  mitm          Start MITM proxy server
  validate      Start server resource validation tool

Usage examples:
  karpark-tools enrollment                    # Start registration manager
  karpark-tools mitm                          # Start MITM proxy (foreground)
  karpark-tools mitm --no-wait               # Start MITM proxy (background)
  karpark-tools validate check               # Check resource changes
  karpark-tools validate --interactive       # Interactive resource validation
        """
    )

    subparsers = parser.add_subparsers(dest='tool', help='Select tool to use')

    # Registration manager subcommand
    enrollment_parser = subparsers.add_parser('enrollment', help='Start registration manager')

    # MITM proxy subcommand
    mitm_parser = subparsers.add_parser('mitm', help='Start MITM proxy server')
    mitm_parser.add_argument('--no-wait', action='store_true', help='Start in background mode')
    mitm_parser.add_argument('--system-python', action='store_true', help='Use system Python (override config)')
    mitm_parser.add_argument('--test-mode', action='store_true', help='Test mode (do not load script)')
    mitm_parser.add_argument('--install-deps', action='store_true', help='Install dependencies')
    mitm_parser.add_argument('--restart', action='store_true', help='Restart proxy')
    mitm_parser.add_argument('--stop', action='store_true', help='Stop proxy')
    mitm_parser.add_argument('--status', action='store_true', help='Show status')
    mitm_parser.add_argument('--email-monitor', action='store_true', help='Start email monitoring only')

    # Resource validation subcommand
    validate_parser = subparsers.add_parser('validate', help='Start server resource validation tool')
    validate_parser.add_argument('--interactive', '-i', action='store_true', help='Interactive menu mode')
    validate_parser.add_argument('--url', help='Target URL to validate')
    validate_parser.add_argument('--cookie', help='Authentication cookie for accessing protected resources')
    validate_parser.add_argument('validate_command', nargs='?', choices=['init', 'check', 'report', 'status'],
                                help='Validation command (init/check/report/status)')

    args = parser.parse_args()

    # If no tool specified, show main menu
    if not args.tool:
        show_main_menu()
        return

    # Execute corresponding function based on selected tool
    if args.tool == 'enrollment':
        run_enrollment_manager(args)
    elif args.tool == 'mitm':
        run_mitm_server(args)
    elif args.tool == 'validate':
        run_validate_resources(args)
    else:
        console.print(f"❌ Unknown tool: {args.tool}")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
