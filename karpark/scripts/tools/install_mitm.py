#!/usr/bin/env python3
"""
Karpark Project Installation Script
Supports virtual environment and system environment installation
"""

import sys
import subprocess
import argparse
from pathlib import Path

# Get absolute path of project root directory
SCRIPT_DIR = Path(__file__).parent.absolute()
PROJECT_ROOT = SCRIPT_DIR.parent.parent
REQUIREMENTS_FILE = PROJECT_ROOT / "requirements.txt"

def run_command(command, check=True):
    """Run command and handle errors"""
    try:
        # Specify encoding on Windows to avoid UnicodeDecodeError
        encoding = 'utf-8' if sys.platform != 'win32' else 'gbk'
        result = subprocess.run(
            command,
            check=check,
            capture_output=True,
            text=True,
            encoding=encoding,
            errors='replace'  # Replace characters that cannot be decoded
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr
    except UnicodeDecodeError:
        # If there are still encoding issues, try without text mode
        try:
            result = subprocess.run(command, check=check, capture_output=True)
            stdout = result.stdout.decode('utf-8', errors='replace')
            stderr = result.stderr.decode('utf-8', errors='replace')
            return result.returncode == 0, stdout, stderr
        except Exception as e:
            return False, "", str(e)

def install_with_venv():
    """Install using virtual environment"""
    print("🔧 Creating virtual environment...")

    # Create virtual environment
    success, stdout, stderr = run_command([sys.executable, "-m", "venv", ".venv"])
    if not success:
        print(f"❌ Failed to create virtual environment: {stderr}")
        return False

    # Determine Python path in virtual environment
    if sys.platform == "win32":
        venv_python = PROJECT_ROOT / ".venv" / "Scripts" / "python.exe"
    else:
        venv_python = PROJECT_ROOT / ".venv" / "bin" / "python"

    if not venv_python.exists():
        print("❌ Virtual environment creation failed")
        return False

    print("📦 Installing dependencies...")

    # Upgrade pip
    success, stdout, stderr = run_command([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"])
    if not success:
        print(f"⚠️  Failed to upgrade pip: {stderr}")

    # Install dependencies
    success, stdout, stderr = run_command([str(venv_python), "-m", "pip", "install", "-r", str(REQUIREMENTS_FILE)])
    if not success:
        print(f"❌ Failed to install dependencies: {stderr}")
        return False

    print("✅ Virtual environment installation completed!")
    print(f"🐍 Python path: {venv_python}")

    # Test installation
    print("🧪 Testing installation...")
    success, stdout, stderr = run_command([
        str(venv_python), "-c",
        "import mitmproxy; import coloredlogs; print('All dependencies installed successfully!')"
    ])

    if success:
        print("✅ Test passed!")
        return True
    else:
        print(f"❌ Test failed: {stderr}")
        return False

def install_system():
    """Install using system Python"""
    print("📦 Installing dependencies using system Python...")

    # Install dependencies
    success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "-r", str(REQUIREMENTS_FILE)])
    if not success:
        print(f"❌ Failed to install dependencies: {stderr}")
        print("💡 Tip: May need administrator privileges or use --user parameter")

        # Try user-level installation
        print("🔄 Trying user-level installation...")
        success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "--user", "-r", str(REQUIREMENTS_FILE)])
        if not success:
            print(f"❌ User-level installation also failed: {stderr}")
            return False

    print("✅ System environment installation completed!")

    # Test installation
    print("🧪 Testing installation...")
    success, stdout, stderr = run_command([
        sys.executable, "-c",
        "import mitmproxy; import coloredlogs; print('All dependencies installed successfully!')"
    ])

    if success:
        print("✅ Test passed!")
        return True
    else:
        print(f"❌ Test failed: {stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Karpark Project Installation Script')
    parser.add_argument('--system', action='store_true',
                       help='Install using system Python environment (default uses virtual environment)')
    parser.add_argument('--test-only', action='store_true',
                       help='Only test dependencies in current environment')

    args = parser.parse_args()

    print("🚀 Karpark Project Installation Program")
    print("=" * 40)

    if args.test_only:
        print("🧪 Testing current environment...")
        success, stdout, stderr = run_command([
            sys.executable, "-c",
            "import mitmproxy; import coloredlogs; print('Dependencies installed successfully!')"
        ])

        if success:
            print("✅ Current environment dependencies are complete!")
            print(f"📍 Python path: {sys.executable}")
        else:
            print("❌ Current environment is missing dependencies")
            print(f"Error message: {stderr}")
            print("Please run installation command:")
            print("  python install_mitm.py")
        return

    # Check if requirements.txt exists
    if not REQUIREMENTS_FILE.exists():
        print(f"❌ Cannot find requirements.txt file: {REQUIREMENTS_FILE}")
        return

    if args.system:
        success = install_system()
    else:
        success = install_with_venv()

    if success:
        print("\n🎉 Installation completed!")
        print("\n📖 Usage instructions:")
        if args.system:
            print("  python karpark/mitm/launch_mitm_server.py --system-python")
        else:
            print("  python karpark/mitm/launch_mitm_server.py")
        print("\n📚 For more information, see: karpark/mitm/DEPLOYMENT.md")
    else:
        print("\n❌ Installation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
