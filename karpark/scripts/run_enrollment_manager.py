#!/usr/bin/env python3
"""
Launcher script for the Enrollment Manager CLI.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from karpark.utils.curses_ui import console, Panel

def main():
    """Main entry point for the enrollment manager."""
    try:
        from karpark.cli import cli

        # Show stylish welcome banner
        welcome_text = """Karpark Enrollment Manager

A CLI tool for managing parking enrollments
Powered by Click for the best user experience"""

        banner = Panel.fit(
            welcome_text,
            title="Welcome",
            padding=(1, 2)
        )
        console.print(banner)
        console.print("")

        # Check if any command line arguments were provided
        if len(sys.argv) == 1:
            # No arguments provided, start interactive menu
            cli()
        else:
            # Arguments provided, use normal CLI mode
            cli()

    except ImportError as e:
        console.print(f"❌ Import error: {e}")
        console.print("Please make sure you're running this from the project root directory.")
        console.print("Usage: python scripts/run_enrollment_manager.py")
        sys.exit(1)
    except Exception as e:
        console.print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
