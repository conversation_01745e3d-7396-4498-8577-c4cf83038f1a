#!/usr/bin/env python3
"""
Karpark MITM Server Launcher Script

This script provides a convenient way to launch the mitmproxy server
from the scripts' directory. It calls the launch_mitm_server.py module
with proper configuration loading.
"""

import sys
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from karpark.mitm.launch_mitm_server import MitmdumpLauncher


def main():
    """Main function to launch mitmproxy server."""
    parser = argparse.ArgumentParser(
        description='🚀 Karpark MITM Proxy Server Launcher',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_interceptor.py                    # Start proxy (wait mode)
  python scripts/run_interceptor.py --no-wait          # Start in background
  python scripts/run_interceptor.py --system-python    # Use system Python (override config)
  python scripts/run_interceptor.py --test-mode        # Test mode (no script)
  python scripts/run_interceptor.py --install-deps     # Install dependencies
  python scripts/run_interceptor.py --restart          # Restart proxy
  python scripts/run_interceptor.py --stop             # Stop proxy
  python scripts/run_interceptor.py --status           # Check status
  python scripts/run_interceptor.py --email-monitor    # Start email monitoring

Configuration:
  Server settings are loaded from karpark/config/_mitmproxy.yaml
  - Port number (default: 8080)
  - Password authentication (optional)
  - Python environment preference
  - Email monitoring settings for remote restart
        """
    )

    parser.add_argument('--no-wait', action='store_true',
                       help='Run in background, exit immediately')
    parser.add_argument('--system-python', action='store_true',
                       help='Use system Python (temporarily override config)')
    parser.add_argument('--test-mode', action='store_true',
                       help='Test mode (do not load intercept script)')
    parser.add_argument('--install-deps', action='store_true',
                       help='Install required dependencies')
    parser.add_argument('--restart', action='store_true',
                       help='Restart MITM proxy')
    parser.add_argument('--stop', action='store_true',
                       help='Stop MITM proxy')
    parser.add_argument('--status', action='store_true',
                       help='Check MITM proxy status')
    parser.add_argument('--email-monitor', action='store_true',
                       help='Start email monitoring for remote restart')

    args = parser.parse_args()

    print("🚀 Karpark MITM Server Launcher")
    print("=" * 40)

    # Create launcher instance
    launcher = MitmdumpLauncher()

    # Handle dependency installation
    if args.install_deps:
        python_exe = launcher.find_python_executable()
        print(f"📦 Installing dependencies using {python_exe}...")
        import subprocess
        result = subprocess.run([
            python_exe, "-m", "pip", "install", "mitmproxy", "coloredlogs", "psutil"
        ])
        if result.returncode == 0:
            print("✅ Dependencies installed successfully!")
        else:
            print("❌ Failed to install dependencies!")
            sys.exit(1)
        return

    # Handle restart command
    if args.restart:
        success = launcher.restart()
        sys.exit(0 if success else 1)

    # Handle stop command
    if args.stop:
        success = launcher.stop()
        sys.exit(0 if success else 1)

    # Handle status command
    if args.status:
        status_info = launcher.status()
        print("📊 MITM Proxy Status:")
        print(f"   Status: {status_info['status']}")
        if status_info['status'] == 'running':
            print(f"   PID: {status_info['pid']}")
            print(f"   Port: {status_info['port']}")
            print(f"   CPU: {status_info['cpu_percent']}%")
            print(f"   Memory: {status_info['memory_mb']} MB")
            print(f"   Started: {status_info['start_time']}")
            print(f"   Email Monitor: {'Running' if status_info['email_monitor'] else 'Stopped'}")
        return

    # Handle email monitoring
    if args.email_monitor:
        success = launcher.start_email_monitoring()
        if success:
            print("📧 Email monitoring is running. Press Ctrl+C to stop.")
            try:
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Stopping email monitoring...")
                launcher.email_monitor.stop()
        sys.exit(0 if success else 1)

    # Apply system Python setting if requested
    if args.system_python:
        launcher.set_system_python(True)

    # Launch the server
    try:
        success = launcher.run(
            wait=not args.no_wait,
            test_mode=args.test_mode
        )

        # Auto-start email monitoring if successful and enabled
        if success:
            from karpark.common.config import MitmproxyConfig
            if MitmproxyConfig.EmailMonitor.enabled:
                launcher.start_email_monitoring()
                if not args.no_wait:
                    print("📧 Email monitoring started automatically")

        if not success:
            print("❌ Failed to start mitmproxy server!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 Server launch interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
