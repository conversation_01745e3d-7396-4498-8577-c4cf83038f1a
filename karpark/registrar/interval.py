#!/usr/bin/env python3

"""
Time utility module for the parking system.

This module provides time-related functionality including
- Time interval calculations
- Work time detection
- Time zone handling (Beijing time)
- Performance measurement
"""
from typing import Optional
from datetime import datetime, timedelta, timezone

from karpark.common.config import TaskSchedulerConfig
from karpark.colorlog import logger

# Constants
SATURDAY = 5  # 0 Monday, ..., 5 Saturday, 6 Sunday
SHANGHAI_UTC_OFFSET = 8  # Shanghai is UTC+8


def get_shanghai_time() -> datetime:
    """Get current time in Beijing timezone (UTC+8)"""
    return datetime.now(timezone(timedelta(hours=SHANGHAI_UTC_OFFSET)))


def is_time_in_between(begin_time: str, end_time: str, current_time: Optional[datetime] = None) -> bool:
    """Check if current time is within specified time range"""
    if current_time is None:
        current_time = get_shanghai_time()
    current_time_str = current_time.strftime("%H:%M:%S")
    return begin_time <= current_time_str <= end_time


def is_work_time(current_time: Optional[datetime] = None) -> bool:
    """Check if current time is work time (weekday between work hours)"""
    if current_time is None:
        current_time = get_shanghai_time()
    weekday = current_time.weekday()
    return weekday < SATURDAY and is_time_in_between(
        TaskSchedulerConfig.WorkTime.work_time_start,
        TaskSchedulerConfig.WorkTime.work_time_end,
        current_time
    )


def get_break_time(pace_work_time=59 * 3, pace_non_work_time=59 * 15) -> int:
    """Calculate break time based on work/non-work hours
    
    Args:
        pace_work_time: Break time in seconds during work hours
        pace_non_work_time: Break time in seconds during non-work hours
        
    Returns:
        int: Break time in seconds
    """
    shanghai_time = get_shanghai_time()
    if is_work_time(shanghai_time):
        duration = pace_work_time
        logger.debug(f"Work-time in Shanghai, shorter check interval {duration}")
    else:
        duration = pace_non_work_time
        logger.debug(f"Non-work-time in Shanghai, longer check interval {duration}")
    return int(duration)


if __name__ == "__main__":
    print(get_break_time())
