import random
from collections import OrderedDict
from typing import List, Optional

from karpark.common.config import MagicPlateConfig


def substitute_characters(plate_number: str, substitute_all: bool = False) -> str:
    """
    Replace characters in the license plate with similar looking characters

    Args:
        plate_number: Original license plate number
        substitute_all: Whether to replace all replaceable characters

    Returns:
        Modified license plate number
    """
    if not plate_number:
        return plate_number

    result = plate_number

    # Find all replaceable character positions
    substitutable_positions = []
    for i, char in enumerate(result):
        if char in CHAR_MAPPING:
            substitutable_positions.append((i, char))

    if not substitutable_positions:
        return result

    # If not replacing all characters, randomly select one position to replace
    if not substitute_all:
        pos_idx, char = random.choice(substitutable_positions)
        substitute = random.choice(CHAR_MAPPING[char])
        result = result[:pos_idx] + substitute + result[pos_idx + 1:]

    else:
        # Replace all replaceable characters
        for pos_idx, char in substitutable_positions:
            substitute = random.choice(CHAR_MAPPING[char])
            # Need to consider that previous substitutes may have changed string length
            result = result[:pos_idx] + substitute + result[pos_idx + 1:]

    return result


def find_optimal_mark_position(plate_number: str) -> int:
    """
    Find the optimal position to insert an invisible mark.
    Priority order:
    1. After the first Chinese character
    2. After the first letter or digit
    3. After later characters, but not at the beginning or end

    Args:
        plate_number: Original license plate number

    Returns:
        Optimal position to insert the invisible mark
    """
    if not plate_number or len(plate_number) <= 1:
        return -1  # Not suitable for marking

    # Avoid first and last positions
    valid_positions = list(range(1, len(plate_number)))
    if not valid_positions:
        return -1

    # Check for Chinese characters first (Unicode ranges for common Chinese characters)
    for i in valid_positions:
        if i > 0 and '\u4e00' <= plate_number[i - 1] <= '\u9fff':
            return i

    # Then check for letters or digits
    for i in valid_positions:
        if i > 0 and plate_number[i - 1].isalnum():
            return i

    # If no optimal position found, return a position in the middle (but not first or last)
    if len(valid_positions) > 0:
        return valid_positions[len(valid_positions) // 2]

    return -1


def add_ltr_mark(plate_number: str, position: int = None) -> str:
    """
    Add an invisible mark at specified position in the license plate.
    If position is None, find the optimal position automatically.

    Args:
        plate_number: Original license plate number
        position: Position to insert the invisible mark (default: None, will find optimal position)

    Returns:
        License plate with invisible mark
    """
    if not plate_number or len(plate_number) <= 1:
        return plate_number

    # Find optimal position if not specified
    if position is None:
        position = find_optimal_mark_position(plate_number)

    # If no valid position found or position is invalid
    if position < 0 or position >= len(plate_number):
        # Try middle position as fallback
        position = len(plate_number) // 2
        if position <= 0 or position >= len(plate_number):
            return plate_number  # Cannot add mark

    result = plate_number[:position] + MagicPlateConfig.Mapping.magic_invisible + plate_number[position:]

    return result


# Hardcoded character mapping (no longer configurable)
CHAR_MAPPING = {
    'A': ['Α', 'А'],  # U+0391 Greek, U+0410 Cyrillic
    'B': ['Β', 'В'],  # U+0392 Greek, U+0412 Cyrillic
    'C': ['С'],  # U+0421 Cyrillic
    'E': ['Ε', 'Е'],  # U+0395 Greek, U+0415 Cyrillic
    'H': ['Η', 'Н'],  # U+0397 Greek, U+041D Cyrillic
    'J': ['Ј'],  # U+0408 Cyrillic(Serbian)
    'K': ['Κ'],  # U+039A Greek
    'M': ['Μ', 'М'],  # U+039C Greek, U+041C Cyrillic
    'P': ['Ρ', 'Р'],  # U+03A1 Greek, U+0420 Cyrillic
    'T': ['Τ', 'Т'],  # U+03A4 Greek, U+0422 Cyrillic
    'X': ['Х'],  # U+0425 Cyrillic
    'Y': ['Υ'],  # U+03A5 Greek
    'Z': ['Ζ'],  # U+0396 Greek
    'Q': ['Ԛ'],  # U+051A Cyrillic extended(Kurdish)
}

# Known historical invisible characters for backward compatibility
HISTORICAL_INVISIBLE_CHARS = [
    '\u200b',  # U+200B ZERO-WIDTH SPACE
    '\u200c',  # U+200C ZERO-WIDTH NON-JOINER
    '\u200d',  # U+200D ZERO-WIDTH JOINER
    '\u200e',  # U+200E LEFT-TO-RIGHT MARK
    '\u2060',  # U+2060 WORD JOINER
]


def restore_plate_variant(variant: str) -> str:
    """
    Restore a plate variant back to its original form by removing special characters and replacing variant characters.
    Handles historical invisible characters for backward compatibility.
    If restoration fails, returns the original variant unchanged.

    Args:
        variant: The plate variant to restore

    Returns:
        Original plate number if restoration succeeds, otherwise returns the variant unchanged
    """
    if not variant:
        return variant

    # Step 1: Remove all known invisible marks (current and historical)
    restored = variant
    for invisible_char in HISTORICAL_INVISIBLE_CHARS:
        restored = restored.replace(invisible_char, '')

    # Step 2: Create reverse character mapping for restoration using hardcoded mapping
    reverse_mapping = {}
    for original_char, substitute_chars in CHAR_MAPPING.items():
        for substitute_char in substitute_chars:
            reverse_mapping[substitute_char] = original_char

    # Step 3: Replace variant characters back to original characters
    result = []
    for char in restored:
        if char in reverse_mapping:
            result.append(reverse_mapping[char])
        else:
            result.append(char)

    restored_plate = ''.join(result)

    # Step 4: Basic validation - check if the restored plate has reasonable length
    # Standard Chinese license plates are typically 7 or 8 characters
    if len(restored_plate) not in [7, 8]:
        return variant  # Return original variant if restoration seems invalid

    # Step 5: Additional validation - check if it contains Chinese characters (typical for Chinese plates)
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in restored_plate)
    if not has_chinese:
        # If no Chinese characters, it might still be valid, but let's be conservative
        # Only return restored version if it looks like a reasonable plate format
        if not (restored_plate[0].isalpha() and any(char.isdigit() for char in restored_plate)):
            return variant

    return restored_plate


def create_plate_variants(plate_number: str, max_variants: Optional[int] = None) -> List[str]:
    """
    Generate various plate number variants based on configured priorities.
    
    Args:
        plate_number: Original license plate number
        max_variants: Maximum number of variants to return (None for all)
        
    Returns:
        List of plate number variants, ordered by priority
    """
    if not plate_number or len(plate_number) not in [7, 8]:  # Only process standard length plates
        return []

    ordered_results = OrderedDict()

    # Process each priority in order
    for priority in MagicPlateConfig.Priority.priority_config:
        if not priority['enabled']:
            continue

        if priority['name'] == 'single_char_substitute':
            # Priority 1: Single character substitute variants
            for i, char in enumerate(plate_number):
                if char in CHAR_MAPPING:
                    for substitute in CHAR_MAPPING[char]:
                        variant = plate_number[:i] + substitute + plate_number[i + 1:]
                        ordered_results[variant] = None

        elif priority['name'] == 'multi_char_substitute':
            # Priority 2: Multiple character substitute combinations
            substitutable_positions = [(i, char) for i, char in enumerate(plate_number) if char in CHAR_MAPPING]
            if len(substitutable_positions) >= 2:
                for i in range(len(substitutable_positions)):
                    for j in range(i + 1, len(substitutable_positions)):
                        pos1, char1 = substitutable_positions[i]
                        pos2, char2 = substitutable_positions[j]

                        for repl1 in CHAR_MAPPING[char1]:
                            for repl2 in CHAR_MAPPING[char2]:
                                variant = list(plate_number)
                                variant[pos1] = repl1
                                variant[pos2] = repl2
                                ordered_results[''.join(variant)] = None

        elif priority['name'] == 'invisible_mark':
            # Priority 3: Invisible mark variants (only for 7-digit plates)
            if len(plate_number) == 7 and len(plate_number) >= 2:
                # Find optimal position
                optimal_position = find_optimal_mark_position(plate_number)
                if optimal_position > 0:
                    marked_plate = add_ltr_mark(plate_number, optimal_position)
                    ordered_results[marked_plate] = None

                # Other positions (skip first and last)
                for i in range(1, len(plate_number)):
                    if i != optimal_position and 0 < i < len(plate_number):
                        marked_plate = add_ltr_mark(plate_number, i)
                        ordered_results[marked_plate] = None

        elif priority['name'] == 'single_char_with_mark':
            # Priority 4: Single character substitute with invisible mark (only for 7-digit plates)
            if len(plate_number) == 7:
                for i, char in enumerate(plate_number):
                    if char in CHAR_MAPPING:
                        for substitute in CHAR_MAPPING[char]:
                            # Create single character substitute variant
                            single_variant = plate_number[:i] + substitute + plate_number[i + 1:]

                            # Add invisible mark to each single character variant
                            for j in range(1, len(single_variant)):
                                marked_variant = add_ltr_mark(single_variant, j)
                                ordered_results[marked_variant] = None

        elif priority['name'] == 'multi_char_with_mark':
            # Priority 5: Multiple character substitute with invisible mark (only for 7-digit plates)
            if len(plate_number) == 7:
                substitutable_positions = [(i, char) for i, char in enumerate(plate_number) if char in CHAR_MAPPING]
                if len(substitutable_positions) >= 2:
                    for i in range(len(substitutable_positions)):
                        for j in range(i + 1, len(substitutable_positions)):
                            pos1, char1 = substitutable_positions[i]
                            pos2, char2 = substitutable_positions[j]

                            for repl1 in CHAR_MAPPING[char1]:
                                for repl2 in CHAR_MAPPING[char2]:
                                    # Create multi-character substitute variant
                                    multi_variant = list(plate_number)
                                    multi_variant[pos1] = repl1
                                    multi_variant[pos2] = repl2
                                    multi_variant = ''.join(multi_variant)

                                    # Add invisible mark to each multi-character variant
                                    for k in range(1, len(multi_variant)):
                                        marked_variant = add_ltr_mark(multi_variant, k)
                                        ordered_results[marked_variant] = None

    # Convert to list and remove original plate number
    unique_results = list(ordered_results.keys())
    if plate_number in unique_results:
        unique_results.remove(plate_number)

    # Apply limit if specified
    if max_variants is not None:
        unique_results = unique_results[:max_variants]

    return unique_results


if __name__ == '__main__':

    print("=== Basic Substitute Test ===")
    test_plates = ['沪A123456', '沪J01962', '沪D019525']
    # test_plates = ['沪KX0090']
    for plate in test_plates:
        # Show variants with limit
        variants = create_plate_variants(plate, max_variants=30)
        print(f"\nOriginal plate: {plate}")
        print(f"Number of variants: {len(variants)}")
        for i, variant in enumerate(variants):
            print(f"  {i + 1}. {variant} (Unicode: {[f'U+{ord(c):04X}' for c in variant]})")
        print("\n" + "=" * 50 + "\n")

    print("=== Plate Restoration Test ===")
    test_plates = ['沪A123456', '沪J01962']
    for plate in test_plates:
        print(f"\nOriginal plate: {plate}")
        variants = create_plate_variants(plate, max_variants=5)

        for i, variant in enumerate(variants[:3]):  # Test first 3 variants
            restored = restore_plate_variant(variant)
            success = "✓" if restored == plate else "✗"
            print(f"  Variant {i + 1}: {variant}")
            print(f"  Restored:   {restored} {success}")
            if restored != plate:
                print(f"    Expected: {plate}")
            print()
        print("=" * 50 + "\n")

    print("=== Historical Invisible Character Test ===")
    # Test with historical invisible character (ZWSP instead of LRM)
    test_variant_with_zwsp = '沪​A123456'  # Contains ZWSP (U+200B)
    print(f"Variant with ZWSP: {test_variant_with_zwsp}")
    restored_zwsp = restore_plate_variant(test_variant_with_zwsp)
    print(f"Restored: {restored_zwsp}")
    print(f"Success: {'✓' if restored_zwsp == '沪A123456' else '✗'}")

    # Test with multiple invisible characters
    test_variant_mixed = '沪​‎A123456'  # Contains both ZWSP and LRM
    print(f"\nVariant with mixed invisible chars: {test_variant_mixed}")
    restored_mixed = restore_plate_variant(test_variant_mixed)
    print(f"Restored: {restored_mixed}")
    print(f"Success: {'✓' if restored_mixed == '沪A123456' else '✗'}")
    print("=" * 50 + "\n")
