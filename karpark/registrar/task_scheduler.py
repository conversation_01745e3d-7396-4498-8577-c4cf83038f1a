#!/usr/bin/env python3
import time
import random
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple, Union

from karpark.colorlog import logger
from karpark.utils import AutoNameEnum, auto

from karpark.common.config import config_manager, TaskSchedulerConfig, AccessControl

from karpark.registrar.parker import Parker
from karpark.registrar.entities import Status
from karpark.registrar.interval import get_break_time
from karpark.registrar.status_manager import StatusManager
from karpark.registrar.db_operations import enrollment_db_operations, EnrollmentDBOperation


class UserPriority(AutoNameEnum):
    """User priority levels for task scheduling.

    Values are automatically converted to camelCase strings:
    - ADMIN_PRIORITY -> 'adminPriority'
    - PRIVILEGED_PRIORITY -> 'privilegedPriority'
    - REGULAR_PRIORITY -> 'regularPriority'
    """
    ADMIN_PRIORITY = auto()
    PRIVILEGED_PRIORITY = auto()
    REGULAR_PRIORITY = auto()

class TaskScheduler:
    """Manages and schedules multiple Parker instances.

    Responsible for managing Parker instances, including adding, removing, and scheduling
    their processing order based on last check time.

    Implements singleton pattern using __new__ method.
    """

    TOKEN_EXPIRATION = timedelta(minutes=29)  # 30 minutes with 1-minute safety margin

    # Class variables for singleton pattern
    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        """Implement singleton pattern: ensures only one TaskScheduler instance"""
        if cls._instance is None:
            cls._instance = super(TaskScheduler, cls).__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize the task scheduler."""
        # Prevent re-initialization
        if TaskScheduler._initialized:
            return

        self.active_parker: Optional[Parker] = None
        self.pending_tasks: List[Parker] = []
        self.completed_tasks: List[Parker] = []
        self.stalled_tasks: List[Parker] = []
        self.has_loaded_all = False  # Flag indicating whether full loading has been completed

        # Mark as initialized
        TaskScheduler._initialized = True

    def load_tasks_from_enrollments(self) -> None:
        """Process all pending tasks and handle new enrollments."""
        active_enrollments, withdrawn_enrollments = self._get_enrollment_groups()
        # Debug colorlog for enrollment counts
        logger.debug(f"Found {len(active_enrollments)} new active enrollments")
        logger.debug(f"Found {len(withdrawn_enrollments)} new withdrawn enrollments")
        if not (active_enrollments or withdrawn_enrollments):
            logger.info("No new enrollments found")
            return

        self._handle_active_enrollments(active_enrollments)
        self._handle_withdrawn_enrollments(withdrawn_enrollments)

    @staticmethod
    def filter_enrollments(enrollments: List[Dict], withdrawn: bool = False) -> List[Dict]:
        """Filter enrollments based on withdrawal status.

        Args:
            enrollments: List of enrollment dictionaries
            withdrawn: Whether to filter for withdrawn enrollments

        Returns:
            Filtered list of enrollments
        """
        return [item for item in enrollments if item.get('is_withdrawn', False) == withdrawn]

    def _get_enrollment_groups(self) -> Tuple[List[Dict], List[Dict]]:
        """Get and classify enrollment information."""
        if not self.has_loaded_all:
            # If full loading hasn't been done, perform full load
            enrollments = enrollment_db_operations(EnrollmentDBOperation.GET_ALL_ENROLLMENT)
            logger.info(f"Initial loading: Retrieved {len(enrollments)} total enrollments")
            self.has_loaded_all = True  # Mark that full loading has been completed
        else:
            # Already performed full loading, load incremental data as per original logic
            enrollments = enrollment_db_operations(EnrollmentDBOperation.GET_DELTA__ENROLLMENT)

        active = self.filter_enrollments(enrollments, withdrawn=False)
        withdrawn = self.filter_enrollments(enrollments, withdrawn=True)
        return active, withdrawn

    def _handle_active_enrollments(self, enrollments: List[Dict]) -> None:
        """Process active enrollments and create/update Parker instances."""
        for enrollment in enrollments:
            parker = self._create_parker(
                enrollment['cookie'],
                enrollment['plate_number'],
                enrollment['use_magic_plate'],
                enrollment['emails']
            )
            if parker:
                self._add_or_update_task(parker)

    def _handle_withdrawn_enrollments(self, enrollments: List[Dict]) -> None:
        """Process withdrawn enrollments and remove corresponding Parker instances."""
        for enrollment in enrollments:
            if ee_id := enrollment.get('ee_id'):
                self._remove_task(ee_id)

    @staticmethod
    def _create_parker(cookie: str, plate_number: str, use_magic_plate: bool,
                       emails: Optional[Union[List[str], str]] = None) -> Optional[Parker]:
        """Create a new Parker instance."""
        parker = Parker(cookie, plate_number, use_magic_plate, emails)
        return parker if parker.queue_info.ee_id else None

    def _remove_task(self, ee_id: str) -> None:
        """Remove a task from pending list if it exists."""
        for i, parker in enumerate(self.pending_tasks):
            if parker.queue_info.ee_id == ee_id:
                removed = self.pending_tasks.pop(i)
                logger.info(f"Removed task {removed.queue_info.ee_id}")
                break

    def _add_or_update_task(self, parker: Parker) -> None:
        """Add a new task or update existing task in the pending list.
        
        If the task already exists in pending_tasks (same ee_id), update its cookie and
        registration number. If it doesn't exist in either list, add it to pending_tasks.
        If it only exists in completed_tasks, do nothing.
        """
        # Check if parker already exists in pending_tasks
        for _, existing_parker in enumerate(self.pending_tasks):
            if existing_parker == parker:  # __eq__ compares ee_id
                # __eq__ compares ee_id, but may have different cookie or plate_number or use_magic_plate
                # Don't replace existing Parker instance as some attributes like initial_order need to be preserved
                # self._replace_parker(existing_parker, parker)
                existing_parker.update_init_parameters(parker.cookie, parker.plate_number, parker.use_magic_plate,
                                                       parker.emails)
                logger.info(f"Updated existing task {existing_parker} with new enrollment data")
                return

        # If not in pending_tasks and not in completed_tasks, add to pending_tasks
        if parker not in self.completed_tasks and parker not in self.stalled_tasks:
            self.pending_tasks.append(parker)
            logger.info(f"Loaded new task {parker} to pending tasks")

    def select_next_due_task(self) -> None:
        """Select the next task to process based on last check time."""
        self.active_parker = (
            min(self.pending_tasks, key=lambda p: p.last_check_time)
            if self.pending_tasks else None
        )

    def has_registered_task(self) -> bool:
        """Check if any task has reached REGISTERED status."""
        return any(p.current_status == Status.REG_SUCCESS for p in self.completed_tasks)

    def _get_user_priority(self, parker: Parker) -> UserPriority:
        """Get user priority based on access control configuration.

        Args:
            parker: Parker instance to check

        Returns:
            UserPriority: Priority level (adminPriority > privilegedPriority > regularPriority)
        """
        # Reload access control configuration to get latest changes
        AccessControl.load_from_config()

        if not parker.queue_info.ee_id:
            return UserPriority.REGULAR_PRIORITY  # Regular user priority for unknown ee_id

        ee_id = parker.queue_info.ee_id

        if ee_id in AccessControl.Users.admin_users:
            return UserPriority.ADMIN_PRIORITY  # Highest priority
        elif ee_id in AccessControl.Users.privileged_users:
            return UserPriority.PRIVILEGED_PRIORITY  # Medium priority
        else:
            return UserPriority.REGULAR_PRIORITY  # Regular priority

    def _sort_pending_tasks_by_priority(self) -> None:
        """Sort pending tasks based on configuration: either by priority or randomly."""
        if not self.pending_tasks:
            return

        # Check if priority sorting is enabled
        if TaskSchedulerConfig.Control.enable_priority_sorting:
            logger.info("Sorting pending tasks by user priority...")
            self._sort_by_priority_with_random_within_groups()
        else:
            logger.info("Sorting pending tasks randomly...")
            self._sort_randomly()

        # Log the sorted order for debugging
        for i, parker in enumerate(self.pending_tasks):
            if TaskSchedulerConfig.Control.enable_priority_sorting:
                priority = self._get_user_priority(parker)
                logger.info(f"Priority {i+1}: {parker.queue_info.ee_id} ({priority.value})")
            else:
                logger.info(f"Random {i+1}: {parker.queue_info.ee_id}")

    def _sort_by_priority_with_random_within_groups(self) -> None:
        # TODO 改成按登记顺序吧
        """Sort tasks by priority groups (admin > privileged > regular) with random order within each group."""
        # Group tasks by priority
        admin_tasks = []
        privileged_tasks = []
        regular_tasks = []

        for parker in self.pending_tasks:
            priority = self._get_user_priority(parker)
            if priority == UserPriority.ADMIN_PRIORITY:
                admin_tasks.append(parker)
            elif priority == UserPriority.PRIVILEGED_PRIORITY:
                privileged_tasks.append(parker)
            else:  # UserPriority.REGULAR_PRIORITY
                regular_tasks.append(parker)

        # Randomly shuffle within each group
        random.shuffle(admin_tasks)
        random.shuffle(privileged_tasks)
        random.shuffle(regular_tasks)

        # Combine groups in priority order
        self.pending_tasks = admin_tasks + privileged_tasks + regular_tasks

    def _sort_randomly(self) -> None:
        """Sort all tasks randomly regardless of priority."""
        random.shuffle(self.pending_tasks)

    def process_active_task(self) -> None:
        """Process the currently active task, handles registration, task completion/stalling."""
        if not self.active_parker:
            return

        try:
            status = self.active_parker.current_status

            if status == Status.OPEN_DELETED:
                logger.info(f"OPEN_DELETED detected for {self.active_parker.queue_info.ee_id}, entering priority mode...")
                time.sleep(TaskSchedulerConfig.Intervals.pause_before_register.seconds)
                self.active_parker.register_parking()
                # status may be updated after registration
                status = self.active_parker.current_status

            if status == Status.REG_SUCCESS:
                logger.info(f"Task {self.active_parker} completed registration")
                self._complete_task()
            elif status == Status.REG_FAILURE:
                    logger.info(f"Task {self.active_parker} failed registration")
                    self._move_to_stalled()
            elif status in (Status.OPEN_STATIC, Status.OPEN_MOVING, Status.OPEN_MOVING):
                if self.has_registered_task() and self._should_stall_based_on_total():
                    logger.info(
                        f"Task {self.active_parker} reached limit: "
                        f"{self.active_parker.queue_info.total} > {TaskSchedulerConfig.Control.exit_on_total}"
                    )
                    self._move_to_stalled()

        except Exception as e:
            logger.error(f"Error processing task {self.active_parker}: {e}")

    def select_next_sorted_task(self) -> bool:
        """Get the next task to process from sorted pending tasks.

        Returns:
            Optional[Parker]: Next parker to process, or None if no tasks available
        """
        if not self.pending_tasks:
            return False

        # Get the next task in sequence
        if self.pending_tasks:
            self.active_parker = self.pending_tasks[0]
            # Rotate the list to prepare for next selection
            if len(self.pending_tasks) > 1:
                self.pending_tasks = self.pending_tasks[1:] + [self.pending_tasks[0]]
        logger.info(f"Next task selected: {self.active_parker}")
        return True

    def _complete_task(self) -> None:
        """Move the active task from pending to 'completed'."""
        if self.active_parker in self.pending_tasks:
            self.completed_tasks.append(self.active_parker)
            self.pending_tasks.remove(self.active_parker)
            logger.info(f"Completed task {self.active_parker}")

    def _should_stall_based_on_total(self) -> bool:
        """Determine if the queue total has reached the exit threshold."""
        logger.debug(f"Checking if total {self.active_parker.queue_info.total} exceeds exit threshold {TaskSchedulerConfig.Control.exit_on_total}")
        return self.active_parker.queue_info.total > TaskSchedulerConfig.Control.exit_on_total

    def _move_to_stalled(self) -> None:
        """Move the active task to the stalled tasks list."""
        if self.active_parker in self.pending_tasks:
            self.stalled_tasks.append(self.active_parker)
            self.pending_tasks.remove(self.active_parker)
            logger.info(f"Moved task {self.active_parker} to stalled")

    def smart_sleep(self) -> None:
        """Calculate sleep time based on task status and token expiration."""
        if self.active_parker:
            status_based_delay = self.get_status_based_delay()
            token_based_delay = self.get_token_based_delay()
            delay = min(status_based_delay, token_based_delay)
            logger.info(f"Take break for {delay} seconds (status_based_delay={status_based_delay}s, token_based_delay={token_based_delay}s)")

        else:
            delay = TaskSchedulerConfig.Intervals.wait_enrollment.seconds
            logger.info(f"Take break for {delay} seconds for new enrollments")


        time.sleep(delay)

    def get_status_based_delay(self) -> float:
        """Calculate sleep time based on task status."""
        if not self.active_parker:
            return TaskSchedulerConfig.Intervals.poll_interval_minimum.seconds

        status = self.active_parker.current_status
        if status == Status.OPEN_STATIC:
            delay = get_break_time(
                TaskSchedulerConfig.Intervals.poll_interval_work.seconds,
                TaskSchedulerConfig.Intervals.poll_interval_off.seconds
            )
            logger.info(f"{status.value} condition detected: Dynamic interval based on working hours ({delay} seconds)")
        elif status in (Status.OPEN_DELETED, Status.OPEN_MOVING):
            delay = TaskSchedulerConfig.Intervals.poll_interval_after_reopen.seconds
            logger.info(f"{status.value} condition detected: Registration interval ({delay} seconds)")
        else:
            delay = TaskSchedulerConfig.Intervals.poll_interval_minimum.seconds
            logger.info(f"{status.value} condition detected: Minimum break duration ({delay} seconds)")

        return delay

    def get_token_based_delay(self) -> float:
        """Calculate sleep time based on token expiration."""
        if len(self.pending_tasks) < 2:
            return self.TOKEN_EXPIRATION.total_seconds()

        oldest_time = min(p.last_check_time for p in self.pending_tasks)
        current_time = datetime.now(timezone.utc)
        seconds_to_expiration = ((oldest_time + self.TOKEN_EXPIRATION) - current_time).total_seconds()
        return max(0.0, seconds_to_expiration)

    def run(self) -> int | None:
        """Run the main scheduling loop.

        Returns:
            int: Exit code (0 for normal exit)
        """
        # First loop: Detect OPEN_DELETED status
        while True:
            # Check and reload config if changed
            config_manager.check_and_reload()
            self.load_tasks_from_enrollments()

            # Select next task for detection
            self.select_next_due_task()

            if not self.active_parker:
                logger.info(f"{len(self.pending_tasks)} pending tasks, waiting...")
                self.smart_sleep()
                continue

            # Fetch queue info and process status
            self.active_parker.fetch_queue_info()
            StatusManager().process_status_of_parker(self.active_parker)

            # Check if OPEN_DELETED detected
            if self.active_parker.current_status == Status.OPEN_DELETED:
                logger.info(f"{Status.OPEN_DELETED.value} detected for {self.active_parker.queue_info.ee_id}, entering registration mode...")
                break

            self.smart_sleep()
        
        # sort pending tasks by priority
        self._sort_pending_tasks_by_priority()

        # Second loop: Process tasks in priority order
        while True:
            ## TODO Once registration starts, no need to reload config and load enrollments. But may consider implementing this in MITM to prevent new enrollments
            # config_manager.check_and_reload()
            # self.load_tasks_from_enrollments()

            # Process tasks in priority order
            self.select_next_sorted_task()

            if not self.active_parker:  # Logically impossible to reach here，anyway... in case
                logger.debug("No pending or active tasks; exiting...")
                break

            # Fetch queue info and process status
            self.active_parker.fetch_queue_info()
            StatusManager().process_status_of_parker(self.active_parker)

            # Process the priority task
            logger.info(f"Processing priority task: {self.active_parker}")
            self.process_active_task()

            # Check if all tasks are completed
            if (self.completed_tasks or self.stalled_tasks) and not self.pending_tasks:
                # Log completed and stalled tasks details
                for task in self.completed_tasks:
                    logger.debug(f"Completed task: {task}")
                for task in self.stalled_tasks:
                    logger.debug(f"Stalled task: {task}")
                logger.info(f"🏁 Exit with completed={len(self.completed_tasks)} stalled={len(self.stalled_tasks)} tasks, 0 pending tasks")
                enrollment_db_operations(EnrollmentDBOperation.CLEAR_ALL_TABLES)
                return 0

# 测试
if __name__ == "__main__":
    pass
