"""Parking lot queue system status management module.

This module provides status management functions for the parking lot registration system, including
1. Registration and execution of status handlers
2. Recording status processing history by Parker
3. Status log recording

Main components:
- log_parker_status: Decorator for colorlog status changes
- StatusManager: Core status processor responsible for status handling logic
"""
import traceback
from functools import wraps
from collections import defaultdict
from typing import Dict, Set, Callable, Optional, DefaultDict, cast

from karpark.colorlog import logger
from karpark.registrar.parker import <PERSON>
from karpark.registrar.entities import Status
from karpark.registrar.notification import Notification

from karpark.utils import Singleton

StatusHandler = Callable[[Parker], None]

def log_parker_status(func: StatusHandler) -> StatusHandler:
    """Decorator for colorlog Parker status changes.

    Args:
        func: Status handler function (Parker -> None)

    Returns:
        Decorated handler function that automatically logs status changes.
    """
    @wraps(func)
    def wrapper(parker: <PERSON>) -> None:
        if not isinstance(parker, <PERSON>):
            raise TypeError("Parameter must be of type <PERSON>")
        
        try:
            status_info = f"{Status(parker.current_status).value} (Position: {parker.queue_info.order}/{parker.queue_info.total})"
            logger.warning(f'Parker {parker}\'s status changed, new status: {status_info}')
            return func(parker)
        except Exception as e:
            logger.error(f"Error in status handler: {e}; Stack: {traceback.format_exc()}")
            raise
            
    return cast(StatusHandler, wrapper)


@Singleton
class StatusManager:
    """Singleton manager for Parker status."""

    def __init__(self) -> None:
        self._handlers_by_status: Dict[Status, StatusHandler] = {}
        self._default_handler: Optional[StatusHandler] = None
        self._processed_status_by_parker: DefaultDict[Parker, Set[Status]] = defaultdict(set)
        self._initialized: bool = False
        self._initialize()

    def _initialize(self) -> None:
        """Initialize status handler functions."""
        # Avoid repeated initialization
        if self._initialized:
            logger.debug("StatusManager already initialized, skipping...")
            return
            
        def register_default_handler():
            def decorator(func: StatusHandler):
                self.bind_default_handler(func)
                return func
            return decorator

        def register_status_handler(status: Status):
            def decorator(func: StatusHandler):
                self.bind_status_handler(status, func)
                return func
            return decorator

        @log_parker_status
        @register_default_handler()
        def route_notification(parker: Parker) -> None:    # noqa: F811
            """Default handler that route notifications based on parker status."""

            if parker.current_status is Status.OPEN_STATIC:
                return  # nothing to be done
                
            # Send notification through Notification singleton
            Notification().send_notification(parker=parker)

        @log_parker_status
        @register_status_handler(Status.OPEN_DELETED)
        def example_function(parker: Parker) -> None:  # noqa: F811
            """Status specific handler example, don't do registration."""
            logger.debug(f"(Example) status specific handler is called: {parker}")

    def bind_default_handler(self, handler: StatusHandler) -> None:
        """Set the global default handler (executed for all statuses).
        
        Args:
            handler: Handler function to be executed for all statuses
        """
        if not handler:
            logger.error("Cannot set default handler: handler function cannot be None")
            return
            
        self._default_handler = handler
        logger.debug(f"Default handler set: {handler.__name__}")

    def bind_status_handler(self, status: Status, handler: StatusHandler) -> None:
        """Register a status handler for a specific status.

        Args:
            status: Status identifier
            handler: Handler function
        """
        if not all([status, handler]):
            logger.error("Cannot bind handler: status or handler function cannot be empty")
            return
            
        if existing := self._handlers_by_status.get(status):
            logger.debug(f"Status {status.value} is already bound to handler: {existing.__name__}")
            return
            
        self._handlers_by_status[status] = handler
        logger.debug(f"Handler [{handler.__name__}] assigned to status {status.value}")

    def process_status_of_parker(self, parker_on_duty: Parker) -> None:
        """Process Parker status.

        Executes the corresponding handler function according to the current status of the Parker.

        Args:
            parker_on_duty: Parker instance
        """
        if not parker_on_duty:
            logger.error("Cannot process parker status: parker is None")
            return

        # Ensure status manager is initialized
        if not self._initialized:
            self._initialize()

        current_status = parker_on_duty.current_status

        # Always run default handler
        if self._default_handler:
            self._run_handler(self._default_handler, parker_on_duty)

        # Run status-specific handler if status has changed or is new
        if handler := self._handlers_by_status.get(current_status):
            self._run_handler(handler, parker_on_duty)
        else:
            logger.info(f"Status: {Status(current_status).value}, no custom handler available")

    def _run_handler(self, handler: StatusHandler, parker: Parker) -> None:
        """Execute status handler and track processing history.
        
        Args:
            handler: Status handler function to execute
            parker: Target Parker instance requiring status processing

        Note:
            Records the original status before handler execution to handle
            potential status changes during processing
        """
        status = parker.current_status
        try:
            handler(parker)
            logger.info(f"Executed handler {handler.__name__} for {parker}\'s status {status.value}")
            # Record triggered status
            self._processed_status_by_parker[parker].add(status)
            logger.debug(
                f"Status {status.value} recorded for {parker}"
            )
            
        except Exception as e:
            logger.error(
                f"Handler failure: {handler.__name__} | Parker: {parker} | "
                f"Error: {str(e).splitlines()[0]}"
            )
            logger.debug(f"Full exception: {traceback.format_exc()}")

    def get_processed_statuses(self, parker: Parker) -> Set[Status]:
        """Get all processed statuses for a specific Parker.
        
        Args:
            parker: Parker instance
            
        Returns:
            Set of processed Status objects
        """
        return self._processed_status_by_parker.get(parker, set())
        
    def has_processed_status(self, parker: Parker, status: Status) -> bool:
        """Check if a specific status has been processed for a Parker.
        
        Args:
            parker: Parker instance
            status: Status to check
            
        Returns:
            True if the status has been processed, False otherwise
        """
        return status in self._processed_status_by_parker.get(parker, set())
        
    def clear_parker_history(self, parker: Parker) -> None:
        """Clear the processing history for a specific Parker.
        
        Args:
            parker: Parker instance
        """
        if parker in self._processed_status_by_parker:
            del self._processed_status_by_parker[parker]
            logger.debug(f"Cleared processing history for Parker: {parker}")
