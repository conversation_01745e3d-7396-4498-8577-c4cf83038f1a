#!/usr/bin/env python3
"""
Comprehensive database operations for enrollment management.
Merged from karpark/database/db_operations.py (non-processed_emails parts) and extended CLI operations.
"""

import json
import sqlite3
from pathlib import Path
from enum import Enum, auto
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple, List, Dict, Any, Union

from karpark.common.paths import paths
from karpark.common.const import RegObject

from karpark.utils import Auto<PERSON>ame<PERSON><PERSON>
from karpark.registrar.parker import Parker
from karpark.registrar.magic_plate import restore_plate_variant

# Constants
DB_PATH = Path(paths.data_dir) / 'sqlite' / 'karpark.db'
DEFAULT_RETENTION_DAYS = 30

class EnrollmentDBOperation(AutoNameEnum):
    """Database operation types"""
    GET_ALL_ENROLLMENT = auto()
    GET_DELTA__ENROLLMENT = auto()
    CLEAR_ALL_TABLES = auto()

# Database connection management
@contextmanager
def get_db_connection():
    """
    Context manager for database connections.

    Yields:
        sqlite3.Connection: Database connection object
    """
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        yield conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            conn.close()

# Database initialization
def init_db() -> None:
    """Initialize the database and create necessary tables if they don't exist."""
    _ensure_db_path_exists()
    _create_tables()

def _ensure_db_path_exists() -> None:
    """Ensure the database directory and file exist."""
    try:
        DB_PATH.parent.mkdir(parents=True, exist_ok=True)
        if not DB_PATH.exists():
            DB_PATH.touch()
    except (OSError, IOError) as e:
        print(f"Error creating database path: {e}")

def _create_tables() -> None:
    """Create all required database tables."""
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            # Enrollments table
            c.execute('''CREATE TABLE IF NOT EXISTS enrollments
                        (ee_id TEXT PRIMARY KEY,
                         ee_name TEXT,
                         cookie TEXT,
                         plate_number TEXT,
                         registration_location TEXT,
                         use_magic_plate BOOLEAN,
                         is_withdrawn BOOLEAN,
                         emails TEXT,
                         created_time TEXT,
                         updated_time TEXT)''')

            # Cookie owner table
            c.execute('''CREATE TABLE IF NOT EXISTS cookie_owner
                        (cookie TEXT PRIMARY KEY,
                         ee_id TEXT,
                         is_valid BOOLEAN,
                         order_value INTEGER DEFAULT 0,
                         total_value INTEGER DEFAULT 0,
                         register_time TEXT,
                         created_time TEXT,
                         updated_time TEXT)''')

            # Delta enrollment state table
            c.execute('''CREATE TABLE IF NOT EXISTS delta_enrollment_state
                         (id INTEGER PRIMARY KEY CHECK (id = 1),
                          last_fetch_time TEXT)''')
            c.execute('''INSERT OR IGNORE INTO delta_enrollment_state
                         (id, last_fetch_time) VALUES (1, ?)''',
                      (datetime.min.replace(tzinfo=timezone.utc).isoformat(),))

            conn.commit()
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")

# Initialize database on module import
init_db()

# Enrollment operations
def db_save_enrollment(
    cookie: str,
    ee_id: str,
    ee_name: str,
    plate_number: str,
    registration_location: str,
    use_magic_plate: int,
    is_withdrawn: int,
    emails: List[str] = None
) -> bool:
    """
    Save or update an enrollment record in the database.

    Args:
        cookie: User's cookie
        ee_id: Employee ID
        ee_name: Employee name
        plate_number: Vehicle registration number
        registration_location: Parking location
        use_magic_plate: Whether multiple registrations are allowed
        is_withdrawn: Whether the enrollment is withdrawn
        emails: List of email addresses for notifications

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        emails_json = json.dumps(emails) if emails else '[]'

        with get_db_connection() as conn:
            c = conn.cursor()
            current_time = datetime.now(timezone.utc).isoformat()

            # Check if record exists and if data has changed
            current_record = c.execute(
                '''SELECT cookie, ee_name, plate_number, registration_location,
                          use_magic_plate, is_withdrawn, emails, updated_time
                   FROM enrollments WHERE ee_id = ?''',
                (ee_id,)
            ).fetchone()

            # Determine if update is needed
            if _is_enrollment_unchanged(current_record, cookie, ee_name, plate_number,
                                      registration_location, use_magic_plate,
                                      is_withdrawn, emails_json):
                updated_time = current_record['updated_time']
            else:
                updated_time = current_time

            # Insert or update record
            c.execute(
                '''INSERT INTO enrollments
                   (ee_id, ee_name, cookie, plate_number, registration_location,
                    use_magic_plate, is_withdrawn, emails, created_time, updated_time)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                   ON CONFLICT(ee_id) DO UPDATE SET
                       cookie = excluded.cookie,
                       ee_name = excluded.ee_name,
                       plate_number = excluded.plate_number,
                       registration_location = excluded.registration_location,
                       use_magic_plate = excluded.use_magic_plate,
                       is_withdrawn = excluded.is_withdrawn,
                       emails = excluded.emails,
                       updated_time = excluded.updated_time''',
                (ee_id, ee_name, cookie, plate_number, registration_location,
                 int(use_magic_plate), int(is_withdrawn), emails_json,
                 current_time if not current_record else None, updated_time)
            )
            conn.commit()
            return True
    except sqlite3.Error as e:
        print(f"Error saving enrollment: {e}")
        return False

def _is_enrollment_unchanged(current_record, cookie, ee_name, plate_number,
                           registration_location, use_magic_plate,
                           is_withdrawn, emails_json):
    """Check if enrollment data has changed compared to current record."""
    return (current_record and
            current_record['cookie'] == cookie and
            current_record['ee_name'] == ee_name and
            current_record['plate_number'] == plate_number and
            current_record['registration_location'] == registration_location and
            current_record['use_magic_plate'] == int(use_magic_plate) and
            current_record['is_withdrawn'] == int(is_withdrawn) and
            current_record['emails'] == emails_json)

def get_latest_enrollment(ee_id: str) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str], bool, Optional[List[str]]]:
    """
    Get the latest non-withdrawn enrollment record for the specified employee.

    Args:
        ee_id: Employee ID

    Returns:
        tuple: (ee_id, ee_name, plate_number, registration_location, use_magic_plate, emails)
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute(
                '''SELECT ee_id, ee_name, plate_number, registration_location,
                          use_magic_plate, emails
                   FROM enrollments
                   WHERE ee_id = ? AND is_withdrawn = 0
                   ORDER BY created_time DESC LIMIT 1''',
                (ee_id,)
            )
            result = c.fetchone()

            if result:
                emails = json.loads(result['emails']) if result['emails'] else []
                return (
                    result['ee_id'],
                    result['ee_name'],
                    result['plate_number'],
                    result['registration_location'],
                    bool(result['use_magic_plate']),
                    emails
                )
            else:
                print(f"No enrollment found for EE ID: {ee_id}")
                return (None, None, None, None, False, None)
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return (None, None, None, None, False, None)

def get_all_enrollments_detailed() -> List[Dict[str, Any]]:
    """
    Get all enrollment records with full details including withdrawn ones.
    
    Returns:
        list: List of dictionaries containing full enrollment information
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute(
                '''SELECT ee_id, ee_name, cookie, plate_number, registration_location,
                          use_magic_plate, is_withdrawn, emails, created_time, updated_time
                   FROM enrollments 
                   ORDER BY updated_time DESC'''
            )
            results = c.fetchall()
            
            return [_format_detailed_enrollment_record(row) for row in results]
    except sqlite3.Error as e:
        print(f"Error getting detailed enrollments: {e}")
        return []

def get_delta_enrollments() -> List[Dict[str, Any]]:
    """
    Get enrollments that have changed since the last query and update last_fetch_time.

    Returns:
        list: List of dictionaries containing enrollment information
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()

            # Get last query time
            last_fetch_time = _get_last_fetch_time(c)

            # Query changed enrollments
            c.execute(
                '''SELECT ee_id, cookie, plate_number, use_magic_plate,
                          is_withdrawn, emails, updated_time
                   FROM enrollments
                   WHERE updated_time > ?''',
                (last_fetch_time,)
            )
            results = c.fetchall()

            # Update last_fetch_time to current time
            _update_last_fetch_time(c, conn)

            return [_format_enrollment_record(row) for row in results]
    except sqlite3.Error as e:
        print(f"Error getting delta enrollments: {e}")
        return []

def get_all_enrollments() -> List[Dict[str, Any]]:
    """
    Get all valid non-withdrawn enrollment records.

    Returns:
        list: List of dictionaries containing enrollment information
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute(
                '''SELECT ee_id, cookie, plate_number, use_magic_plate,
                          is_withdrawn, emails, updated_time
                   FROM enrollments
                   WHERE is_withdrawn = 0'''
            )
            results = c.fetchall()

            # Update last_fetch_time to current time
            _update_last_fetch_time(c, conn)

            return [_format_enrollment_record(row) for row in results]
    except sqlite3.Error as e:
        print(f"Error getting all enrollments: {e}")
        return []

def _get_last_fetch_time(cursor):
    """Get the last fetch time from delta_enrollment_state table."""
    cursor.execute('SELECT last_fetch_time FROM delta_enrollment_state WHERE id = 1')
    row = cursor.fetchone()
    return row['last_fetch_time'] if row else datetime.min.replace(tzinfo=timezone.utc).isoformat()

def _update_last_fetch_time(cursor, conn):
    """Update the last fetch time to current time."""
    current_time = datetime.now(timezone.utc).isoformat()
    cursor.execute('UPDATE delta_enrollment_state SET last_fetch_time = ? WHERE id = 1', (current_time,))
    conn.commit()

def _format_enrollment_record(row):
    """Format a database row into an enrollment dictionary."""
    return {
        'ee_id': row['ee_id'],
        'cookie': row['cookie'],
        'plate_number': row['plate_number'],
        'use_magic_plate': bool(row['use_magic_plate']),
        'is_withdrawn': bool(row['is_withdrawn']),
        'emails': json.loads(row['emails']) if row['emails'] else [],
        'updated_time': row['updated_time']
    }

# Cookie owner operations
def save_cookie_owner(cookie: str, ee_id: str, order: int = 0, total: int = 0, register_time: str = None) -> bool:
    """
    Save (UPSERT) user's cookie and eeId to the database.

    Args:
        cookie: User's cookie
        ee_id: Employee ID
        order: User's position in queue (default: 0)
        total: Total number in queue (default: 0)
        register_time: Registration time (default: None)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            current_time = datetime.now(timezone.utc).isoformat()

            # Check if record exists and if data has changed
            current_record = c.execute(
                '''SELECT ee_id, is_valid, order_value, total_value, register_time, updated_time
                   FROM cookie_owner
                   WHERE cookie = ?''',
                (cookie,)
            ).fetchone()

            # Determine if update is needed
            if (current_record and
                current_record['ee_id'] == ee_id and
                current_record['is_valid'] == 1 and
                current_record['order_value'] == order and
                current_record['total_value'] == total and
                current_record['register_time'] == register_time):
                updated_time = current_record['updated_time']
            else:
                updated_time = current_time

            # Insert or update record
            c.execute(
                '''INSERT INTO cookie_owner
                   (cookie, ee_id, is_valid, order_value, total_value, register_time, created_time, updated_time)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                   ON CONFLICT(cookie) DO UPDATE SET
                       ee_id = excluded.ee_id,
                       is_valid = excluded.is_valid,
                       order_value = excluded.order_value,
                       total_value = excluded.total_value,
                       register_time = excluded.register_time,
                       updated_time = excluded.updated_time''',
                (cookie, ee_id, 1, order, total, register_time,
                 current_time if not current_record else None, updated_time)
            )
            conn.commit()
            return True
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False

def get_cookie_owner(cookie: str) -> Tuple[str, Optional[str], int, int, Optional[str]]:
    """
    Get the owner of a cookie and its associated data.

    Args:
        cookie: User's cookie

    Returns:
        tuple: (cookie, ee_id, order, total, register_time)
    """
    try:
        with get_db_connection() as conn:
            # Mark old records as invalid
            mark_old_records_invalid(conn)

            # Get a valid record for the specified cookie
            c = conn.cursor()
            c.execute(
                '''SELECT ee_id, order_value, total_value, register_time
                   FROM cookie_owner
                   WHERE cookie = ? AND is_valid = 1
                   ORDER BY updated_time DESC LIMIT 1''',
                (cookie,)
            )
            result = c.fetchone()

            if result:
                return (cookie, result['ee_id'], result['order_value'], result['total_value'], result['register_time'])
            else:
                return (cookie, None, 0, 0, None)
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return (cookie, None, 0, 0, None)

def mark_old_records_invalid(conn: sqlite3.Connection) -> None:
    """
    Mark records older than 30 minutes as invalid.

    Args:
        conn: Database connection
    """
    try:
        cutoff_time = (datetime.now(timezone.utc) - timedelta(minutes=30)).isoformat()
        conn.execute(
            '''UPDATE cookie_owner
               SET is_valid = 0
               WHERE is_valid = 1 AND updated_time < ?''',
            (cutoff_time,)
        )
        conn.commit()
    except sqlite3.Error as e:
        print(f"Error marking old records as invalid: {e}")

def get_enrollment_by_ee_id(ee_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a specific enrollment by ee_id.
    
    Args:
        ee_id: Employee ID
        
    Returns:
        dict: Enrollment record or None if not found
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute(
                '''SELECT ee_id, ee_name, cookie, plate_number, registration_location,
                          use_magic_plate, is_withdrawn, emails, created_time, updated_time
                   FROM enrollments 
                   WHERE ee_id = ?''',
                (ee_id,)
            )
            result = c.fetchone()
            
            if result:
                return _format_detailed_enrollment_record(result)
            return None
    except sqlite3.Error as e:
        print(f"Error getting enrollment by ee_id: {e}")
        return None


def update_enrollment(ee_id: str, **kwargs) -> bool:
    """
    Update specific fields of an enrollment record and sync cookie_owner table.

    Args:
        ee_id: Employee ID
        **kwargs: Fields to update

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()

            # Get current record
            current_record = c.execute(
                '''SELECT * FROM enrollments WHERE ee_id = ?''',
                (ee_id,)
            ).fetchone()

            if not current_record:
                print(f"No enrollment found for ee_id: {ee_id}")
                return False

            # Build update query dynamically
            update_fields = []
            update_values = []
            updated_cookie = None

            for field, value in kwargs.items():
                if field in ['ee_name', 'cookie', 'plate_number', 'registration_location']:
                    update_fields.append(f"{field} = ?")
                    update_values.append(value)
                    if field == 'cookie':
                        updated_cookie = value
                elif field in ['use_magic_plate', 'is_withdrawn']:
                    update_fields.append(f"{field} = ?")
                    update_values.append(int(value))
                elif field == 'emails':
                    update_fields.append("emails = ?")
                    update_values.append(json.dumps(value) if value else '[]')

            if not update_fields:
                print("No valid fields to update")
                return False

            # Add updated_time
            update_fields.append("updated_time = ?")
            update_values.append(datetime.now(timezone.utc).isoformat())
            update_values.append(ee_id)

            query = f"UPDATE enrollments SET {', '.join(update_fields)} WHERE ee_id = ?"
            c.execute(query, update_values)
            conn.commit()

            # Update cookie_owner table if cookie was changed
            if updated_cookie:
                save_cookie_owner(updated_cookie, ee_id)
                print(f"Cookie owner updated for ee_id: {ee_id}")

            return True
    except sqlite3.Error as e:
        print(f"Error updating enrollment: {e}")
        return False


def delete_enrollment(ee_id: str) -> bool:
    """
    Delete an enrollment record.
    
    Args:
        ee_id: Employee ID
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('DELETE FROM enrollments WHERE ee_id = ?', (ee_id,))
            conn.commit()
            
            if c.rowcount > 0:
                print(f"Enrollment {ee_id} deleted successfully")
                return True
            else:
                print(f"No enrollment found for ee_id: {ee_id}")
                return False
    except sqlite3.Error as e:
        print(f"Error deleting enrollment: {e}")
        return False


def _format_detailed_enrollment_record(row) -> Dict[str, Any]:
    """Format a database row into a detailed enrollment dictionary."""
    return {
        'ee_id': row['ee_id'],
        'ee_name': row['ee_name'],
        'cookie': row['cookie'],
        'plate_number': row['plate_number'],
        'registration_location': row['registration_location'],
        'use_magic_plate': bool(row['use_magic_plate']),
        'is_withdrawn': bool(row['is_withdrawn']),
        'emails': json.loads(row['emails']) if row['emails'] else [],
        'created_time': row['created_time'],
        'updated_time': row['updated_time']
    }


def get_all_cookie_owners() -> List[Dict[str, Any]]:
    """
    Get all cookie owner records.

    Note: This function returns all records (both valid and invalid).
    The mark_old_records_invalid function is called by get_cookie_owner
    when needed, so we don't need to call it here again.

    Returns:
        list: List of dictionaries containing cookie owner information
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute(
                '''SELECT cookie, ee_id, is_valid, order_value, total_value, register_time, created_time, updated_time
                   FROM cookie_owner
                   ORDER BY updated_time DESC'''
            )
            results = c.fetchall()

            return [_format_cookie_owner_record(row) for row in results]
    except sqlite3.Error as e:
        print(f"Error getting cookie owners: {e}")
        return []





def delete_cookie_owner(cookie: str) -> bool:
    """
    Delete a cookie owner record.

    Args:
        cookie: Cookie string to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('DELETE FROM cookie_owner WHERE cookie = ?', (cookie,))
            conn.commit()

            if c.rowcount > 0:
                print(f"Cookie owner deleted successfully")
                return True
            else:
                print(f"No cookie owner found for cookie: {cookie[:50]}...")
                return False
    except sqlite3.Error as e:
        print(f"Error deleting cookie owner: {e}")
        return False





def _format_cookie_owner_record(row) -> Dict[str, Any]:
    """Format a database row into a cookie owner dictionary."""
    return {
        'cookie': row['cookie'],
        'ee_id': row['ee_id'],
        'is_valid': bool(row['is_valid']),
        'order': row['order_value'],
        'total': row['total_value'],
        'register_time': row['register_time'],
        'created_time': row['created_time'],
        'updated_time': row['updated_time']
    }


def fetch_employee_info(cookie: str) -> Dict[str, Any]:
    """
    Temporary function to extract enrollment data from cookie.
    This will be replaced with actual implementation later.

    Args:
        cookie: User's cookie string

    Returns:
        dict: Extracted enrollment data
    """
    # Placeholder implementation
    parker = Parker(cookie, plate_number='沪PH1234', use_magic_plate=False, emails=[])

    return {
        'ee_id': parker.queue_info.ee_id,
        'ee_name': parker.queue_info.ee_name,
        'plate_number': restore_plate_variant(parker.queue_info.car_no),
        'registration_location': RegObject.LOCATION,
        'use_magic_plate': False,
        'is_withdrawn': False,
        'emails': []
    }

# Database maintenance
def clear_all_enrollment_tables() -> None:
    """
    Clear all data from tables created by init_db while preserving the table structure.
    """
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('DELETE FROM enrollments')
            c.execute('DELETE FROM cookie_owner')
            c.execute('DELETE FROM delta_enrollment_state')

            # Reset delta_enrollment_state to initial value
            c.execute(
                'INSERT OR REPLACE INTO delta_enrollment_state (id, last_fetch_time) VALUES (1, ?)',
                (datetime.min.replace(tzinfo=timezone.utc).isoformat(),)
            )
            conn.commit()
        print("All tables cleared successfully")
    except sqlite3.Error as e:
        print(f"Error clearing tables: {e}")

# Unified database operations wrapper
def enrollment_db_operations(operation: Enum) -> Union[List[Dict[str, Any]], None]:
    """
    Wrapper function for common database operations.

    Args:
        operation: Operation type (use EnrollmentDBOperation enum values)

    Returns:
        List of enrollment dictionaries or None for clear operation
    """
    if operation == EnrollmentDBOperation.GET_ALL_ENROLLMENT:
        return get_all_enrollments()
    elif operation == EnrollmentDBOperation.GET_DELTA__ENROLLMENT:
        return get_delta_enrollments()
    elif operation == EnrollmentDBOperation.CLEAR_ALL_TABLES:
        clear_all_enrollment_tables()
        return None
    else:
        print(f"Unknown operation: {operation}")
        return None
