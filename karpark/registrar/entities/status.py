from typing import Optional

from karpark.utils import AutoNameEnum, auto


# 基础状态枚举
class CloseStatus(AutoNameEnum):
    CLOSED = auto()
    OPEN = auto()
    ERROR = auto()

class MoveStatus(AutoNameEnum):
    STATIC = auto()
    MOVING = auto()
    DELETED = auto()
    ERROR = auto()

class RegistrationStatus(AutoNameEnum):
    REG_SUCCESS = auto()
    REG_FAILURE = auto()
    ERROR = auto()

# 组合状态枚举（新增INITIAL）
class Status(AutoNameEnum):
    ERROR = auto()
    INITIAL = auto()       # 新增的初始状态
    # Close + Queue 组合状态
    CLOSED_STATIC = auto()
    CLOSED_MOVING = auto()
    CLOSED_DELETED = auto()
    OPEN_STATIC = auto()
    OPEN_MOVING = auto()
    OPEN_DELETED = auto()
    # 注册状态（减少为2种）
    REG_SUCCESS = auto()
    REG_FAILURE = auto()

    @classmethod
    def from_components(cls,
                        close_status: Optional[CloseStatus] = None,
                        move_status: Optional[MoveStatus] = None,
                        reg_status: Optional[RegistrationStatus] = None):
        # 处理初始状态
        if all(v is None for v in [close_status, move_status, reg_status]):
            return cls.INITIAL

        # 优先处理注册状态
        error_states = [s for s in [close_status, move_status, reg_status] 
                      if s and s._name_ == 'ERROR']
        if error_states:
            return cls.ERROR
        
        if reg_status is not None:
            return cls[reg_status.name]
        
        # 处理组合状态
        if close_status and move_status:
            return cls[f"{close_status.name}_{move_status.name}"]
        
        # 无效状态组合返回初始状态（可根据需求改为抛出异常）
        return cls.INITIAL

# 状态管理器（增加初始状态处理）
class StatusDeterminator:
    def __init__(self, parker_instance):
        self._close_status = None
        self._move_status = None
        self._reg_status = None
        self.parker = parker_instance
    
    def refresh_status(self):
        """带初始状态保护的状态刷新"""
        try:
            self._close_status = self._get_close_status()
            self._move_status = self._get_move_status()
            self._reg_status = self._get_reg_status()
        except Exception as e:
            # 状态获取失败时重置为初始状态
            print(f"状态刷新失败: {str(e)}")
            self._close_status = None
            self._move_status = None
            self._reg_status = None
    
    @property
    def current_status(self) -> Status:
        """智能状态解析"""
        self.refresh_status()
        return Status.from_components(
            self._close_status,
            self._move_status,
            self._reg_status
        )

    # 模拟状态获取方法（可自定义实现）
    def _get_close_status(self) -> CloseStatus:
        if not self.parker.queue_info.has_required_fields():
            return CloseStatus.ERROR
        return CloseStatus.OPEN if self.parker.queue_info.has_no_error() else CloseStatus.CLOSED

    def _get_move_status(self) -> MoveStatus:
        if not self.parker.queue_info.has_required_fields():
            return MoveStatus.ERROR
        if self.parker.has_been_deleted():
            return MoveStatus.DELETED
        if self.parker.has_queue_position_improved():
            return MoveStatus.MOVING
        return MoveStatus.STATIC

    def _get_reg_status(self) -> Optional[RegistrationStatus]:
        if not self.parker.queue_info.has_required_fields():
            return RegistrationStatus.ERROR
        
        if self.parker.is_registration_valid():
            return RegistrationStatus.REG_SUCCESS

        if self.parker.is_registration_attempt_failed():
            return RegistrationStatus.REG_FAILURE

        if self.parker.has_been_deleted():
            return None

        return None


# 使用示例
if __name__ == "__main__":
    pass