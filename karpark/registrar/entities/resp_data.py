#!/usr/bin/env python3
import json
from datetime import datetime
from karpark.common.const import Json<PERSON>ield<PERSON>eys, ErrorCode


class Error:
    def __init__(self, err: dict | None = None) -> None:
        err = err if err else {}
        self.error_code = err.get(JsonFieldKeys.ERROR_CODE.value)
        self.error_message = err.get(JsonFieldKeys.ERROR_MESSAGE.value)


class RespData:
    def __init__(self, res: dict = None) -> None:
        res = res if res else {}
        self.id = res.get(JsonFieldKeys.ID.value)
        self.ee_id = res.get(JsonFieldKeys.EE_ID.value)
        self.ee_name = res.get(JsonFieldKeys.EE_NAME.value)

        # getEntry,saveEntry成功(哥德巴赫猜想，需要写log验证)
        if JsonFieldKeys.CAR_NO.value in res:
            self.car_no = res.get(JsonFieldKeys.CAR_NO.value)

        # postEntry失败
        if JsonFieldKeys.CAR_PLATE_NUMBER.value in res:
            self.car_plate_number = res.get(JsonFieldKeys.CAR_PLATE_NUMBER.value)

        self.location = res.get(JsonFieldKeys.LOCATION.value)
        self.order = res.get(JsonFieldKeys.ORDER.value)
        self.total = res.get(JsonFieldKeys.TOTAL.value)

        self.error = Error(res.get(JsonFieldKeys.ERROR.value))

        _register_time = res.get(JsonFieldKeys.TIME_STAMP.value)
        self.register_time = datetime.fromisoformat(_register_time) if _register_time else None # aware datetime

        _time_stamp = res.get(JsonFieldKeys.TIME_STAMP.value)
        self.time_stamp = datetime.fromisoformat(_time_stamp) if _time_stamp else None  # aware datetime

        self.status = res.get(JsonFieldKeys.STATUS.value)

    def has_no_error(self) -> bool:
        """检查响应是否没有错误（错误代码为空）

        Returns:
            bool: 如果响应没有错误则返回True，否则返回False
        """
        return getattr(self, 'error', None) is None or self.error.error_code is None or self.error.error_code == ""

    def has_closed_error(self) -> bool:
        """检查响应是否包含已关闭的错误（错误代码 40005）

        Returns:
            bool: 如果响应包含已关闭的错误则返回True，否则返回False
        """
        return getattr(self, 'error', None) is not None and self.error.error_code == ErrorCode.CLOSED.value

    def contains_error(self) -> bool:
        """检查响应是否包含错误（错误代码不为空）

        Returns:
            bool: 如果响应包含错误则返回True，否则返回False
        """
        return getattr(self, 'error', None) is not None and self.error.error_code is not None and self.error.error_code != ""

    def is_duplicate_error(self) -> bool:
        """检查响应是否包含重复车牌号错误（错误代码 40004）
        
        Returns:
            bool: 如果响应包含重复车牌号错误则返回True，否则返回False
        """
        return getattr(self, 'error', None) is not None and self.error.error_code == ErrorCode.DUPLICATE_CAR.value

    def has_required_fields(self) -> bool:
        """Check if the response contains the required fields (ee_id and ee_name are not None)

        Returns:
            bool: True if both ee_id and ee_name are not None, False otherwise
        """
        return self.ee_id is not None and self.ee_name is not None


def print_header(title: str, subtitle: str = '') -> None:
    header = f' {title} '.center(50, '=')
    print(f'\n{header}')
    if subtitle:
        print(f'* {subtitle}')
        print('-' * 50)


if __name__ == "__main__":
    from pprint import pprint

    # Test case 1: getEntry
    print_header('TEST CASE 1', 'getEntry')
    example_get_res_one = """{"id":null,"eeName":"LIU, Tao","eeId":"I888888","location":"PVG06","carNo":"沪A88A88",
    "registerTime":"2024-06-07T07:31:19.010+00:00","timeStamp":"2024-06-07T07:31:19.010+00:00","error":null,
    "order":291,"total":486,"status":"Waiting"}"""

    example_dict = json.loads(example_get_res_one)
    example_resp_data = RespData(example_dict)

    print('RespData instance attributes:')
    pprint(example_resp_data.__dict__, indent=2, width=100)
    print(f'\n✅ Is error-free: {example_resp_data.has_no_error()}')

    # Test case 2: Duplicate car plate error
    print_header('TEST CASE 2', 'Duplicate Car Plate Error')
    example_post_error_response = """{"id":null,"eeName":"LIU, Tao","eeId":"I888888","location":"PVG06",
    "carPlateNumber":"沪A01B02","registerTime":null,"comment":"","status":"Waiting",
    "error":{"errorCode":"40004","errorMessage":"Car Plate Number 沪A01B02 was already registered by I000000, please confirm with administrator."}}"""

    example_dict = json.loads(example_post_error_response)
    example_resp_data = RespData(example_dict)

    print('📦 Response Data:')
    pprint(example_resp_data.__dict__, indent=2, width=100)

    print('\n❌ Error Details:')
    pprint(example_resp_data.error.__dict__, indent=2, width=100)

    # 测试用例1修改后
    print(f'\n✅ Has no errors check: {example_resp_data.has_no_error()}')
    
    # 测试用例2修改后 
    print(f'🚨 Duplicate car check: {example_resp_data.is_duplicate_error()}')

    print_header('END OF TESTS')
