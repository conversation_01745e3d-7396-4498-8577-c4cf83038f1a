#!/usr/bin/env python3
from karpark.common.config import ServiceURL

common_header: dict = {
    # Client
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9",
    "X-Requested-With": "XMLHttpRequest",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b25) NetType/WIFI Language/zh_CN",
    # Transport
    "Host": ServiceURL.domain,
    "Connection": "keep-alive",
    # Security
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    # Miscellaneous
    "Referer": ServiceURL.full_url("/regist"),
    "sec-ch-ua": '"Chromium";v="107"',
    "sec-ch-ua-mobile": "?0",
}


def create_headers(method, cookie):
    _method = method.__str__().strip().upper()
    if _method == "GET":
        return _create_for_get(cookie)
    elif _method == "POST":
        return _create_for_post(cookie)
    # default
    return _create_for_get(cookie)


def _create_for_get(cookies):
    headers_get = common_header.copy()
    headers_get["Cookie"] = "; ".join(cookies) if isinstance(cookies, list) else cookies
    headers_get["Accept"] = "*/*"
    return headers_get


def _create_for_post(cookies):
    headers_post = common_header.copy()
    headers_post["Cookie"] = "; ".join(cookies) if isinstance(cookies, list) else cookies
    headers_post["Accept"] = "application/json"
    headers_post["Content-Type"] = "application/json; charset=utf-8"
    return headers_post


if __name__ == "__main__":
    # Print headers for GET request
    print("GET Headers:")
    print(create_headers("GET", "test_cookie"))
    # Print headers for POST request
    print("\nPOST Headers:")
    print(create_headers("POST", "test_token"))
