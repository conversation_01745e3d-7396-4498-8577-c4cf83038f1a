#!/usr/bin/env python3
import json
from typing import Optional
from karpark.common.const import JsonFieldKeys
from dataclasses import dataclass, field

# Default constants
COMMENT = ""
WAITING = "WAITING"
LOCATION = "PVG 06"  # Only supports PVG06


@dataclass
class PostData:
    """Data class for sending requests"""
    ee_id: str  # Employee ID
    ee_name: str  # Employee Name
    car_no: str  # Actual license plate to register (could be magic plate)
    id: Optional[str] = None  # Unique identifier
    location: str = field(default=LOCATION)  # Parking location
    comment: str = field(default=COMMENT)  # Remarks
    register_time: Optional[str] = None  # Registration time
    status: str = field(default=WAITING)  # Status

    def as_json(self) -> bytes:
        """Convert object to JSON-formatted bytes"""
        _dict = {
            JsonFieldKeys.ID.value: self.id,
            JsonFieldKeys.EE_ID.value: self.ee_id,
            JsonFieldKeys.EE_NAME.value: self.ee_name,
            JsonFieldKeys.LOCATION.value: self.location,
            JsonFieldKeys.CAR_NO.value: self.car_no,
            JsonFieldKeys.COMMENT.value: self.comment,
            JsonFieldKeys.REGISTER_TIME.value: self.register_time,
            JsonFieldKeys.STATUS.value: self.status.upper(),
        }
        # 转换为JSON字符串并编码为UTF-8字节
        return json.dumps(_dict, ensure_ascii=False).encode("utf8")

    @classmethod
    def from_json(cls, data: bytes) -> "PostData":
        """Create PostData instance from JSON bytes"""
        _dict = json.loads(data.decode("utf8"))
        return cls(
            ee_id=_dict[JsonFieldKeys.EE_ID.value],
            ee_name=_dict[JsonFieldKeys.EE_NAME.value],
            car_no=_dict[JsonFieldKeys.CAR_NO.value],
            id=_dict.get(JsonFieldKeys.ID.value),
            location=_dict.get(JsonFieldKeys.LOCATION.value, LOCATION),
            comment=_dict.get(JsonFieldKeys.COMMENT.value, COMMENT),
            register_time=_dict.get(JsonFieldKeys.REGISTER_TIME.value),
            status=_dict.get(JsonFieldKeys.STATUS.value, WAITING)
        )
    
    @classmethod
    def from_request(cls, request) -> "PostData":
        """Create PostData instance from request object"""
        return cls.from_json(request.body)


if __name__ == "__main__":
    _my_post = PostData(ee_id="I888888", ee_name="LIU, Tao", car_no="云C56789")
    print(f"Bytes to be posted: {_my_post.as_json()}")
