#!/usr/bin/env python3
"""Parking registration and queue management module.

This module provides functionality for managing parking registrations and queue positions
for the SAP Labs parking system.
"""

import requests
from typing import Optional, List, Union
from datetime import datetime, timedelta, timezone

from karpark.colorlog import logger, timeit
from karpark.utils import normalize_emails

from karpark.common.config import ParkerConfig
from karpark.registrar.magic_plate import create_plate_variants, restore_plate_variant
from karpark.registrar.entities import PostData, RespData, create_headers, Status, StatusDeterminator

class Parker:
    """Manages parking registration and queue position for a single vehicle.
    
    This class handles all interactions with the parking system, including
    - Queue information retrieval
    - Parking registration
    - Status tracking
    - Queue position monitoring
    """

    # ================ Initialization ================
    def __init__(self, cookie: str, plate_number: Optional[str] = None, use_magic_plate: Optional[bool] = False, emails: Optional[Union[List[str], str]] = None) -> None:
        """Initialize a Parker instance.
        
        Args:
            cookie: Session cookie for authentication
            plate_number: Vehicle's license plate number
            use_magic_plate: Whether to generate license plate variants for registration
            emails: Notification emails (list or comma/semicolon-separated string)
        """
        self.cookie = cookie
        self.plate_number = plate_number
        self.use_magic_plate = use_magic_plate
        self.emails = normalize_emails(emails)
        self.initial_order: Optional[int] = None
        self.queue_info = RespData()
        self.last_check_time: datetime = datetime.min.replace(tzinfo=timezone.utc)
        self.status_determinator = StatusDeterminator(self)
        self._registration_attempt_failed = False
        # Initialize queue information
        self._fetch_queue_info()

    # ================ Special Methods ================
    def __repr__(self) -> str:
        """Return an unambiguous string representation of the Parker instance."""
        return (
            f"Parker(cookie={self.cookie!r}, "
            f"plate_number={self.plate_number!r}, "
            f"use_magic_plate={self.use_magic_plate!r}), "
            f"emails={self.emails!r})"
        )

    def __str__(self) -> str:
        """Return a human-readable string representation of the Parker instance."""
        return f'#{self.plate_number}@{self.queue_info.ee_id}({self.queue_info.ee_name})#'

    def __hash__(self) -> int:
        """Use ee_id as the unique identifier for hashing."""
        return hash(self.queue_info.ee_id)

    def __eq__(self, other) -> bool:
        """Two Parker instances are considered equal if they have the same ee_id."""
        if not isinstance(other, Parker):
            return False
        return self.queue_info.ee_id == other.queue_info.ee_id

    # ================ Properties and Data Generation ================
    @property
    def current_status(self) -> Status:
        """Get current parking queue status through StatusDeterminator"""
        return self.status_determinator.current_status

    def update_init_parameters(self, cookie: str, plate_number: str, use_magic_plate: bool = False, emails: Optional[Union[List[str], str]] = None) -> None:
        """Update all initialization parameters for the Parker instance.

        Args:
            cookie: New session cookie value for authentication
            plate_number: New vehicle license plate number
            use_magic_plate: Whether to generate license plate variants for registration
            emails: Notification emails (list or comma/semicolon-separated string)
        """
        self.cookie = cookie
        self.plate_number = plate_number
        self.use_magic_plate = use_magic_plate
        self.emails = normalize_emails(emails)
        # Refresh queue info with new cookie
        self._fetch_queue_info()

    def generate_registration_data(self, plate_number: str) -> bytes:
        """Generate parking spot registration data with specified registration number.

         Args:
            plate_number: License plate number to use for registration
        """
        if not self.queue_info.has_required_fields():
            self._fetch_queue_info()
        return PostData(
            ee_id=self.queue_info.ee_id,
            ee_name=self.queue_info.ee_name,
            car_no=plate_number
        ).as_json()

    # ================ Update Methods ================
    def update_cookie(self, new_cookie: str) -> None:
        """Update the session cookie.
        
        Args:
            new_cookie: New session cookie value for authentication
        """
        self.cookie = new_cookie
        # Refresh queue info with new cookie
        self._fetch_queue_info()

    def update_plate_number(self, new_plate_number: str) -> None:
        """Update the vehicle registration number.
        
        Args:
            new_plate_number: New vehicle license plate number
        """
        self.plate_number = new_plate_number

    def update_use_magic_plate(self, use_magic_plate: bool) -> None:
        """Update whether to use magic plate number for registration.

        Args:
            use_magic_plate: Whether to generate license plate variants for registration
        """
        self.use_magic_plate = use_magic_plate

    # ================ Queue Information Methods ================

    def update_queue_info(self, resp: RespData) -> None:
        """Update queue information with new response data.
        
        Args:
            resp: New response data to update queue information
        """
        if not (resp.has_no_error() or resp.has_closed_error()):
            return

        self.queue_info = resp
        self.initial_order = self.initial_order if self.initial_order is not None else self.queue_info.order

    @timeit
    def fetch_queue_info(self) -> RespData:
        return self._fetch_queue_info()

    def _fetch_queue_info(self) -> RespData:
        """Fetch and update current queue information

        Workflow:
        1. Send HTTP GET request to retrieve latest queue data
        2. Process response based on status code
        3. Update instance's queue information
        4. Maintain last check timestamp

        Returns:
            RespData: Response data structure containing queue information
        """
        try:
            headers = create_headers("GET", self.cookie)
            response = requests.get(
                url=ParkerConfig.ApiEndpoints.entry_url,
                headers=headers,
                timeout=ParkerConfig.ServiceThresholds.request_timeout
            )


            if response.status_code == 200:
                logger.debug(f"HTTPS GET response {response.status_code}")
                self.last_check_time = datetime.now(timezone.utc)
                result = RespData(response.json())
            else:
                result = RespData()
                logger.error(f"HTTPS GET error {response.status_code}")

            self.update_queue_info(result)
            return result

        except Exception as e:
            logger.critical(f"Connection error: {e}")
            self.update_queue_info(RespData())
            return RespData()

    # ================ Registration Methods ================
    @timeit
    def _submit_registration(self, plate_number: str) -> RespData:
        """Submit parking registration to the server.
        !important! Re-fetch queue info due to unclear post-saveEntry response, prioritizing data reliability over performance

        Args:
            plate_number: License plate number to use for registration
        """

        try:
            headers = create_headers("POST", self.cookie)
            _post_resp = requests.post(
                url=ParkerConfig.ApiEndpoints.post_url,
                data=self.generate_registration_data(plate_number),
                headers=headers,
                timeout=ParkerConfig.ServiceThresholds.request_timeout
            )

            if _post_resp.status_code == 200:
                logger.debug(f"HTTPS POST response {_post_resp.status_code}")
                # !important! Re-fetch queue info due to unknown saveEntry response
                # !important! prioritizing data reliability over performance
                response = self._fetch_queue_info()
                return response
            else:
                logger.error(f"HTTPS POST response {_post_resp.status_code}")
                return RespData()
        except Exception as e:
            logger.critical(f"Submission error: {e}")
            return RespData()

    def _register_without_magic(self) -> bool:
        """Handle registration without magic number variation."""
        _ = self._submit_registration(self.plate_number)
        if self.is_registration_valid():
            logger.debug(f"{self} has successfully registered for parking!")
            return True
        else:
            logger.debug(f"{self} failed to register for parking! ")
            return False

    def _try_magic_variants(self) -> bool:
        """Attempt registration with all generated license plate variants.
        
        Generates all possible variants of the current plate number and tries
        to register with each until one succeeds.
        
        Returns:
            bool: True if any variant registration succeeded, False otherwise
        """
        variants = self._generate_variants()
        if not variants:
            logger.warning(f"No valid plate variants generated for {self}")
            return False

        logger.debug(f"Attempting registration with {len(variants)} plate variants")
        for variant in variants:
            if self._try_single_variant(variant):
                return True
                
        logger.debug(f"All {len(variants)} variants failed for {self}")
        return False

    def _generate_variants(self) -> list:
        """Generate all possible license plate variations.

        Returns:
            list: List of plate number variants, or [original_plate] on error
        """
        try:
            variants = create_plate_variants(self.plate_number)
            if not variants:
                logger.warning(f"Unable to generate any variants for {self}")
            return variants
        except Exception as e:
            logger.error(f"Error generating variants for {self}: {e}")
            # Fall back to original plate number on error
            return [self.plate_number]

    def _try_single_variant(self, variant: str) -> bool:
        """Try to register with a single license plate variant.
        
        Args:
            variant: The license plate variant to try
            
        Returns:
            bool: True if registration succeeded, False otherwise
        """
        logger.debug(f"Attempting registration with variant: {variant}")
        resp = self._submit_registration(variant)
        
        if resp.has_no_error():
            logger.info(f"{self} has successfully registered for parking with variant {variant}!")
            logger.info(f"Successfully registered with variant {variant} (Unicode: {[ord(c) for c in variant]})")
            return True
        elif resp.is_duplicate_error():
            logger.warning(f"License plate {variant} is already in use, trying next variant")
        else:
            logger.debug(f"Registration failed with variant {variant}: {resp.error}")
            
        return False

    def register_parking(self) -> bool:
        """Register parking spot, optionally using multiple license plate variants.

        If use_magic_plate is enabled, tries multiple plate number variants.
        Otherwise, attempts registration with the original plate number only.

        Returns:
            bool: True if registration was successful, False otherwise
        """
        ## important! Don't register with the same ee_id twice, even though technically possible
        if not self.has_been_deleted():
            logger.warning(f"{self} has not been deleted, cannot register parking")
            return False

        logger.info(f"Starting parking registration for {self} with plate {self.plate_number}")

        success = self._try_magic_variants() if self.use_magic_plate else self._register_without_magic()

        if success:
            self._registration_attempt_failed = False
            log_msg = "using magic plate variant" if self.use_magic_plate else f"with plate {self.plate_number}"
            logger.info(f"Registration successful for {self} {log_msg}")
        else:
            self._registration_attempt_failed = True
            # Restore original plate if magic variants were used
            if self.use_magic_plate:
                self.plate_number = getattr(self, '_original_plate', self.plate_number)
                logger.warning(f"All variants failed, unable to register parking for {self}")
            else:
                logger.warning(f"Registration failed for {self} with plate {self.plate_number}")

        return success

    # ================ Validation Methods ================
    def is_registration_valid(self) -> bool:
        """Check if the parking registration is still valid.
        
        Returns:
            bool: True if registration is still valid, False otherwise
        """
        if not self.queue_info.register_time:
            return False

        time_diff = datetime.now(timezone.utc) - self.queue_info.register_time
        return time_diff < timedelta(hours=ParkerConfig.ServiceThresholds.new_registration_grace_hours)

    def has_been_deleted(self) -> bool:
        """Check if parking entry has been deleted from queue."""
        return self.queue_info.ee_id \
            and self.queue_info.ee_name \
            and self.queue_info.order == 0 \
            and self.queue_info.total == 0

    def is_registration_attempt_failed(self) -> bool:
        """Check if the parking registration has failed."""
        return self._registration_attempt_failed

    def has_queue_position_improved(self) -> bool:
        """Check if queue position has significantly improved.
        
        Returns:
            bool: True if position has improved beyond the benchmark, False otherwise
        """
        if self.queue_info.has_required_fields() and self.initial_order and not self.is_registration_valid():
            improvement = self.initial_order - self.queue_info.order
            is_improved = self.queue_info.order < self.initial_order - ParkerConfig.ServiceThresholds.moving_benchmark

            if is_improved:
                logger.info(f"{self} queue position changed from {self.initial_order} to {self.queue_info.order} (moved up {improvement} positions)")

            else:
                logger.debug(f"{self} queue position changed from {self.initial_order} to {self.queue_info.order} (moved up {improvement} positions)")

            return is_improved

        return False


if __name__ == "__main__":
    from pprint import pprint

    # Example usage
    parker = Parker(
        cookie="JSESSIONID=2ABCD576D836A4E184647F9E52EDB6C8; __VCAP_ID__=603f30e5-9be4-4878-78dc-239b",
        plate_number='沪A01B02',
        use_magic_plate=False,
        emails=['<EMAIL>', '<EMAIL>']  # 列表格式示例
    )
    
    # 或者使用字符串格式
    parker2 = Parker(
        cookie="JSESSIONID=2ABCD576D836A4E184647F9E52EDB6C9; __VCAP_ID__=603f30e5-9be4-4878-78dc-5c4c",
        plate_number='沪B03C04',
        emails='<EMAIL>;<EMAIL>'  # 分隔字符串示例
    )

    pprint(parker.__dict__)
