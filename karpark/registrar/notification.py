#!/usr/bin/env python3
"""
Notification module for the parking system.

This module handles mail notifications for different parking statuses,
including global notifications, admin notifications, and parker-specific notifications.
"""
from enum import auto

import yaml
import time
import traceback
from dataclasses import dataclass
from typing import Dict, List, Set, Tuple, Optional

from karpark.colorlog import logger
from karpark.utils import Singleton
from karpark.utils import AutoNameEnum

from karpark.common.paths import paths
from karpark.common.config import NotificationConfig

from karpark.registrar.parker import Parker
from karpark.registrar.entities import Status
from karpark.mail.template import EmailTemplate
from karpark.mail.email_client import EmailClient


class NotificationEmailFiles(AutoNameEnum):
    """Email address configuration file paths."""
    EMAIL_ADDRESS = auto()

    @property
    def value(self):
        """Get the actual file path at runtime."""
        return paths.email_address_file


class NotificationReceiver(AutoNameEnum):
    """Defines the types of notification recipients."""
    ADMIN = auto()
    PARKER = auto()
    GLOBAL = auto()
    NOBODY = auto()


class NotificationPreset:
    """Centralized configuration for notification rules"""
    # Ordered list of global notification statuses by priority (highest to lowest)
    GLOBAL_PRIORITY_ORDER: List[Status] = [  ## TODO 好像合理了，需要多验证
        # Status.ERROR, # not global
        # Status.REG_SUCCESS, # not global
        # Status.REG_FAILURE, # not global
        # Status.OPEN_STATIC, # very beginning status
        Status.OPEN_DELETED,
        Status.CLOSED_DELETED,
        Status.OPEN_MOVING,
        # If there is a close action, it must be after close; if not, the previous two states are not valid; hence this is always valid prio
        Status.CLOSED_MOVING,
        Status.CLOSED_STATIC,
    ]

    # Defines which statuses can trigger repeated notifications for each receiver type
    # Empty list means no repeatable notifications for that receiver
    REPEATABLE_STATUSES: Dict[NotificationReceiver, List[Status]] = {
        NotificationReceiver.GLOBAL: [],
        NotificationReceiver.PARKER: [Status.ERROR],
        NotificationReceiver.ADMIN: [Status.ERROR]
    }

    # Cooldown period in seconds for repeatable notifications
    COOLDOWN_SECONDS: int = NotificationConfig.Threshold.notification_cooldown

    # Maps each status to its appropriate notification receiver
    # This determines who gets notified when a parker reaches a specific status
    STATUS_RECEIVER_MAP: Dict[Status, NotificationReceiver] = {
        Status.ERROR: NotificationReceiver.PARKER,
        Status.REG_SUCCESS: NotificationReceiver.PARKER,
        Status.REG_FAILURE: NotificationReceiver.PARKER,

        Status.OPEN_STATIC: NotificationReceiver.NOBODY,
        Status.CLOSED_STATIC: NotificationReceiver.GLOBAL,
        Status.OPEN_DELETED: NotificationReceiver.GLOBAL,
        Status.CLOSED_DELETED: NotificationReceiver.GLOBAL,
        Status.OPEN_MOVING: NotificationReceiver.GLOBAL,
        Status.CLOSED_MOVING: NotificationReceiver.GLOBAL
    }


@dataclass
class NotificationTemplate:
    """Email template configuration for notifications."""
    subject: str
    text: str


class NotificationRecord:
    """Container for notification tracking records."""

    def __init__(self):
        self.sent: Set[Status] = set()
        self.repeatable: Dict[Status, float] = {}
        self.parker_sent: Set[Tuple[Status, Parker]] = set()
        self.parker_repeatable: Dict[Tuple[Status, Parker], float] = {}


@Singleton
class Notification:
    """Singleton class for managing mail notifications based on parker status."""

    # Map status to mail templates
    TEMPLATES = {
        Status.ERROR: NotificationTemplate(**EmailTemplate.ERROR),
        Status.CLOSED_STATIC: NotificationTemplate(**EmailTemplate.CLOSED_STATIC),
        Status.CLOSED_MOVING: NotificationTemplate(**EmailTemplate.CLOSED_MOVING),
        Status.OPEN_MOVING: NotificationTemplate(**EmailTemplate.OPEN_MOVING),
        Status.CLOSED_DELETED: NotificationTemplate(**EmailTemplate.CLOSED_DELETED),
        Status.OPEN_DELETED: NotificationTemplate(**EmailTemplate.OPEN_DELETED),
        Status.REG_SUCCESS: NotificationTemplate(**EmailTemplate.REG_SUCCESS),
        Status.REG_FAILURE: NotificationTemplate(**EmailTemplate.REG_FAILURE)
    }

    def __init__(self) -> None:
        """Initialize notification tracker and mail data."""
        logger.info("Initializing notification system")
        self.email_client = EmailClient()

        # Initialize notification records
        self.records = {
            NotificationReceiver.GLOBAL: NotificationRecord(),
            NotificationReceiver.PARKER: NotificationRecord(),
            NotificationReceiver.ADMIN: NotificationRecord()
        }

        # Load mail addresses
        self._load_email_address_data()

        # Load configuration
        self.global_status_priority = NotificationPreset.GLOBAL_PRIORITY_ORDER
        self.repeatable_statuses = NotificationPreset.REPEATABLE_STATUSES

        logger.info("Notification system initialized")

    def _load_email_address_data(self) -> None:
        """Load all mail data from configuration files."""
        logger.info("Loading mail address data from configuration files")
        try:
            with open(NotificationEmailFiles.EMAIL_ADDRESS.value, 'r', encoding='utf-8') as f:
                email_data = yaml.safe_load(f)

            self.global_email_addresses: List[str] = email_data.get('global_emails', [])
            self.admin_email_addresses: List[str] = email_data.get('admin_emails', [])

            logger.info(
                f"Loaded {len(self.global_email_addresses)} global emails and {len(self.admin_email_addresses)} admin emails")
        except Exception as e:
            logger.error(f"Error loading mail data: {e}")
            self.global_email_addresses = []
            self.admin_email_addresses = []

    def _get_recipients(self, receiver_type: NotificationReceiver, parker: Optional[Parker] = None) -> List[str]:
        """Get the list of recipients for a notification."""
        if receiver_type == NotificationReceiver.GLOBAL:
            return self.global_email_addresses
        elif receiver_type == NotificationReceiver.PARKER and parker:
            return parker.emails
        elif receiver_type == NotificationReceiver.ADMIN:
            return self.admin_email_addresses
        return []

    def _should_send_notification(self,
                                  status: Status,
                                  receiver_type: NotificationReceiver,
                                  parker: Optional[Parker] = None) -> bool:
        """Determine if a notification should be sent."""
        record = self.records[receiver_type]
        current_time = time.time()

        if receiver_type == NotificationReceiver.GLOBAL:
            if status in self.global_status_priority:
                priority_index = self.global_status_priority.index(status)
                higher_statuses = self.global_status_priority[
                                  :priority_index]  # Get all statuses before current priority
                if any(s in record.sent for s in higher_statuses):
                    return False
                return status not in record.sent

        if status in self.repeatable_statuses[receiver_type]:
            if receiver_type == NotificationReceiver.PARKER and parker:
                key = (status, parker)
                return (key not in record.parker_repeatable or
                        current_time - record.parker_repeatable[key] > NotificationPreset.COOLDOWN_SECONDS)
            return (status not in record.repeatable or
                    current_time - record.repeatable[status] > NotificationPreset.COOLDOWN_SECONDS)

        if receiver_type == NotificationReceiver.PARKER and parker:
            return (status, parker) not in record.parker_sent

        return status not in record.sent

    def _record_notification(self,
                             status: Status,
                             receiver_type: NotificationReceiver,
                             parker: Optional[Parker] = None) -> None:
        """Record that a notification has been sent."""
        record = self.records[receiver_type]
        current_time = time.time()

        if receiver_type == NotificationReceiver.PARKER and parker:
            if status in self.repeatable_statuses[receiver_type]:
                record.parker_repeatable[(status, parker)] = current_time
            else:
                record.parker_sent.add((status, parker))
        else:
            if status in self.repeatable_statuses[receiver_type]:
                record.repeatable[status] = current_time
            else:
                record.sent.add(status)

    def _send_notification(self,
                           status: Status,
                           receiver_type: NotificationReceiver,
                           parker: Optional[Parker] = None) -> None:
        """Routes notifications to appropriate recipients based on parker's status and configured rules."""
        if not self._should_send_notification(status, receiver_type, parker):
            return

        recipients = self._get_recipients(receiver_type, parker)
        if not recipients:
            logger.warning(f"No recipients found for {receiver_type.value} notification")
            return

        template = self.TEMPLATES[status]
        self.email_client.send_mail(
            to=recipients,
            subject=template.subject,
            text=template.text
        )

        self._record_notification(status, receiver_type, parker)
        logger.info(f"Sent {receiver_type.value} notification for status: {status.value}")

    def send_notification(self, parker: Parker) -> bool:
        """Routes notifications to appropriate recipients based on parker's status and configured rules."""
        try:
            status = parker.current_status
            notification_type = NotificationPreset.STATUS_RECEIVER_MAP.get(status)

            if not notification_type or notification_type == NotificationReceiver.NOBODY:
                logger.debug(f"No notification needed for non-existing {notification_type}")
                return True

            # Type assertion since we know notification_type is not None at this point
            notification_type: NotificationReceiver = notification_type
            self._send_notification(status, notification_type, parker)
            return True

        except Exception as e:
            logger.error(f"Error in send_notification: {e}\n{traceback.format_exc()}")
            return False

    def clear_notification_history(self) -> None:
        """Clear all notification history records."""
        logger.info("Clearing notification history records")
        for record in self.records.values():
            record.sent.clear()
            record.repeatable.clear()
            record.parker_sent.clear()
            record.parker_repeatable.clear()
        logger.info("Notification history cleared")


if __name__ == "__main__":
    # Display all mail addresses from different sources
    notification_ = Notification()

    print("Global Subscriber Emails:")
    for email in notification_.global_email_addresses:
        print(f"  - {email}")

    print("\nAdmin Emails:")
    for email in notification_.admin_email_addresses:
        print(f"  - {email}")

    # Note: Parker emails are now stored directly in Parker objects
