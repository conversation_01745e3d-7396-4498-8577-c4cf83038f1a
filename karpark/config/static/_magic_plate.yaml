# Character mapping is now hardcoded in karpark/registrar/magic_plate.py (CHAR_MAPPING constant)
# This was changed to simplify configuration management and ensure consistency.
# If you need to modify character mappings, edit the CHAR_MAPPING dictionary in magic_plate.py
# But be aware that it may affect restoration of historical data when restoring old magic plate variants.

### Invisible character configuration
# Possible invisible characters, if you add more, you need to update the HISTORICAL_INVISIBLE_CHARS in magic_plate.py
#magic_invisible: "\u200b"  # U+200B ZERO-WIDTH-SPACE MARK
#magic_invisible: "\u200c"  # U+200C ZERO-WIDTH NON-JOINER
#magic_invisible: "\u200d"  # U+200D ZERO-WIDTH JOINER
#magic_invisible: "\u200e"  # U+200E LEFT-TO-RIGHT MARK
#magic_invisible: "\u2060"  # U+2060 WORD JOINER
magic_invisible: "\u200e"  # U+200E LEFT-TO-RIGHT MARK

priority_config:
  - name: invisible_mark
    description: Variants with invisible marks (only for 7-digit plates)
    enabled: true
  - name: single_char_substitute
    description: Single character substitute variants
    enabled: true
  - name: multi_char_substitute
    description: Multiple character substitute combinations
    enabled: true
  - name: single_char_with_mark
    description: Single character substitute with invisible mark (only for 7-digit plates)
    enabled: true
  - name: multi_char_with_mark
    description: Multiple character substitute with invisible mark (only for 7-digit plates)
    enabled: true 