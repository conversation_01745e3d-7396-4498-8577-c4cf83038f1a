logger:
  logger_name: karpark_log
  level: DEBUG
  # Loguru format with color tags: <color>text</color> or <level>text</level>
  # Available colors: black, red, green, yellow, blue, magenta, cyan, white
  # <level> automatically colors based on log level
  # https://loguru.readthedocs.io/en/stable/api/logger.html#color
  # Note that you can use any formatting directives available in Python’s str.format() method (e.g. "{key: >3}" will right-align and pad to a width of 3 characters)
  console_format: "<white>{time:YYYY-MM-DD HH:mm:ss.SSS}</white> | <level>{level:>7}</level> | <cyan>{file:>18}</cyan>:<cyan>{line:>4}</cyan> - <level>{message}</level>"
#  console_format: "<level>{level:>7}</level> | <white>{file:>18}</white>:<cyan>{line:>4}</cyan> - <level>{message}</level>"
  file_format: "{time:YYYY-MM-DD HH:mm:ss.SSS} {file:>18} line:{line:>4} {level:>7} {message}"
  max_bytes: 10485760  # 10MB
  backup_count: 2
  # Loguru specific settings
  colorize: true      # Enable colored console output
  backtrace: true     # Show full traceback on errors
  diagnose: true      # Show variable values in tracebacks