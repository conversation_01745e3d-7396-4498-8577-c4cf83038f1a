# Mitmproxy configuration for Karpark
# This file contains configuration options for the mitmproxy server

# Python environment settings
python:
  # Whether to use system Python instead of virtual environment
  use_system_python: false

# Server settings
server:
  # Port number for the mitmproxy server
  port: 8080

  # Optional password for mitmproxy server authentication
  # Leave empty or null to disable password authentication
  password: null

  # Additional mitmproxy options (future enhancement)
  # mode: "regular"  # regular, transparent, socks5, etc.
  # ssl_insecure: false

# Email monitoring settings for remote restart functionality
email_monitor:
  # Enable email monitoring for restart commands
  enabled: true

  # Check interval in minutes (how often to check for restart emails)
  check_interval: 5

  # Restart command configuration
  restart_command:
    # Password required in email for restart (security measure)
    # If this password appears anywhere in email subject or body, restart will be triggered
    password: "karpark_restart_2025"

    # Restart cooldown period in minutes (prevent frequent restarts)
    cooldown_minutes: 10

    # Email reply settings for cooldown notifications
    reply_settings:
      enabled: true
      subject_template: "Re: {original_subject} - Restart Cooldown Active"
      message_template: |
        Hello,

        Your restart request has been received, but the proxy was recently restarted.
        To prevent system instability, restarts are limited to once every {cooldown_minutes} minutes.

        Last restart: {last_restart_time}
        Next available restart: {next_available_time}

        Please try again after {remaining_minutes} minutes.

        Best regards,
        Karpark Proxy Monitor

  # Email retention settings
  retention:
    # Days to keep processed restart emails in database
    days: 30