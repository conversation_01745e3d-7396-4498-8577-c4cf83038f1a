#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from pathlib import Path
from datetime import date
from functools import wraps

from loguru import logger as loguru_logger

from karpark.common.paths import paths
from karpark.common.config import LoggerConfig


def timeit(func):
    """Decorator for measuring function execution time"""
    @wraps(func)
    def timeit_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        logger.debug(
            f"Execution of the function {func.__name__} took {total_time:.4f} seconds"
        )
        return result
    return timeit_wrapper

class LoguruLoggerSetup:
    """Loguru Logger Setup Class"""

    def __init__(self, logger_name: str = None) -> None:
        # Use configured logger name if not provided
        if logger_name is None:
            logger_name = LoggerConfig.Settings.logger_name

        self.logger_name = logger_name
        self._setup_paths()
        self._setup_loguru()

    def _setup_paths(self) -> None:
        """Setup log paths"""
        self.log_dir = paths.log_dir
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)

        today = date.today()
        log_file = f"{self.logger_name}_{today:%Y_%m_%d}.log"
        self.log_file_path = Path(self.log_dir) / log_file

    def reinitialize(self) -> None:
        """重新初始化logger - 用于测试时重新设置"""
        self._setup_paths()
        self._setup_loguru()

    def _setup_loguru(self) -> None:
        """Setup Loguru logger with console and file handlers"""
        # Remove default handler
        loguru_logger.remove()

        # Get log level
        log_level = LoggerConfig.Settings.level.upper()

        # Add console handler with colors
        import sys

        # Use configured console format, fallback to colorized default if needed
        console_format = LoggerConfig.Settings.console_format

        # If the format doesn't contain color tags, provide a colorized version
        if '<' not in console_format and '>' not in console_format:
            console_format = (
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{file}</cyan>:<cyan>{line}</cyan> - "
                "<level>{message}</level>"
            )

        loguru_logger.add(
            sink=sys.stderr,
            format=console_format,
            level=log_level,
            colorize=getattr(LoggerConfig.Settings, 'colorize', True),
            backtrace=getattr(LoggerConfig.Settings, 'backtrace', True),
            diagnose=getattr(LoggerConfig.Settings, 'diagnose', True)
        )

        # Add file handler with rotation
        loguru_logger.add(
            sink=str(self.log_file_path),
            format=LoggerConfig.Settings.file_format,
            level=log_level,
            rotation=LoggerConfig.Settings.max_bytes,
            retention=LoggerConfig.Settings.backup_count,
            encoding="utf-8",
            backtrace=getattr(LoggerConfig.Settings, 'backtrace', True),
            diagnose=getattr(LoggerConfig.Settings, 'diagnose', True)
        )

# Initialize Loguru logger setup
_logger_setup = LoguruLoggerSetup()

# Create global logger instance that maintains the same interface
logger = loguru_logger

def reinitialize_logger():
    """重新初始化logger - 用于测试环境"""
    global _logger_setup
    _logger_setup.reinitialize()


if __name__ == "__main__":
    # Test code
    def test_logging() -> None:
        test_messages = [
            ("info", "Test info message with Unicode \u6caaA88B88"),
            ("critical", "Test critical error message"),
            ("error", "Test error message"),
            ("warning", "Test warning message"),
            ("debug", "Test debug message"),
        ]

        for level, msg in test_messages:
            getattr(logger, level)(msg)

    test_logging()
